"use server";

import AuthService from "@/common/services/auth.service";
import { AxiosError, HttpStatusCode } from "axios";
import { auth, signIn } from "@/app/(auth)/auth";
import { InvalidLoginError } from "@/app/(auth)/auth";

export interface CreateProviderProfileFormData {
    firstName?: string;
    lastName?: string;
    email?: string;
    telephone?: string;
    companyName?: string;
}

export interface CreateProviderProfileError {
    success: false;
    message: string;
    status: string;
    statusCode: number;
    violations?: Array<{
        field: string;
        message: string;
    }>;
    formData?: CreateProviderProfileFormData;
}

export interface CreateProviderProfileSuccess {
    success: true;
    message: string;
}

export type CreateProviderProfileState =
    | CreateProviderProfileError
    | CreateProviderProfileSuccess
    | undefined;

export interface ReauthenticateUserError {
    success: false;
    message: string;
    status: string;
    statusCode: number;
}

export interface ReauthenticateUserSuccess {
    success: true;
    message: string;
}

export type ReauthenticateUserState =
    | ReauthenticateUserError
    | ReauthenticateUserSuccess
    | undefined;

const service = new AuthService();

export async function createProviderProfileAction(
    state: CreateProviderProfileState,
    formData: FormData,
) {
    const firstName = formData.get("firstName")?.toString();
    const lastName = formData.get("lastName")?.toString();
    const email = formData.get("email")?.toString();
    const telephone = formData.get("telephone")?.toString();
    const companyName = formData.get("companyName")?.toString();

    const formDataForError: CreateProviderProfileFormData = {
        firstName,
        lastName,
        email,
        telephone,
        companyName,
    };

    const data = {
        administratorCreateDto: {
            adminEmail: email,
            firstname: firstName,
            lastname: lastName,
        },
        billingEmail: email,
        email: email,
        name: companyName,
        telephone: telephone,
        userType: "AGENCY" as const,
        agencyType: "TRANSPORTER",
    };
    try {
        await service.signUp(data);
        return {
            success: true,
            message: "Car rental profile created successfully",
        } as CreateProviderProfileSuccess;
    } catch (error) {
        if (error instanceof AxiosError) {
            const errorData = error?.response?.data;
            if (
                errorData?.message?.includes(
                    "Account already registered, try resetting your password",
                )
            ) {
                return {
                    success: false,
                    message: "A provider profile with this email already exists.",
                    status: "EMAIL_EXISTS",
                    statusCode: 409,
                    formData: formDataForError,
                } as CreateProviderProfileError;
            }
            return {
                ...errorData,
                formData: formDataForError,
            } as CreateProviderProfileError;
        }

        return {
            success: false,
            message: "Something went wrong. Please try again.",
            status: "INTERNAL_SERVER_ERROR",
            statusCode: HttpStatusCode.InternalServerError,
            formData: formDataForError,
        } as CreateProviderProfileError;
    }
}

export async function reauthenticateUserAction(
    state: ReauthenticateUserState,
    formData: FormData,
) {
    const username = formData.get("username")?.toString();
    const password = formData.get("password")?.toString();

    if (!username || !password) {
        return {
            success: false,
            message: "Username and password are required.",
            status: "VALIDATION_ERROR",
            statusCode: 400,
        } as ReauthenticateUserError;
    }

    try {
        await signIn("credentials", {
            username,
            password,
            redirect: false,
        });
        await auth();

        return {
            success: true,
            message: "Authentication successful. Redirecting to dashboard...",
        } as ReauthenticateUserSuccess;
    } catch (error) {
        if (error instanceof InvalidLoginError) {
            return {
                success: false,
                message: "Invalid credentials. Please try again.",
                status: "AUTHENTICATION_ERROR",
                statusCode: 401,
            } as ReauthenticateUserError;
        }

        return {
            success: false,
            message: "Something went wrong. Please try again.",
            status: "INTERNAL_SERVER_ERROR",
            statusCode: HttpStatusCode.InternalServerError,
        } as ReauthenticateUserError;
    }
}
