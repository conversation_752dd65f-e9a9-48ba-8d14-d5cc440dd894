"use client"

import { useEffect, useState } from 'react';
import { BrandConfig } from '@/common/config/brands';
import { getCurrentBrandConfig } from '@/common/config/brands/utils';

export function useBrandConfig(): BrandConfig {
    const [brandConfig, setBrandConfig] = useState<BrandConfig>(getCurrentBrandConfig());

    useEffect(() => {
        // Get the brand config
        const config = getCurrentBrandConfig();
        setBrandConfig(config);

        // Set the theme attribute on the root element
        document.documentElement.setAttribute('data-theme', config.theme);
    }, []);

    return brandConfig;
}
