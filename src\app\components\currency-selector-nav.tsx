"use client";

import React, { useState } from "react";
import { Globe, Check, ChevronDown } from "lucide-react";
import { Button } from "@/common/components/ui/button";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/common/components/ui/dropdown-menu";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import { formatCurrency, getCurrencySymbol } from "@/common/lib/currency-utils";
import { useCurrencyDetectionInfo } from "@/common/hooks/use-auto-currency-detection";
import { useCommonSupportedCurrencies } from "@/common/hooks/use-supported-currencies";

// Currency display info mapping
const CURRENCY_DISPLAY_INFO: Record<string, { name: string; symbol: string; flag: string }> = {
  USD: { name: "US Dollar", symbol: "$", flag: "🇺🇸" },
  EUR: { name: "Euro", symbol: "€", flag: "🇪🇺" },
  GBP: { name: "British Pound", symbol: "£", flag: "🇬🇧" },
  CAD: { name: "Canadian Dollar", symbol: "C$", flag: "🇨🇦" },
  AUD: { name: "Australian Dollar", symbol: "A$", flag: "🇦🇺" },
  JPY: { name: "Japanese Yen", symbol: "¥", flag: "🇯🇵" },
  CHF: { name: "Swiss Franc", symbol: "CHF", flag: "🇨🇭" },
  SEK: { name: "Swedish Krona", symbol: "kr", flag: "🇸🇪" },
  NOK: { name: "Norwegian Krone", symbol: "kr", flag: "🇳🇴" },
  DKK: { name: "Danish Krone", symbol: "kr", flag: "🇩🇰" },
  PLN: { name: "Polish Zloty", symbol: "zł", flag: "🇵🇱" },
  CZK: { name: "Czech Koruna", symbol: "Kč", flag: "🇨🇿" },
  HUF: { name: "Hungarian Forint", symbol: "Ft", flag: "🇭🇺" },
  RON: { name: "Romanian Leu", symbol: "lei", flag: "🇷🇴" },
  BGN: { name: "Bulgarian Lev", symbol: "лв", flag: "🇧🇬" },
  HKD: { name: "Hong Kong Dollar", symbol: "HK$", flag: "🇭🇰" },
  SGD: { name: "Singapore Dollar", symbol: "S$", flag: "🇸🇬" },
  KRW: { name: "South Korean Won", symbol: "₩", flag: "🇰🇷" },
  TWD: { name: "Taiwan Dollar", symbol: "NT$", flag: "🇹🇼" },
  MYR: { name: "Malaysian Ringgit", symbol: "RM", flag: "🇲🇾" },
  THB: { name: "Thai Baht", symbol: "฿", flag: "🇹🇭" },
  IDR: { name: "Indonesian Rupiah", symbol: "Rp", flag: "🇮🇩" },
  PHP: { name: "Philippine Peso", symbol: "₱", flag: "🇵🇭" },
  VND: { name: "Vietnamese Dong", symbol: "₫", flag: "🇻🇳" },
  INR: { name: "Indian Rupee", symbol: "₹", flag: "🇮🇳" },
  MXN: { name: "Mexican Peso", symbol: "MX$", flag: "🇲🇽" },
  BRL: { name: "Brazilian Real", symbol: "R$", flag: "🇧🇷" },
  ARS: { name: "Argentine Peso", symbol: "AR$", flag: "🇦🇷" },
  CLP: { name: "Chilean Peso", symbol: "CL$", flag: "🇨🇱" },
  COP: { name: "Colombian Peso", symbol: "CO$", flag: "🇨🇴" },
  PEN: { name: "Peruvian Sol", symbol: "S/", flag: "🇵🇪" },
  AED: { name: "UAE Dirham", symbol: "AED", flag: "🇦🇪" },
  SAR: { name: "Saudi Riyal", symbol: "SR", flag: "🇸🇦" },
  ILS: { name: "Israeli Shekel", symbol: "₪", flag: "🇮🇱" },
  TRY: { name: "Turkish Lira", symbol: "₺", flag: "🇹🇷" },
  ZAR: { name: "South African Rand", symbol: "R", flag: "🇿🇦" },
  EGP: { name: "Egyptian Pound", symbol: "E£", flag: "🇪🇬" },
  NZD: { name: "New Zealand Dollar", symbol: "NZ$", flag: "🇳🇿" },
  CNY: { name: "Chinese Yuan", symbol: "¥", flag: "🇨🇳" },
};

interface CurrencySelectorNavProps {
  className?: string;
  variant?: "compact" | "full";
}

export function CurrencySelectorNav({
  className = "",
  variant = "compact"
}: CurrencySelectorNavProps) {
  const { preferredCurrency, updatePreferredCurrency } = useUserCurrency();
  const { detectionInfo } = useCurrencyDetectionInfo();
  const { currencies: supportedCurrencies, isLoading: currenciesLoading } = useCommonSupportedCurrencies();
  const [isChanging, setIsChanging] = useState(false);

  // Create display currencies from supported currencies
  const displayCurrencies = supportedCurrencies.map(currency => ({
    code: currency.code,
    name: CURRENCY_DISPLAY_INFO[currency.code]?.name || currency.name,
    symbol: CURRENCY_DISPLAY_INFO[currency.code]?.symbol || currency.symbol,
    flag: CURRENCY_DISPLAY_INFO[currency.code]?.flag || "🌍"
  }));

  const currentCurrency = displayCurrencies.find(c => c.code === preferredCurrency) || {
    code: preferredCurrency,
    name: preferredCurrency,
    symbol: getCurrencySymbol(preferredCurrency),
    flag: "🌍"
  };

  const handleCurrencyChange = async (currencyCode: string) => {
    if (currencyCode === preferredCurrency) return;

    setIsChanging(true);
    try {
      await updatePreferredCurrency(currencyCode);
      const newCurrency = displayCurrencies.find(c => c.code === currencyCode);
      toast.info( `Currency Updated. Prices will now be displayed in ${newCurrency?.name || currencyCode}`);
    } catch (error) { 
      toast.error("Failed to update currency preference");
    } finally {
      setIsChanging(false);
    }
  };

  // Show loading state
  if (currenciesLoading) {
    return (
      <Button
        variant="ghost"
        size="sm"
        className={`h-8 px-2 text-sm ${className}`}
        disabled
      >
        <Globe className="h-4 w-4 mr-1" />
        <span className="hidden sm:inline">...</span>
      </Button>
    );
  }

  if (variant === "compact") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 px-2 text-sm ${className}`}
            disabled={isChanging}
          >
            <Globe className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">{currentCurrency.symbol}</span>
            <span className="sm:hidden">{currentCurrency.code}</span>
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Currency
          </DropdownMenuLabel>
          {detectionInfo && !detectionInfo.isUsingDetected && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleCurrencyChange(detectionInfo.detectedCurrency)}
                className="text-blue-600"
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <span>🎯</span>
                    <div>
                      <div className="font-medium">
                        {displayCurrencies.find(c => c.code === detectionInfo.detectedCurrency)?.name || detectionInfo.detectedCurrency}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Detected from your location
                      </div>
                    </div>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-700 px-1 rounded">
                    {detectionInfo.confidence}%
                  </span>
                </div>
              </DropdownMenuItem>
            </>
          )}
          <DropdownMenuSeparator />
          {displayCurrencies.map((currency) => (
            <DropdownMenuItem
              key={currency.code}
              onClick={() => handleCurrencyChange(currency.code)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                <span>{currency.flag}</span>
                <div>
                  <div className="font-medium">{currency.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {formatCurrency(100, currency.code)} example
                  </div>
                </div>
              </div>
              {preferredCurrency === currency.code && (
                <Check className="h-4 w-4 text-green-600" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Full variant for larger displays
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Globe className="h-4 w-4 text-muted-foreground" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3"
            disabled={isChanging}
          >
            <span className="flex items-center gap-2">
              <span>{currentCurrency.flag}</span>
              <span>{currentCurrency.code}</span>
              <span className="text-muted-foreground">({currentCurrency.symbol})</span>
            </span>
            <ChevronDown className="h-3 w-3 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel>Select Currency</DropdownMenuLabel>
          {detectionInfo && !detectionInfo.isUsingDetected && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleCurrencyChange(detectionInfo.detectedCurrency)}
                className="text-blue-600"
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <span>🎯</span>
                    <div>
                      <div className="font-medium">
                        {displayCurrencies.find(c => c.code === detectionInfo.detectedCurrency)?.name || detectionInfo.detectedCurrency}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Detected from your location ({detectionInfo.confidence}% confidence)
                      </div>
                    </div>
                  </div>
                </div>
              </DropdownMenuItem>
            </>
          )}
          <DropdownMenuSeparator />
          <div className="grid grid-cols-1 gap-1">
            {displayCurrencies.map((currency) => (
              <DropdownMenuItem
                key={currency.code}
                onClick={() => handleCurrencyChange(currency.code)}
                className="flex items-center justify-between p-3"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">{currency.flag}</span>
                  <div>
                    <div className="font-medium">{currency.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {currency.code} • {formatCurrency(100, currency.code)} example
                    </div>
                  </div>
                </div>
                {preferredCurrency === currency.code && (
                  <Check className="h-4 w-4 text-green-600" />
                )}
              </DropdownMenuItem>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
