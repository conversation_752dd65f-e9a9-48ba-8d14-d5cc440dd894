"use client";

import { useState, useEffect, useCallback } from "react";
import {
  getUserPreferredCurrency,
  setUserPreferredCurrency,
  isValidCurrencyCode
} from "@/common/lib/currency-utils";
import { detectSupportedUserCurrency } from "@/common/lib/currency-detection";

interface UserPreferences {
  preferredCurrency: string;
}

interface UseUserPreferencesReturn {
  preferences: UserPreferences;
  updatePreferredCurrency: (currencyCode: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook for managing user preferences including preferred currency
 */
export function useUserPreferences(): UseUserPreferencesReturn {
  const [preferences, setPreferences] = useState<UserPreferences>({
    preferredCurrency: "USD",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasAutoDetected, setHasAutoDetected] = useState(false);

  // Load preferences on mount
  useEffect(() => {
    try {
      const preferredCurrency = getUserPreferredCurrency();
      setPreferences({
        preferredCurrency,
      });
    } catch (err) {
      setError("Failed to load user preferences");
      console.error("Error loading user preferences:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Auto-detect currency on first load if no preference is set
  useEffect(() => {
    const autoDetectCurrency = async () => {
      if (hasAutoDetected || preferences.preferredCurrency !== "USD") {
        return; // Skip if already detected or user has a non-default preference
      }

      try {
        setIsLoading(true);
        const detectionResult = await detectSupportedUserCurrency();

        // Only auto-set if detected currency is different from default USD
        if (detectionResult.currency !== "USD" && detectionResult.confidence > 60) {
          setUserPreferredCurrency(detectionResult.currency);
          setPreferences(prev => ({
            ...prev,
            preferredCurrency: detectionResult.currency,
          }));
          console.log(`Auto-detected and set currency: ${detectionResult.currency} (${detectionResult.method})`);
        }
      } catch (error) {
        console.warn("Auto currency detection failed:", error);
      } finally {
        setIsLoading(false);
        setHasAutoDetected(true);
      }
    };

    autoDetectCurrency();
  }, [hasAutoDetected, preferences.preferredCurrency]);

  // Update preferred currency
  const updatePreferredCurrency = useCallback(async (currencyCode: string) => {
    try {
      setError(null);

      // Validate currency code
      if (!isValidCurrencyCode(currencyCode)) {
        throw new Error("Invalid currency code format");
      }

      // Update localStorage
      setUserPreferredCurrency(currencyCode);

      // Update state
      setPreferences(prev => ({
        ...prev,
        preferredCurrency: currencyCode,
      }));

      // TODO: Update backend when user service supports preferred currency
      // await userService.updateUserPreferredCurrency(userId, currencyCode);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update currency preference";
      setError(errorMessage);
      throw err;
    }
  }, []);

  return {
    preferences,
    updatePreferredCurrency,
    isLoading,
    error,
  };
}

/**
 * Hook specifically for currency preference with additional utilities
 */
export function useUserCurrency() {
  const { preferences, updatePreferredCurrency, isLoading, error } = useUserPreferences();
  
  return {
    preferredCurrency: preferences.preferredCurrency,
    updatePreferredCurrency,
    isLoading,
    error,
  };
}
