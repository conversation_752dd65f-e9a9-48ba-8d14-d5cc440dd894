export default function ZimSwitch({
  className,
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      className={className}
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      width="107px"
      height="68px"
      viewBox="0 0 107 68"
      enableBackground="new 0 0 107 68"
      xmlSpace="preserve"
    >
      <g>
        <path
          fill="#FFF406"
          stroke="#9AB4D8"
          strokeWidth="0.5"
          strokeMiterlimit="10"
          d="M12.744,4.603h81.512
		c4.495,0,8.238,3.745,8.238,8.239v42.315c0,4.494-3.743,8.24-8.238,8.24H12.744c-4.495,0-8.238-3.746-8.238-8.24V12.842
		C4.506,8.347,8.249,4.603,12.744,4.603"
        />
        <path
          d="M29.847,31.939v-8.862c0-0.625,0.498-1.123,1.12-1.123c0.625,0,1.125,0.498,1.125,1.123v8.862
		c0,0.622-0.5,1.124-1.125,1.124C30.345,33.063,29.847,32.562,29.847,31.939 M34.839,33.063h-1.625
		c-0.247,0-0.372-0.126-0.247-0.377l3.869-9.484c0.249-0.623,0.874-0.998,1.622-0.998c0.624,0,1.248,0.497,1.498,0.998l2.747,6.74
		l2.748-6.74c0.248-0.623,0.872-0.998,1.622-0.998c0.623,0,1.247,0.497,1.495,0.998l3.869,9.484c0.127,0.251,0,0.377-0.248,0.377
		h-1.624c-0.124,0-0.372-0.126-0.372-0.248l-3.247-7.865l-3.245,7.865c-0.123,0.373-0.498,0.248-0.749,0.248h-0.999
		c-0.123,0-0.375-0.126-0.375-0.248l-3.244-7.865l-3.244,7.865C35.09,32.938,34.964,33.063,34.839,33.063L34.839,33.063z
		 M28.721,43.673l-6.741-6.242c-0.124-0.122-0.622-0.624-0.622-1.495c0-0.623,0.498-1.125,1.123-1.125h8.863
		c0.625,0,1.122,0.502,1.122,1.125c0,0.624-0.497,1.121-1.122,1.121h-6.243l6.741,6.242c0.127,0.126,0.624,0.622,0.624,1.501
		c0,0.623-0.497,1.122-1.122,1.122H22.48c-0.625,0-1.123-0.499-1.123-1.122c0-0.625,0.498-1.127,1.123-1.127H28.721z M34.839,34.811
		h-1.625c-0.247,0-0.372,0.125-0.247,0.375l3.869,9.486c0.249,0.624,0.874,0.999,1.622,0.999c0.624,0,1.248-0.5,1.498-0.999
		l2.747-6.74l2.748,6.74c0.248,0.624,0.872,0.999,1.622,0.999c0.623,0,1.247-0.5,1.495-0.999l3.869-9.486
		c0.127-0.25,0-0.375-0.248-0.375h-1.624c-0.124,0-0.372,0.125-0.372,0.249l-3.247,7.864l-3.245-7.864
		c-0.123-0.372-0.498-0.249-0.749-0.249h-0.999c-0.123,0-0.375,0.125-0.375,0.249l-3.244,7.864L35.09,35.06
		C35.09,34.936,34.964,34.811,34.839,34.811L34.839,34.811z M53.438,44.8v-8.864c0-0.623,0.499-1.125,1.123-1.125
		c0.626,0,1.123,0.502,1.123,1.125V44.8c0,0.623-0.497,1.122-1.123,1.122C53.937,45.794,53.438,45.296,53.438,44.8z M57.557,34.936
		h7.989c0.622,0,1.124,0.499,1.124,1.125c0,0.624-0.502,1.122-1.124,1.122h-2.747c-0.374,0-0.624,0.373-0.624,0.626V44.8
		c0,0.623-0.497,1.122-1.122,1.122c-0.623,0-1.123-0.499-1.123-1.122v-6.991c0-0.378-0.375-0.626-0.626-0.626h-1.872
		c-0.623,0-1.123-0.498-1.123-1.122C56.309,35.435,56.932,34.936,57.557,34.936L57.557,34.936z M73.16,34.936h3.496
		c0.622,0,0.998,0.499,0.998,1c0,0.624-0.5,0.997-0.998,0.997h-4.617c-1.748,0-3.247,1.498-3.247,3.245v0.376
		c0,1.747,1.499,3.245,3.247,3.245h4.617c0.622,0,0.998,0.497,0.998,1.001c0,0.623-0.5,0.994-0.998,0.994H71.91
		c-2.871,0-5.117-2.369-5.117-5.118v-0.623c0-2.87,2.373-5.117,5.117-5.117H73.16z M40.33,33.813l-1.996-4.743l-1.997,4.743
		l1.997,4.745L40.33,33.813z M47.195,28.942L45.2,33.688l1.995,4.743l1.999-4.743L47.195,28.942z M78.652,44.8v-8.864
		c0-0.623,0.499-1,0.999-1c0.624,0,0.997,0.499,0.997,1v2.745c0,0.375,0.376,0.623,0.624,0.623h4.245c0.625,0,1,0.5,1,1
		c0,0.625-0.5,0.998-1,0.998h-4.245c-0.374,0-0.624,0.375-0.624,0.624v2.746c0,0.624-0.498,0.999-0.997,0.999
		C79.151,45.794,78.652,45.423,78.652,44.8z M87.393,44.8v-8.864c0-0.623,0.498-1.125,1.122-1.125c0.623,0,1.123,0.502,1.123,1.125
		V44.8c0,0.623-0.5,1.122-1.123,1.122C87.891,45.794,87.393,45.296,87.393,44.8z M24.728,24.199l-6.741,6.245
		c-0.123,0.121-0.625,0.624-0.625,1.495c0,0.622,0.502,1.124,1.126,1.124h8.862c0.624,0,1.122-0.502,1.122-1.124
		c0-0.624-0.498-1.126-1.122-1.126h-6.241l6.74-6.24c0,0,0.623-0.624,0.623-1.496c0-0.625-0.498-1.123-1.122-1.123h-8.862
		c-0.624,0-1.126,0.498-1.126,1.123s0.502,1.122,1.126,1.122H24.728z"
        />
      </g>
    </svg>
  );
}
