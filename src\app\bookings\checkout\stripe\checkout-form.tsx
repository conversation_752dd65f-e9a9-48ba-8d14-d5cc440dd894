"use client";

import { PaymentElement } from "@stripe/react-stripe-js";
import { StripePaymentElementOptions } from "@stripe/stripe-js";
import { useConfirmPayment } from "@/app/bookings/checkout/stripe/use-confirm-payment";
import { formatUserCurrency } from "@/common/lib/currency-utils";
import { Button } from "@/common/components/ui/button";
import Loader from "@/common/components/loader";
import { CurrencyAwarePrice } from "@/app/components/currency-aware-price";

interface Props {
  payableNow: number;
  bookingId: number;
  originalCurrency?: string; // The agency's base currency
}

export default function CheckoutForm({ payableNow, bookingId, originalCurrency = "USD" }: Props) {
  const { isPending, mutate: confirmPayment, error } = useConfirmPayment();

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault();

    confirmPayment({
      bookingId: bookingId,
    });
  };

  const paymentElementOptions: StripePaymentElementOptions = {
    layout: "accordion",
  };

  return (
    <form id="payment-form" onSubmit={handleSubmit} className="grid gap-4">
      <div className="text-primary flex items-end justify-between text-xl font-semibold md:text-2xl">
        <span>Complete your payment</span>
        <span className="flex flex-col">
          <span className="text-end text-xs font-bold text-gray-500">
            Payable Now
          </span>
          <span className="text-end">
            <CurrencyAwarePrice
              amount={payableNow}
              originalCurrency={originalCurrency}
              showOriginal={false}
              size="lg"
            />
          </span>
        </span>
      </div>
      <PaymentElement id="payment-element" options={paymentElementOptions} />
      <Button disabled={isPending} className="w-full">
        {isPending && <Loader />}
        {isPending ? "Processing payment..." : "Pay Now"}
      </Button>
      {error && (
        <div id="payment-message" className="text-destructive -mt-2 text-sm">
          {error.message}
        </div>
      )}
    </form>
  );
}
