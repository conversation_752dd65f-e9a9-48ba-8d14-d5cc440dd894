import { Metada<PERSON> } from "next";
import { readFile } from "fs/promises";
import { join } from "path";
import { markdownToHtml } from "@/common/lib/markdown";

/* eslint-disable react/no-unescaped-entities */
import Link from "next/link";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/common/components/ui/card";
import { ScrollArea } from "@/common/components/ui/scroll-area";
import { Button } from "@/common/components/ui/button";

export const metadata: Metadata = {
  title:
    "Cancellation and Refund Policy - MyKarLink Zimbabwe | Car Rental Refunds",
  description:
    "Read MyKarLink's comprehensive cancellation and refund policy for car rentals in Zimbabwe. Learn about refund procedures, cancellation fees, and policy terms for vehicle bookings.",
  keywords: [
    "MyKarLink cancellation policy",
    "car rental refund policy Zimbabwe",
    "vehicle rental cancellation",
    "MyKarLink refund procedures",
    "car hire cancellation fees",
    "rental booking cancellation",
    "Zimbabwe car rental refunds",
    "vehicle booking policy",
    "car rental terms Zimbabwe",
    "MyKarLink refund terms",
    "cancellation procedures Zimbabwe",
    "car rental policy",
  ],
  openGraph: {
    title: "Cancellation and Refund Policy - MyKarLink Zimbabwe",
    description:
      "Complete cancellation and refund policy for car rentals in Zimbabwe. Understand refund procedures, cancellation fees, and booking terms.",
    type: "article",
    url: "https://mykarlink.com/cancellation-and-refund-policy",
    siteName: "MyKarLink",
    locale: "en_ZW",
    images: [
      {
        url: "https://mykarlink.com/brands/karlink/images/og-image.png",
        width: 1200,
        height: 630,
        alt: "MyKarLink Cancellation and Refund Policy - Car Rental Terms",
        type: "image/png",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Cancellation and Refund Policy - MyKarLink Zimbabwe",
    description:
      "Comprehensive cancellation and refund policy for car rentals in Zimbabwe. Clear procedures and terms.",
    images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
    creator: "@MyKarLink",
    site: "@MyKarLink",
  },
  alternates: {
    canonical: "https://mykarlink.com/cancellation-and-refund-policy",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default async function CancellationPolicyPage() {
  const markdownPath = join(
    process.cwd(),
    "src/app/cancellation-and-refund-policy/cancellation-and-refund-policy.md",
  );
  const markdown = await readFile(markdownPath, "utf-8");
  const { contentHtml } = await markdownToHtml(markdown);

  // Structured data for Policy page
  const policyJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": "https://mykarlink.com/cancellation-and-refund-policy",
    name: "MyKarLink Cancellation and Refund Policy",
    description:
      "Comprehensive cancellation and refund policy for car rentals and vehicle sharing services in Zimbabwe.",
    url: "https://mykarlink.com/cancellation-and-refund-policy",
    mainEntity: {
      "@type": "Article",
      headline: "Cancellation and Refund Policy",
      description:
        "Detailed policy covering cancellation procedures, refund terms, and conditions for car rental bookings.",
      datePublished: "2024-01-01",
      dateModified: "2024-12-01",
      author: {
        "@type": "Organization",
        name: "MyKarLink",
        url: "https://mykarlink.com",
      },
      publisher: {
        "@type": "Organization",
        name: "MyKarLink",
        logo: {
          "@type": "ImageObject",
          url: "https://mykarlink.com/brands/karlink/images/logo.svg",
        },
      },
      articleSection: "Legal Policy",
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: "https://mykarlink.com",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Cancellation and Refund Policy",
          item: "https://mykarlink.com/cancellation-and-refund-policy",
        },
      ],
    },
  };

  // Structured data for Organization with Policy
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://mykarlink.com/#organization",
    name: "MyKarLink",
    url: "https://mykarlink.com",
    logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
    description: "Car rental and vehicle sharing platform in Zimbabwe",
    hasPolicy: [
      {
        "@type": "CreativeWork",
        name: "Cancellation and Refund Policy",
        url: "https://mykarlink.com/cancellation-and-refund-policy",
        description:
          "Policy governing cancellations, refunds, and booking modifications for car rental services",
        datePublished: "2024-01-01",
        dateModified: "2024-12-01",
      },
    ],
    areaServed: {
      "@type": "Country",
      name: "Zimbabwe",
    },
    serviceType: "Car Rental and Vehicle Sharing",
  };

  // Structured data for Legal Document
  const legalDocumentJsonLd = {
    "@context": "https://schema.org",
    "@type": "DigitalDocument",
    name: "MyKarLink Cancellation and Refund Policy",
    description:
      "Legal document outlining cancellation procedures, refund policies, and terms for car rental services",
    url: "https://mykarlink.com/cancellation-and-refund-policy",
    dateCreated: "2024-01-01",
    dateModified: "2024-12-01",
    creator: {
      "@type": "Organization",
      name: "MyKarLink",
    },
    publisher: {
      "@type": "Organization",
      name: "MyKarLink",
    },
    inLanguage: "en-ZW",
    keywords:
      "cancellation policy, refund policy, car rental terms, vehicle booking policy",
    about: {
      "@type": "Service",
      name: "Car Rental Services",
      serviceType: "Vehicle Rental",
    },
    hasPart: [
      {
        "@type": "WebPageElement",
        name: "PDF Download",
        url: "https://mykarlink.com/documents/cancellation-and-refund-policy.pdf",
      },
    ],
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(policyJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(legalDocumentJsonLd).replace(/</g, "\\u003c"),
        }}
      />

      <main className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <section className="bg-primary pt-32 pb-20 text-white">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-3xl text-center">
              <h1 className="mb-4 text-3xl font-bold tracking-tight sm:text-5xl">
                Cancellation And Refund Policy
              </h1>
              <p className="text-xl text-gray-300">
                Understanding our cancellation and refund procedures
              </p>
            </div>
          </div>
        </section>

        {/* Policy Content */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <Card className="mx-auto max-w-4xl">
              <CardHeader className="flex items-center justify-between md:flex-row">
                <CardTitle className="text-primary text-2xl font-bold">
                  MyKarLink Cancellation And Refund Policy
                </CardTitle>
                <Button asChild>
                  <Link
                    href="/documents/cancellation-and-refund-policy.pdf"
                    target="_blank"
                    download
                  >
                    Download PDF
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                <ScrollArea className="border-muted h-[60vh] w-full rounded-md border p-4">
                  <div className="prose">
                    <article>
                      <div dangerouslySetInnerHTML={{ __html: contentHtml }} />
                    </article>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Help Section */}
        <section className="bg-gray-100 py-16">
          <div className="container mx-auto px-4 text-center">
            <div className="mx-auto max-w-2xl">
              <h2 className="text-primary mb-4 text-2xl font-bold">
                Need Help?
              </h2>
              <p className="mb-8 text-gray-600">
                If you have any questions about our cancellation policy or need
                assistance with a cancellation, our support team is here to
                help.
              </p>
              <Button asChild variant="secondary">
                <Link href="/contact">Contact Support</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}
