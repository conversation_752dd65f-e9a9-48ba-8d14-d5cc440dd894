import axios, { AxiosError, AxiosInstance, AxiosResponse } from "axios";
import { logger } from "@/common/lib/logger";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL + "/karlink-service/api/v1/";

function createAxiosInstance(): AxiosInstance {
    const instance = axios.create({
        baseURL: API_BASE_URL,
    });

    instance.interceptors.request.use(
        (config) => {
            const timestamp = new Date().toISOString();
            config.headers["X-Request-Time"] = timestamp;

            // Add agencyId to every request as a query parameter
            const brandConfig = getCurrentBrandConfig();
            const agencyId = brandConfig.agencyId;
            if (agencyId) {
                config.params = config.params || {};
                config.params.agencyId = agencyId;
            }

            logger.info({
                msg: "[AXIOS] Request",
                timestamp,
                method: config.method?.toUpperCase(),
                url: config.url,
            });

            return config;
        },
        (error) => {
            logger.error({
                msg: "[AXIOS] Request Setup Error",
                timestamp: new Date().toISOString(),
                errorMessage: error.message,
            });
            return Promise.reject(error);
        },
    );

    instance.interceptors.response.use(
        (response: AxiosResponse) => {
            const requestTime = response.config.headers["X-Request-Time"] as string;
            const responseTime = new Date().toISOString();

            logger.info({
                msg: "[AXIOS] Response",
                requestTime,
                responseTime,
                status: response.status,
                url: response.config.url,
            });

            return response;
        },
        (error: AxiosError) => {
            const { response, request, config, message } = error;
            const timestamp = new Date().toISOString();
            const requestTime = config?.headers["X-Request-Time"] as string;

            const errorContext = {
                requestTime,
                responseTime: timestamp,
                url: config?.url,
                status: response?.status,
            };

            if (response) {
                logger.error({
                    ...errorContext,
                    msg: `[AXIOS] Error ${response.status}`,
                    data: response.status === 400 ? response.data : undefined,
                });
            } else if (request) {
                logger.error({
                    ...errorContext,
                    msg: "[AXIOS] No Response",
                    errorMessage: message,
                });
            } else {
                logger.error({
                    msg: "[AXIOS] Request Error",
                    timestamp,
                    errorMessage: message,
                });
            }

            return Promise.reject(error);
        },
    );

    return instance;
}

export const apiClient = createAxiosInstance();
