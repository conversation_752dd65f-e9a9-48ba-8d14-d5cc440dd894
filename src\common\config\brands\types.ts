
export interface Link {
    name: string;
    href: string;
}

export interface SocialLink extends Link {
    icon: React.FC<React.SVGProps<SVGSVGElement>>;
}

export interface ContactInfo {
    phone: string;
    email: string;
    location: string;
    hours: string;
}

export interface PopularLocation {
    name: string;
    image: string;
    slug: string;
}

export interface BrandConfig {
    name: string;
    brandName: string;
    agencyId: string | null | undefined;
    logo: {
        nav: string;
        footer: string;
    };
    theme: string;
    contactInfo: ContactInfo;
    links: {
        primary: Link[];
        important: Link[];
        social?: SocialLink[];
    };
    popularLocations?: PopularLocation[];
    copyright: string;
}
