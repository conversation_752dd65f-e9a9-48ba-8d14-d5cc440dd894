import { useMutation } from "@tanstack/react-query";
import { vehicleBookingService } from "../services/vehicle-booking.service";
import { VehicleBooking } from "@/common/models";
import { ApiError } from "@/common/types/api-errors";

export default function useCreateBookingQuote() {
    return useMutation<
        VehicleBooking,
        ApiError,
        Partial<VehicleBooking>
    >({
        mutationFn: (data: Partial<VehicleBooking>) =>
            vehicleBookingService.createBookingQuote(data),
    });
}
