import { BrandConfig } from "@/common/config/brands/types";
import { socialIcons } from "../social-icons";

const murareConfig: BrandConfig = {
    name: "murare",
    brandName: "<PERSON>rare Car Rental",
    agencyId: process.env.NEXT_PUBLIC_AGENCY_ID,
    logo: {
        nav: "/brands/murare/images/logo.png",
        footer: "/brands/murare/images/logo-footer.png",
    },
    theme: "murare",
    contactInfo: {
        phone: "+263777469369",
        email: "<EMAIL>",
        location: "35 Western Rd, Harare",
        hours: "Open 24/7, everyday",
    },
    links: {
        primary: [
            { name: "Home", href: "/" },
            { name: "About Us", href: "https://murare.co.zw/about.html" },
            { name: "Contact us", href: "https://murare.co.zw/contact.html" },
        ],
        important: [
            { name: "FAQ", href: "https://murare.co.zw/faqs.html" },
            { name: "Blog", href: "https://murare.co.zw/blog.html" },
            { name: "Rental Policy", href: "https://murare.co.zw/policy.html" },
        ],
        social: [
            {
                name: "Facebook",
                href: "https://www.facebook.com/muraraecarrental",
                icon: socialIcons.facebook,
            },
            {
                name: "Instagram",
                href: "https://www.instagram.com/murarecarrentalzimbabwe",
                icon: socialIcons.instagram,
            },
            {
                name: "LinkedIn",
                href: "https://zw.linkedin.com/in/murare-car-rental-910558140",
                icon: socialIcons.linkedin,
            },
            {
                name: "Twitter/X",
                href: "https://twitter.com/murarecarhire",
                icon: socialIcons.twitterx,
            },
            {
                name: "Whatsapp",
                href: "https://api.whatsapp.com/send?phone=263777469369&text=I%27m%20inquiring%20about%20car%20rental%20services",
                icon: socialIcons.whatsapp,
            },
            {
                name: "TikTok",
                href: "https://www.tiktok.com/@murare_carrental",
                icon: socialIcons.tiktok,
            },
        ],
    },
    popularLocations: [
        {
            name: "Harare",
            image: "/images/locations/harare.webp",
            slug: "1",
        },
        {
            name: "Bulawayo",
            image: "/images/locations/bulawayo.jpg",
            slug: "2",
        },
        {
            name: "Mutare",
            image: "/images/locations/mutare.jpg",
            slug: "3",
        },
        {
            name: "Victoria Falls",
            image: "/images/locations/victoria-falls.jpg",
            slug: "9",
        },
        {
            name: "RGM Airport",
            image: "/images/locations/rgm-airport.jpg",
            slug: "26",
        },
        {
            name: "Masvingo",
            image: "/images/locations/masvingo.jpg",
            slug: "4",
        },
    ],
    copyright: "© {year} Murare Car Rental. All rights reserved.",
};

export default murareConfig;
