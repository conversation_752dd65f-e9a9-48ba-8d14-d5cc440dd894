"use server";
import AuthService from "@/common/services/auth.service";
import { AxiosError, HttpStatusCode } from "axios";

import { auth, InvalidLoginError, signIn, signOut } from "@/app/(auth)/auth";

export interface AuthError {
    success: false;
    message: string;
    status: string;
    statusCode: number;
    violations?: Array<{
        field: string;
        message: string;
    }>;
    formData?: SignupFormData | LoginFormData;
}

export interface LoginFormData {
    username?: string;
}

export interface LoginSuccess {
    success: true;
}

export interface LoginFailure {
    success: false;
    formData: LoginFormData;
}

export interface SignupFormData {
    firstName?: string;
    lastName?: string;
    email?: string;
    userType?: "AGENCY" | "CLIENT";
    companyName?: string;
    telephone?: string;
}

export interface SignupSuccess {
    success: true;
    redirect: string;
    message: string;
}

export type FormState =
    | AuthError
    | LoginSuccess
    | LoginFailure
    | SignupSuccess
    | undefined;

const service = new AuthService();

export async function loginAction(state: FormState, form: FormData) {
    const username = form.get("username")?.toString() || "";

    try {
        await signIn("credentials", {
            username,
            password: form.get("password"),
            redirect: false,
        });
        await auth();
        return { success: true } as LoginSuccess;
    } catch (error) {
        if (error instanceof InvalidLoginError) {
            return {
                success: false,
                formData: { username }
            } as LoginFailure;
        }
        throw error;
    }
}

export async function signupAction(state: FormState, formData: FormData) {
    const firstName = formData.get("firstName")?.toString();
    const lastName = formData.get("lastName")?.toString();
    const email = formData.get("email")?.toString();
    const userType = formData.get("userType")?.toString() as "AGENCY" | "CLIENT";
    const companyName = formData.get("companyName")?.toString();
    const telephone = formData.get("telephone")?.toString();

    const formDataForError: SignupFormData = {
        firstName,
        lastName,
        email,
        userType,
        companyName,
        telephone
    };

    const name =
        userType === "AGENCY"
            ? companyName
            : `${firstName} ${lastName}`;

    const data = {
        administratorCreateDto: {
            adminEmail: email,
            firstname: firstName,
            lastname: lastName,
        },
        billingEmail: email,
        email: email,
        name: name,
        telephone: telephone,
        userType: userType,
        agencyType: userType === "AGENCY" ? "TRANSPORTER" : undefined,
    };

    try {
        await service.signUp(data);
        return {
            success: true,
            redirect: '/login',
            message: 'Account created successfully. Please check your email for login credentials.'
        } as SignupSuccess;
    } catch (error) {
        if (error instanceof AxiosError) {
            const errorData = error?.response?.data;
            if (errorData?.message?.includes('email already exists')) {
                return {
                    success: false,
                    message: 'Email already exists. Try logging in instead.',
                    status: 'EMAIL_EXISTS',
                    statusCode: 409,
                    formData: formDataForError
                } as AuthError;
            }
            return { ...errorData, formData: formDataForError } as AuthError;
        }

        return {
            message: "Something went wrong. Please try again.",
            status: "INTERNAL_SERVER_ERROR",
            statusCode: HttpStatusCode.InternalServerError,
            formData: formDataForError
        } as AuthError;
    }
}

export async function forgotPasswordAction(
    state: FormState,
    formData: FormData,
) {
    const email = formData.get("email")?.toString();

    try {
        const data = await service.forgotPassword(email);
        return data;
    } catch (error) {
        if (error instanceof AxiosError) {
            return error?.response?.data as AuthError;
        }

        return {
            message: "Something went wrong. Please try again.",
            status: "INTERNAL_SERVER_ERROR",
            statusCode: HttpStatusCode.InternalServerError,
        } as AuthError;
    }
}

export async function logoutAction() {
    await signOut({ redirect: false });
    return { success: true } as { success: boolean };
}

export async function changePasswordAction(
    state: FormState,
    formData: FormData,
) {
    const password = formData.get("password")!.toString();
    const token = formData.get("token")!.toString();

    try {
        await service.changePassword(token, password);
        return {
            success: true,
            redirect: '/login',
            message: 'Password reset successfully. You can now login with your new password.'
        };
    } catch (error) {
        if (error instanceof AxiosError) {
            return error?.response?.data as AuthError;
        }

        return {
            message: "Something went wrong. Please try again.",
            status: "INTERNAL_SERVER_ERROR",
            statusCode: HttpStatusCode.InternalServerError,
        } as AuthError;
    }
}
