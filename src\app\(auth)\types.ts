export type Permission = {
    id: number;
    authority: string;
    description: string;
  };
  
  export type Role = {
    id: number;
    name: string;
    permissions: Permission[];
  };
  
  export type User = {
    userType: string;
    agentId: number;
    clientId: number | null;
    workerId: number;
    roles: Role[];
    id: number;
    firstName: string;
    lastName: string;
    access_token: string;
    token_type: string;
    refresh_token: string;
    scope: string;
    expires_in: string;
  };
  