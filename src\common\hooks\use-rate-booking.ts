import { useMutation } from "@tanstack/react-query";
import {
  CreateRatingInput,
  vehicleBookingService,
} from "@/common/services/vehicle-booking.service";
import { useRouter } from "next/navigation";

export default function useRateBooking() {
  const router = useRouter();
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: CreateRatingInput }) => {
      return vehicleBookingService.rate(id, data);
    },
    onSuccess: (data, variables) => {
      router.push(`/bookings/${variables.id}/rate/success`);
    },
  });
}
