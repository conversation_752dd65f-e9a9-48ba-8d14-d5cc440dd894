"use client";

import React, {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  parsePhoneNumberWithError,
  AsYouType,
  type CountryCode,
  type PhoneNumber,
} from "libphonenumber-js";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/common/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/common/components/ui/command";
import { Check, ChevronDown } from "lucide-react";
import { cn } from "@/common/lib/shadcn-utils";
import { useCountries } from "@/common/lib/country_utils";
import { useAutoCountryDetection, saveUserCountryPreference } from "@/common/hooks/use-auto-country-detection";
import { Loader2 } from "lucide-react";

export interface PhoneValidationError {
  type: "invalid" | "incomplete" | "too_short" | "too_long" | "invalid_country";
  message: string;
}

export interface PhoneInputProps {
  value?: string;
  defaultValue?: string;
  onChange?: (value: string, isValid: boolean, phoneNumber?: PhoneNumber | null) => void;
  onValidationChange?: (isValid: boolean, error?: string) => void;
  onCountryChange?: (country: CountryCode) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  inputClassName?: string;
  buttonClassName?: string;
  defaultCountry?: CountryCode;
  error?: boolean;
  enableAutoDetection?: boolean;
  enableIpDetection?: boolean;
  enableTimezoneDetection?: boolean;
  enableLanguageDetection?: boolean;
  showDetectionStatus?: boolean;
}

export interface PhoneInputRef {
  focus: () => void;
  blur: () => void;
  getValue: () => string;
  getFormattedValue: () => string;
  getInternationalValue: () => string;
  isValid: () => boolean;
  getValidationError: () => string | null;
  validate: () => boolean;
}

const PhoneInput = forwardRef<PhoneInputRef, PhoneInputProps>(
  (
    {
      value,
      defaultValue,
      onChange,
      onValidationChange,
      onCountryChange,
      placeholder = "Enter phone number",
      disabled = false,
      className,
      inputClassName,
      buttonClassName,
      defaultCountry = "ZW", // Zimbabwe as default
      error = false,
      enableAutoDetection = true,
      enableIpDetection = true,
      enableTimezoneDetection = true,
      enableLanguageDetection = true,
      showDetectionStatus = false,
    },
    ref,
  ) => {
    const [internalValue, setInternalValue] = useState(defaultValue || "");
    const [selectedCountry, setSelectedCountry] =
      useState<CountryCode>(defaultCountry);
    const [isCountryOpen, setIsCountryOpen] = useState(false);
    const [inputRef, setInputRef] = useState<HTMLInputElement | null>(null);
    const [, setIsFocused] = useState(false);
    const [userSelectedCountry, setUserSelectedCountry] = useState(false);

    const countries = useCountries();

    const autoDetection = useAutoCountryDetection({
      enableIpDetection: enableAutoDetection && enableIpDetection,
      enableTimezoneDetection: enableAutoDetection && enableTimezoneDetection,
      enableLanguageDetection: enableAutoDetection && enableLanguageDetection,
      fallbackCountry: defaultCountry,
    });

    useEffect(() => {
      if (
        enableAutoDetection &&
        !autoDetection.isLoading &&
        autoDetection.detectedCountry &&
        autoDetection.detectedCountry !== selectedCountry &&
        !value && // Only auto-detect if no value is set
        !defaultValue && // Only auto-detect if no default value is set
        !userSelectedCountry // Don't override if user has manually selected a country
      ) {
        setSelectedCountry(autoDetection.detectedCountry);
        onCountryChange?.(autoDetection.detectedCountry);
      }
    }, [
      enableAutoDetection,
      autoDetection.isLoading,
      autoDetection.detectedCountry,
      selectedCountry,
      value,
      defaultValue,
      userSelectedCountry,
      onCountryChange,
    ]);

    // Determine if this is a controlled component
    const isControlled = value !== undefined;
    const currentValue = isControlled ? value : internalValue;

    // Parse and validate the current phone number
    const phoneData = useMemo(() => {
      if (!currentValue) {
        return {
          isValid: false,
          error: null,
          phoneNumber: null,
          formattedValue: "",
        };
      }

      try {
        let phoneNumber = parsePhoneNumberWithError(
          currentValue,
          selectedCountry,
        );

        if (!phoneNumber) {
          phoneNumber = parsePhoneNumberWithError(currentValue);
        }

        if (phoneNumber) {
          const isValid = phoneNumber.isValid();
          let error: PhoneValidationError | null = null;

          if (!isValid) {
            if (!phoneNumber.isPossible()) {
              error = {
                type: "invalid",
                message: "Phone number is not possible for selected country"
              };
            } else if (phoneNumber.nationalNumber.length < 3) {
              error = {
                type: "too_short",
                message: "Phone number is too short"
              };
            } else if (phoneNumber.nationalNumber.length > 15) {
              error = {
                type: "too_long",
                message: "Phone number is too long"
              };
            } else {
              error = {
                type: "invalid",
                message: "Invalid phone number format for selected country"
              };
            }
          }

          return {
            isValid,
            error,
            phoneNumber,
            formattedValue: phoneNumber.formatInternational(),
          };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown error";

        if (errorMessage.includes("INVALID_COUNTRY")) {
          return {
            isValid: false,
            error: {
              type: "invalid_country",
              message: "Invalid country code"
            },
            phoneNumber: null,
            formattedValue: currentValue,
          };
        }

        const asYouType = new AsYouType(selectedCountry);
        const formattedValue = asYouType.input(currentValue);

        return {
          isValid: false,
          error: {
            type: "incomplete",
            message: "Incomplete phone number"
          },
          phoneNumber: null,
          formattedValue,
        };
      }

      return {
        isValid: false,
        error: {
          type: "invalid",
          message: "Invalid phone number"
        },
        phoneNumber: null,
        formattedValue: currentValue,
      };
    }, [currentValue, selectedCountry]);

    // Update country when phone number changes (for international numbers)
    // but only if the user hasn't manually selected a country
    useEffect(() => {
      if (
        phoneData.phoneNumber &&
        phoneData.phoneNumber.country &&
        !userSelectedCountry // Only update if user hasn't manually selected
      ) {
        setSelectedCountry(phoneData.phoneNumber.country);
      }
    }, [phoneData.phoneNumber, userSelectedCountry]);

    // Remove the automatic validation change notification that causes infinite loops
    // Validation state is now handled manually in the onChange callback

    // Handle input changes
    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;

        if (!isControlled) {
          setInternalValue(inputValue);
        }

        let internationalValue = inputValue;
        let isValidNumber = false;
        let parsedPhoneNumber = null;
        let validationError: string | undefined = "Invalid phone number";

        try {
          let phoneNumber = parsePhoneNumberWithError(
            inputValue,
            selectedCountry,
          );

          if (!phoneNumber) {
            phoneNumber = parsePhoneNumberWithError(inputValue);
          }

          if (phoneNumber) {
            isValidNumber = phoneNumber.isValid();
            parsedPhoneNumber = phoneNumber;

            if (isValidNumber) {
              internationalValue = phoneNumber.format("E.164");
              validationError = undefined;
            } else {
              if (!phoneNumber.isPossible()) {
                validationError = "Phone number is not possible for selected country";
              } else if (phoneNumber.nationalNumber.length < 3) {
                validationError = "Phone number is too short";
              } else if (phoneNumber.nationalNumber.length > 15) {
                validationError = "Phone number is too long";
              } else {
                validationError = "Invalid phone number format for selected country";
              }
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : "Unknown error";

          if (errorMessage.includes("INVALID_COUNTRY")) {
            validationError = "Invalid country code";
          } else {
            validationError = "Invalid phone number format";
          }

          isValidNumber = false;
        }

        onChange?.(internationalValue, isValidNumber, parsedPhoneNumber);
        onValidationChange?.(isValidNumber, validationError);
      },
      [isControlled, selectedCountry, onChange, onValidationChange],
    );

    // Handle country selection
    const handleCountrySelect = useCallback(
      (countryCode: CountryCode) => {
        setSelectedCountry(countryCode);
        setUserSelectedCountry(true);
        setIsCountryOpen(false);

        // Save user preference for future sessions
        saveUserCountryPreference(countryCode);
        onCountryChange?.(countryCode);

        if (currentValue) {
          try {
            const phoneNumber = parsePhoneNumberWithError(
              currentValue,
              countryCode,
            );
            if (phoneNumber) {
              const newValue = phoneNumber.format("E.164");
              if (!isControlled) {
                setInternalValue(newValue);
              }

              const isValid = phoneNumber.isValid();
              let validationError: string | undefined;

              if (!isValid) {
                if (!phoneNumber.isPossible()) {
                  validationError = "Phone number is not possible for selected country";
                } else if (phoneNumber.nationalNumber.length < 3) {
                  validationError = "Phone number is too short";
                } else if (phoneNumber.nationalNumber.length > 15) {
                  validationError = "Phone number is too long";
                } else {
                  validationError = "Invalid phone number format for selected country";
                }
              }

              onChange?.(newValue, isValid, phoneNumber);
              onValidationChange?.(isValid, validationError);
            } else {
              onChange?.(currentValue, false, null);
              onValidationChange?.(false, "Invalid phone number for selected country");
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Unknown error";
            const validationError = errorMessage.includes("INVALID_COUNTRY")
              ? "Invalid country code"
              : "Invalid phone number format";

            onChange?.(currentValue, false, null);
            onValidationChange?.(false, validationError);
          }
        }
      },
      [currentValue, isControlled, onChange, onValidationChange, onCountryChange],
    );

    // Expose methods via ref
    React.useImperativeHandle(
      ref,
      () => ({
        focus: () => inputRef?.focus(),
        blur: () => inputRef?.blur(),
        getValue: () => currentValue,
        getFormattedValue: () => phoneData.formattedValue,
        getInternationalValue: () => {
          if (phoneData.phoneNumber && phoneData.isValid) {
            return phoneData.phoneNumber.format("E.164");
          }
          return currentValue;
        },
        isValid: () => phoneData.isValid,
        getValidationError: () => phoneData.error?.message || null,
        validate: () => phoneData.isValid,
      }),
      [currentValue, phoneData, inputRef],
    );

    // Get selected country data
    const selectedCountryData =
      countries.find((c) => c.code === selectedCountry) ||
      countries.find((c) => c.code === "ZW") ||
      countries[0];

    // Use countries in alphabetical order (already sorted in country_utils)
    const sortedCountries = countries;

    const handlePaste = useCallback((e: React.ClipboardEvent) => {
      const pastedText = e.clipboardData.getData("text");
      try {
        const phoneNumber = parsePhoneNumberWithError(pastedText);
        if (phoneNumber && phoneNumber.country) {
          setSelectedCountry(phoneNumber.country);
        }
      } catch {
        // Handle paste error
      }
    }, []);

    const handleFocus = useCallback(() => {
      setIsFocused(true);
    }, []);

    const handleBlur = useCallback(() => {
      setIsFocused(false);
    }, []);

    // Determine if we should show error styling
    // Show error if: external error prop is true OR internal validation failed
    const hasError = error || (currentValue && !phoneData.isValid);



    // Detection status for debugging/development
    const detectionStatus = showDetectionStatus && autoDetection ? (
      <div className="text-xs text-muted-foreground mt-1">
        Detection: {autoDetection.method}
        {autoDetection.confidence > 0 && ` (${autoDetection.confidence}% confidence)`}
        {autoDetection.isLoading && <Loader2 className="inline ml-1 h-3 w-3 animate-spin" />}
        {!autoDetection.isAccurateDetectionComplete && autoDetection.fastDetection && (
          <span className="ml-1">(verifying...)</span>
        )}
      </div>
    ) : null;

    return (
      <div className="space-y-1">{/* Wrapper for input and status */}
      <div
        className={cn(
          "border-input ring-offset-background flex rounded-md border bg-transparent text-sm shadow-xs",
          "focus-within:border-ring focus-within:ring-[3px]",
          hasError && "border-destructive",
          hasError
            ? "focus-within:ring-destructive"
            : "focus-within:ring-ring/50",
          disabled && "cursor-not-allowed opacity-50",
          className,
        )}
      >
        <Popover open={isCountryOpen} onOpenChange={setIsCountryOpen}>
          <PopoverTrigger asChild className={cn("px-3 py-1", buttonClassName)}>
            <Button
              variant="ghost"
              role="combobox"
              aria-expanded={isCountryOpen}
              aria-label="Select country code"
              className="border-input h-auto w-32 justify-between rounded-none rounded-l-md border-0 border-r hover:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
              disabled={disabled}
            >
              <span className="flex items-center gap-2">
                <span className="text-lg">{selectedCountryData.flag}</span>
                <span className="text-muted-foreground text-sm">{`+${selectedCountryData.dialCode}`}</span>
              </span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0">
            <Command>
              <CommandInput placeholder="Search country..." />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  {sortedCountries.map((country) => (
                    <CommandItem
                      key={country.code}
                      value={`${country.name} ${country.code} +${country.dialCode}`}
                      onSelect={() => handleCountrySelect(country.code)}
                    >
                      <span className="mr-2 text-lg">{country.flag}</span>
                      <span className="flex-1">{country.name}</span>
                      <span className="text-muted-foreground text-sm">{`+${country.dialCode}`}</span>
                      <Check
                        className={cn(
                          "ml-2 h-4 w-4",
                          selectedCountry === country.code
                            ? "opacity-100"
                            : "opacity-0",
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        <input
          ref={setInputRef}
          type="tel"
          value={currentValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onPaste={handlePaste}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "placeholder:text-muted-foreground w-full min-w-0 flex-1 rounded-none rounded-r-md border-0 bg-transparent px-3 py-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
            inputClassName,
          )}
        />
      </div>

      {/* Detection status */}
      {detectionStatus}

    </div>
    );
  },
);

PhoneInput.displayName = "PhoneInput";

export { PhoneInput };
