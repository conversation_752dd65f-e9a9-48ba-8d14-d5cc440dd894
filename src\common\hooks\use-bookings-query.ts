import { useQueryStates } from "nuqs";
import { bookingsQueryConfig, bookkingsQuerySerializer } from "@/common/config/bookings-query-config";
import { BookingsQueryActions, BookingsQueryParams } from "@/common/types/bookings-query";

export default function useBookingsQuery() {
    const [query, setQuery] = useQueryStates(bookingsQueryConfig, {
        clearOnDefault: false,
    });

    const state: BookingsQueryParams = {
        page: query.page,
        size: query.size,
        searchCriteria: query.searchCriteria,
        statuses: query.statuses,
    };

    const actions: BookingsQueryActions = {
        updatePage: (page) => setQuery({ page }),
        updateSize: (size) => setQuery({ size }),
        updateSearchCriteria: (criteria) => setQuery({ searchCriteria: criteria, page: 0 }),
        updateStatuses: (statuses) => setQuery({ statuses, page: 0 }),
        nextPage: () => setQuery({ page: query.page + 1 }),
        previousPage: () => setQuery({ page: query.page - 1 }),
        firstPage: () => setQuery({ page: 0 }),
        lastPage: (totalItems) => {
            const lastPage = Math.max(0, Math.ceil(totalItems / query.size) - 1);
            setQuery({ page: lastPage });
        },
    }

    const queryString = bookkingsQuerySerializer(query);

    return {
        state,
        ...actions,
        queryString,
    };
}
