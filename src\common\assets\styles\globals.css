@import "tailwindcss";
@plugin "@tailwindcss/typography";
@custom-variant dark (&:where(.dark, .dark *));

/* Default: MyKarLink Theme */
:root {
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.26 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.26 0 0);
  --popover: oklch(0.99 0 0);
  --popover-foreground: oklch(0.26 0 0);
  --primary: oklch(0.27 0.1 258.5);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.7 0.19 47.6);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.44 0 0);
  --accent: oklch(0.94 0 0);
  --accent-foreground: oklch(0.26 0 0);
  --destructive: oklch(0.63 0.19 23.03);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.92 0 0);
  --input: oklch(0.94 0 0);
  --ring: oklch(0.26 0 0);

  /* Toast notification colors */
  --success: oklch(0.65 0.2 142.5);
  --success-foreground: oklch(1 0 0);
  --info: oklch(0.65 0.2 230);
  --info-foreground: oklch(1 0 0);
  --warning: oklch(0.75 0.18 80);
  --warning-foreground: oklch(0.2 0 0);

  --chart-1: oklch(0.81 0.17 75.35);
  --chart-2: oklch(0.55 0.22 264.53);
  --chart-3: oklch(0.72 0 0);
  --chart-4: oklch(0.92 0 0);
  --chart-5: oklch(0.56 0 0);
  --sidebar: oklch(0.99 0 0);
  --sidebar-foreground: oklch(0.26 0 0);
  --sidebar-primary: oklch(0.26 0 0);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.94 0 0);
  --sidebar-accent-foreground: oklch(0.26 0 0);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0.26 0 0);
  --font-sans: var(--font-inter);
  --radius: 0.5rem;
  --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
  --shadow:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
  --shadow-md:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18);
  --shadow-lg:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18);
  --shadow-xl:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);
  --tracking-normal: 0.025em;

  --nav-foreground: var(--secondary);
}

/* Murare Car Rental Theme */
:root[data-theme="murare"] {
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.26 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.26 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.26 0 0);
  --primary: oklch(0.46 0.11 242.6);
  --primary-foreground: oklch(1 0 0);
  --secondary-foreground: oklch(1 0 0);
  --secondary: oklch(0.63 0.14 136.44);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.44 0 0);
  --accent: oklch(0.94 0 0);
  --accent-foreground: oklch(0.26 0 0);
  --destructive: oklch(0.63 0.19 23.03);
  --destructive-foreground: oklch(1 0 0);

  /* Toast notification colors */
  --success: oklch(0.6 0.18 142.5);
  --success-foreground: oklch(1 0 0);
  --info: oklch(0.6 0.18 230);
  --info-foreground: oklch(1 0 0);
  --warning: oklch(0.7 0.18 80);
  --warning-foreground: oklch(0.2 0 0);

  --border: oklch(0.92 0 0);
  --input: oklch(0.94 0 0);
  --ring: oklch(0 0 0);
  --chart-1: oklch(0.81 0.17 75.35);
  --chart-2: oklch(0.55 0.22 264.53);
  --chart-3: oklch(0.72 0 0);
  --chart-4: oklch(0.92 0 0);
  --chart-5: oklch(0.56 0 0);
  --sidebar: oklch(0.99 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0 0 0);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.94 0 0);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.94 0 0);
  --sidebar-ring: oklch(0 0 0);
  --font-sans: var(--font-rubik);
  /* --font-serif: Georgia, serif;
  --font-mono: Geist Mono, monospace; */
  --radius: 0.7rem;
  --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);
  --shadow-sm:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
  --shadow:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);
  --shadow-md:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18);
  --shadow-lg:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18);
  --shadow-xl:
    0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);

  --nav-foreground: var(--primary);
}

/* KhenAuto Car Rental Theme */
:root[data-theme="khenauto"] {
  --background: oklch(0.9911 0 0);
  --foreground: oklch(0.2603 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2603 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2603 0 0);
  --primary: oklch(0.4876 0.1653 142.3445);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.7752 0.1452 87.4823);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.9702 0 0);
  --muted-foreground: oklch(0.4386 0 0);
  --accent: oklch(0.9702 0 0);
  --accent-foreground: oklch(0.2603 0 0);
  --destructive: oklch(0.6357 0.2139 24.4166);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9189 0 0);
  --input: oklch(0.9401 0 0);
  --ring: oklch(0.2603 0 0);
  --chart-1: oklch(0.8089 0.1698 75.2054);
  --chart-2: oklch(0.5465 0.2489 263.9526);
  --chart-3: oklch(0.7187 0 0);
  --chart-4: oklch(0.9189 0 0);
  --chart-5: oklch(0.559 0 0);
  --sidebar: oklch(0.9911 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0 0 0);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9401 0 0);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.9401 0 0);
  --sidebar-ring: oklch(0 0 0);
  --font-sans: Inter, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0.4rem;
  --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 10.1961% / 0.1);
  --shadow-xs: 0px 1px 2px 0px hsl(0 0% 10.1961% / 0.1);
  --shadow-sm:
    0px 1px 2px 0px hsl(0 0% 10.1961% / 0.19),
    0px 1px 2px -1px hsl(0 0% 10.1961% / 0.19);
  --shadow:
    0px 1px 2px 0px hsl(0 0% 10.1961% / 0.19),
    0px 1px 2px -1px hsl(0 0% 10.1961% / 0.19);
  --shadow-md:
    0px 1px 2px 0px hsl(0 0% 10.1961% / 0.19),
    0px 2px 4px -1px hsl(0 0% 10.1961% / 0.19);
  --shadow-lg:
    0px 1px 2px 0px hsl(0 0% 10.1961% / 0.19),
    0px 4px 6px -1px hsl(0 0% 10.1961% / 0.19);
  --shadow-xl:
    0px 1px 2px 0px hsl(0 0% 10.1961% / 0.19),
    0px 8px 10px -1px hsl(0 0% 10.1961% / 0.19);
  --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 10.1961% / 0.47);

  /* Toast notification colors */
  --success: oklch(0.65 0.2 142.5);
  --success-foreground: oklch(1 0 0);
  --info: oklch(0.65 0.2 230);
  --info-foreground: oklch(1 0 0);
  --warning: oklch(0.75 0.18 80);
  --warning-foreground: oklch(0.2 0 0);
}

@theme inline {
  --background-image-hero: url("/images/hero-lg.webp");
  --background-image-top-rated: url("/images/top-rated-cars-bg.jpg");
  --background-image-car-brands: url("/images/car-brands-bg.png");

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  /* Custom Variables for Whitebranding */
  --color-nav-foreground: var(--nav-foreground);
  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility container {
  max-width: 1440px;
  margin-inline: auto;
  padding-inline: 1rem;

  @media (min-width: 640px) {
    & {
      padding-left: 1.5rem /* 24px */;
      padding-right: 1.5rem /* 24px */;
    }
  }

  @media (min-width: 1024px) {
    & {
      padding-left: 2rem /* 32px */;
      padding-right: 2rem /* 32px */;
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/*
  ---break---
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
