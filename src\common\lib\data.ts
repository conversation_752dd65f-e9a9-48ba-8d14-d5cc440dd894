import { DivideIcon as LucideIcon, Bed, Home, Building2, Warehouse, Tent, Mountain, Trees, Palmtree, Building, Hotel, UtensilsCrossed, Waves, Coffee } from 'lucide-react';

export type CategoryType = {
  id: string;
  label: string;
  icon: LucideIcon;
};

export const categories: CategoryType[] = [
  { id: 'rooms', label: 'Rooms', icon: Bed },
  { id: 'homes', label: 'Homes', icon: Home },
  { id: 'mansions', label: 'Mansions', icon: Building2 },
  { id: 'cabins', label: 'Cabins', icon: Warehouse },
  { id: 'camping', label: 'Camping', icon: Tent },
  { id: 'caves', label: 'Caves', icon: Mountain },
  { id: 'countryside', label: 'Countryside', icon: Trees },
  { id: 'tropical', label: 'Tropical', icon: Palmtree },
  { id: 'apartments', label: 'Apartments', icon: Building },
  { id: 'bnb', label: 'Bed & breakfasts', icon: Hotel },
  { id: 'dining', label: 'Amazing dining', icon: UtensilsCrossed },
  { id: 'lakefront', label: 'Lakefront', icon: Mountain },
  { id: 'beach', label: 'Beach', icon: Waves },
  { id: 'cafes', label: 'Cafes nearby', icon: Coffee },
];

export type FilterType = {
  priceRange: [number, number];
  bedrooms: number;
  beds: number;
  bathrooms: number;
  propertyType: string[];
  amenities: string[];
};

export const propertyTypes = [
  'Entire home',
  'Private room',
  'Shared room',
  'Hotel room',
];

export const amenities = [
  'Wifi',
  'Kitchen',
  'Washer',
  'Dryer',
  'Air conditioning',
  'Heating',
  'Pool',
  'Hot tub',
  'Free parking',
  'EV charger',
  'Gym',
  'BBQ grill',
  'Fire pit',
  'Indoor fireplace',
  'Pool table',
];