"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { type CountryCode } from "libphonenumber-js";
import {
    detectCountryFromTimezone,
    detectCountryFromLanguage,
    detectCountryFromIP,
    detectCountryFromIPFallback,
} from "@/common/lib/country_utils";

export interface CountryDetectionMethod {
    country: CountryCode;
    method: "timezone" | "language" | "ip" | "fallback";
    confidence: number; // 0-100
    timestamp: number;
}

export interface CountryDetectionResult {
    detectedCountry: CountryCode | null;
    isLoading: boolean;
    method: "timezone" | "language" | "ip" | "fallback" | "cached" | null;
    error: string | null;
    confidence: number;
    // Two-phase detection results
    fastDetection: CountryDetectionMethod | null;
    accurateDetection: CountryDetectionMethod | null;
    isAccurateDetectionComplete: boolean;
}

export interface UseAutoCountryDetectionOptions {
    enableIpDetection?: boolean;
    fallbackCountry?: CountryCode;
    enableTimezoneDetection?: boolean;
    enableLanguageDetection?: boolean;
    enableCaching?: boolean;
    cacheExpiryHours?: number;
}

// Cache key for localStorage
const COUNTRY_CACHE_KEY = "country_detection_cache";
const USER_COUNTRY_PREFERENCE_KEY = "user_country_preference";

interface CachedCountryData {
    country: CountryCode;
    method: string;
    confidence: number;
    timestamp: number;
    expiresAt: number;
}

/**
 * Enhanced hook for automatically detecting user's country using a two-phase system:
 * Phase 1: Fast detection (timezone + language) - immediate UI update
 * Phase 2: Accurate detection (IP-based) - runs in parallel, updates if different and more confident
 */
export function useAutoCountryDetection(options: UseAutoCountryDetectionOptions = {}): CountryDetectionResult {
    const {
        enableIpDetection = true,
        fallbackCountry = "US",
        enableTimezoneDetection = true,
        enableLanguageDetection = true,
        enableCaching = true,
        cacheExpiryHours = 24,
    } = options;

    const [result, setResult] = useState<CountryDetectionResult>({
        detectedCountry: null,
        isLoading: true,
        method: null,
        error: null,
        confidence: 0,
        fastDetection: null,
        accurateDetection: null,
        isAccurateDetectionComplete: false,
    });

    const isMountedRef = useRef(true);
    const ipDetectionAttemptedRef = useRef(false);

    // Get cached country data
    const getCachedCountry = useCallback((): CachedCountryData | null => {
        if (!enableCaching || typeof window === "undefined") return null;

        try {
            const cached = localStorage.getItem(COUNTRY_CACHE_KEY);
            if (cached) {
                const data: CachedCountryData = JSON.parse(cached);
                if (Date.now() < data.expiresAt) {
                    return data;
                }
                // Remove expired cache
                localStorage.removeItem(COUNTRY_CACHE_KEY);
            }
        } catch (error) {
            console.warn("Failed to read country cache:", error);
        }
        return null;
    }, [enableCaching]);

    // Cache country data
    const setCachedCountry = useCallback((country: CountryCode, method: string, confidence: number) => {
        if (!enableCaching || typeof window === "undefined") return;

        try {
            const data: CachedCountryData = {
                country,
                method,
                confidence,
                timestamp: Date.now(),
                expiresAt: Date.now() + (cacheExpiryHours * 60 * 60 * 1000),
            };
            localStorage.setItem(COUNTRY_CACHE_KEY, JSON.stringify(data));
        } catch (error) {
            console.warn("Failed to cache country data:", error);
        }
    }, [enableCaching, cacheExpiryHours]);

    // Get user's manual country preference
    const getUserCountryPreference = useCallback((): CountryCode | null => {
        if (typeof window === "undefined") return null;

        try {
            return localStorage.getItem(USER_COUNTRY_PREFERENCE_KEY) as CountryCode | null;
        } catch {
            return null;
        }
    }, []);

    // Confidence scoring for different detection methods
    const getConfidenceScore = useCallback((method: string, country: CountryCode): number => {
        switch (method) {
            case "user_preference": return 100; // User manually selected
            case "cached": return 90; // Previously detected and cached
            case "timezone": return 80; // Usually accurate
            case "ip": return 85; // Generally reliable but can be affected by VPNs
            case "language": return 60; // Less reliable, many false positives
            case "fallback": return 10; // Last resort
            default: return 0;
        }
    }, []);

    // Phase 1: Fast detection (timezone + language)
    const performFastDetection = useCallback((): CountryDetectionMethod | null => {
        // Check user preference first
        const userPreference = getUserCountryPreference();
        if (userPreference) {
            return {
                country: userPreference,
                method: "timezone", // Treat as timezone for compatibility
                confidence: getConfidenceScore("user_preference", userPreference),
                timestamp: Date.now(),
            };
        }

        // Check cache
        const cached = getCachedCountry();
        if (cached) {
            return {
                country: cached.country,
                method: "timezone", // Treat as timezone for compatibility
                confidence: getConfidenceScore("cached", cached.country),
                timestamp: cached.timestamp,
            };
        }

        // Try timezone detection
        if (enableTimezoneDetection) {
            const timezoneCountry = detectCountryFromTimezone();
            if (timezoneCountry) {
                return {
                    country: timezoneCountry,
                    method: "timezone",
                    confidence: getConfidenceScore("timezone", timezoneCountry),
                    timestamp: Date.now(),
                };
            }
        }

        // Try language detection as fallback
        if (enableLanguageDetection) {
            const languageCountry = detectCountryFromLanguage();
            if (languageCountry) {
                return {
                    country: languageCountry,
                    method: "language",
                    confidence: getConfidenceScore("language", languageCountry),
                    timestamp: Date.now(),
                };
            }
        }

        return null;
    }, [enableTimezoneDetection, enableLanguageDetection, getCachedCountry, getUserCountryPreference, getConfidenceScore]);

    // Phase 2: Accurate detection (IP-based)
    const performAccurateDetection = useCallback(async (): Promise<CountryDetectionMethod | null> => {
        if (!enableIpDetection || ipDetectionAttemptedRef.current) {
            return null;
        }

        ipDetectionAttemptedRef.current = true;

        try {
            // Try primary IP detection service
            const ipCountry = await detectCountryFromIP();
            if (ipCountry && isMountedRef.current) {
                const detection: CountryDetectionMethod = {
                    country: ipCountry,
                    method: "ip",
                    confidence: getConfidenceScore("ip", ipCountry),
                    timestamp: Date.now(),
                };

                // Cache the result
                setCachedCountry(ipCountry, "ip", detection.confidence);
                return detection;
            }

            // If primary fails, try fallback IP detection service
            const fallbackIpCountry = await detectCountryFromIPFallback();
            if (fallbackIpCountry && isMountedRef.current) {
                const detection: CountryDetectionMethod = {
                    country: fallbackIpCountry,
                    method: "ip",
                    confidence: getConfidenceScore("ip", fallbackIpCountry),
                    timestamp: Date.now(),
                };

                // Cache the result
                setCachedCountry(fallbackIpCountry, "ip", detection.confidence);
                return detection;
            }
        } catch (error) {
            console.warn("IP detection failed:", error);
        }

        return null;
    }, [enableIpDetection, getConfidenceScore, setCachedCountry]);

    // Determine the best country based on available detections
    const determineBestCountry = useCallback((
        fast: CountryDetectionMethod | null,
        accurate: CountryDetectionMethod | null
    ): { country: CountryCode; method: string; confidence: number } => {
        // If we have both detections
        if (fast && accurate) {
            // If they agree, use the more confident one
            if (fast.country === accurate.country) {
                return accurate.confidence >= fast.confidence
                    ? { country: accurate.country, method: accurate.method, confidence: accurate.confidence }
                    : { country: fast.country, method: fast.method, confidence: fast.confidence };
            }

            // If they disagree, use the more confident one
            return accurate.confidence > fast.confidence
                ? { country: accurate.country, method: accurate.method, confidence: accurate.confidence }
                : { country: fast.country, method: fast.method, confidence: fast.confidence };
        }

        // Use whichever detection we have
        if (accurate) {
            return { country: accurate.country, method: accurate.method, confidence: accurate.confidence };
        }

        if (fast) {
            return { country: fast.country, method: fast.method, confidence: fast.confidence };
        }

        // Fallback
        return {
            country: fallbackCountry,
            method: "fallback",
            confidence: getConfidenceScore("fallback", fallbackCountry)
        };
    }, [fallbackCountry, getConfidenceScore]);

    useEffect(() => {
        isMountedRef.current = true;
        ipDetectionAttemptedRef.current = false;

        const runDetection = async () => {
            try {
                // Phase 1: Fast detection - immediate UI update
                const fastDetection = performFastDetection();

                if (fastDetection && isMountedRef.current) {
                    setResult(prev => ({
                        ...prev,
                        detectedCountry: fastDetection.country,
                        isLoading: false,
                        method: fastDetection.method as any,
                        error: null,
                        confidence: fastDetection.confidence,
                        fastDetection,
                    }));
                }

                // Phase 2: Accurate detection - runs in parallel
                if (enableIpDetection) {
                    const accurateDetection = await performAccurateDetection();

                    if (isMountedRef.current) {
                        const currentFast = fastDetection;
                        const best = determineBestCountry(currentFast, accurateDetection);

                        setResult(prev => ({
                            ...prev,
                            detectedCountry: best.country,
                            method: best.method as any,
                            confidence: best.confidence,
                            accurateDetection,
                            isAccurateDetectionComplete: true,
                            // Only update if the accurate detection is different and more confident
                            ...(accurateDetection && (
                                !currentFast ||
                                accurateDetection.country !== currentFast.country ||
                                accurateDetection.confidence > currentFast.confidence
                            ) ? {
                                detectedCountry: accurateDetection.country,
                                method: accurateDetection.method as any,
                                confidence: accurateDetection.confidence,
                            } : {}),
                        }));
                    }
                } else {
                    // If IP detection is disabled, mark as complete
                    if (isMountedRef.current) {
                        setResult(prev => ({
                            ...prev,
                            isAccurateDetectionComplete: true,
                        }));
                    }
                }

                // If no detection worked, use fallback
                if (!fastDetection && !enableIpDetection && isMountedRef.current) {
                    setResult(prev => ({
                        ...prev,
                        detectedCountry: fallbackCountry,
                        isLoading: false,
                        method: "fallback",
                        error: null,
                        confidence: getConfidenceScore("fallback", fallbackCountry),
                        isAccurateDetectionComplete: true,
                    }));
                }

            } catch (error) {
                if (isMountedRef.current) {
                    setResult(prev => ({
                        ...prev,
                        detectedCountry: fallbackCountry,
                        isLoading: false,
                        method: "fallback",
                        error: error instanceof Error ? error.message : "Unknown error",
                        confidence: getConfidenceScore("fallback", fallbackCountry),
                        isAccurateDetectionComplete: true,
                    }));
                }
            }
        };

        runDetection();

        return () => {
            isMountedRef.current = false;
        };
    }, [
        enableIpDetection,
        enableTimezoneDetection,
        enableLanguageDetection,
        fallbackCountry,
        performFastDetection,
        performAccurateDetection,
        determineBestCountry,
        getConfidenceScore
    ]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
        };
    }, []);

    return result;
}

// Export utility function to save user's country preference
export const saveUserCountryPreference = (country: CountryCode): void => {
    if (typeof window === "undefined") return;

    try {
        localStorage.setItem(USER_COUNTRY_PREFERENCE_KEY, country);
    } catch (error) {
        console.warn("Failed to save user country preference:", error);
    }
};

// Export utility function to clear country cache
export const clearCountryCache = (): void => {
    if (typeof window === "undefined") return;

    try {
        localStorage.removeItem(COUNTRY_CACHE_KEY);
        localStorage.removeItem(USER_COUNTRY_PREFERENCE_KEY);
    } catch (error) {
        console.warn("Failed to clear country cache:", error);
    }
};
