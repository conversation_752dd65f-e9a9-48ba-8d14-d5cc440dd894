"use client";

import Link from "next/link";
import { useSession } from "next-auth/react";
import { UserNav } from "@/app/components/user-nav";
import { Button } from "@/common/components/ui/button";
import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { setFlashMessage } from "@/common/lib/flash-message-utils";

interface Props {
  onClick?: () => void;
}

export default function UserAuthButtons({ onClick }: Props) {
  const session = useSession();
  const pathname = usePathname();

  useEffect(() => {
    session.update().then((updatedSession) => {
      if (updatedSession?.user.id !== session?.data?.user.id) {
        setFlashMessage({
          title: "Session Expired",
          description: "Your session has expired. Please login again.",
          variant: "warning",
        });

        // delay to show the flash message
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      }
    });
  }, [pathname]);

  if (session.status === "authenticated") return <UserNav />;

  return (
    <div className="flex w-full flex-col gap-4 md:flex-row">
      <Button asChild onClick={onClick}>
        <Link href="/login">Login</Link>
      </Button>
      <Button variant="outline" asChild onClick={onClick}>
        <Link href="/signup">Sign Up</Link>
      </Button>
    </div>
  );
}
