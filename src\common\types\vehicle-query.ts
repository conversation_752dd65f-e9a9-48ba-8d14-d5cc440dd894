import { PromotionType } from "@/common/models";

export type TransmissionType = "AUTO" | "MANUAL" | null;
export type SortByType = "random" | "minDailyRate" | "avgRating" | "name" | "model";
export type SortDirectionType = "ASC" | "DESC";

export interface VehicleQueryParams {
    location: number | null;
    start: string;
    end: string;
    startTime: string;
    endTime: string;
    vehicleType: string;
    page: number;
    size: number;
    agencyId: string;
    lowMileageLimit: number | null;
    maxDeposit: number | null;
    minPrice: number | null;
    maxPrice: number | null;
    transmission: TransmissionType;
    hasPromotion: boolean | null;
    promotionType: PromotionType | null;
    sortBy: SortByType;
    sortDirection: SortDirectionType;
}

export interface VehicleQueryState extends Omit<VehicleQueryParams, 'start' | 'end'> {
    start: string | undefined;
    end: string | undefined;
}

export interface VehicleQueryActions {
    updateLocation: (id: number | null) => void;
    updateStart: (date: Date | undefined) => void;
    updateEnd: (date: Date | undefined) => void;
    updateStartTime: (time: string) => void;
    updateEndTime: (time: string) => void;
    updateVehicleType: (type: string) => void;
    updatePage: (page: number) => void;
    updateSize: (size: number) => void;
    updateLowMileageLimit: (limit: number | null) => void;
    updateMaxDeposit: (deposit: number | null) => void;
    updateMinPrice: (price: number | null) => void;
    updateMaxPrice: (price: number | null) => void;
    updateTransmission: (transmission: TransmissionType) => void;
    updateHasPromotion: (hasPromotion: true | null | undefined) => void;
    updatePromotionType: (promotionType: PromotionType | null) => void;
    updateSortBy: (sortBy: SortByType) => void;
    updateSortDirection: (sortDirection: SortDirectionType) => void;
}
