import VehicleBookingService from "@/common/services/vehicle-booking.service";
import {
  PaymentCancelledCard,
  PaymentSuccessfulCard,
} from "@/app/bookings/checkout/success/booking-payment-status-cards";

interface Props {
  searchParams: {
    bookingId: string;
    redirect_status: "succeeded";
  };
}

const service = new VehicleBookingService();

export default async function CheckoutSuccess({ searchParams }: Props) {
  const { bookingId: id, redirect_status } = await searchParams;

  const booking = await service.getBookingById(id);
  const paymentSuccessful = booking?.status === "BOOKED";

  return (
    <div className="container mx-auto flex items-center justify-center px-4 pt-28 pb-16">
      {paymentSuccessful || redirect_status === "succeeded" ? (
        <PaymentSuccessfulCard />
      ) : (
        <PaymentCancelledCard />
      )}
    </div>
  );
}
