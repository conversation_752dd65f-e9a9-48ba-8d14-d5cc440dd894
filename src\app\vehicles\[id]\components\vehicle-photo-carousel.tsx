"use client";
import { VehiclePhoto } from "@/common/models";
import {
  Carousel,
  CarouselMainContainer,
  CarouselNext,
  CarouselPrevious,
  CarouselThumbsContainer,
  SliderMainItem,
  SliderThumbItem,
} from "@/common/components/ui/multi-carousel";
import Image from "next/image";

interface Props {
  photos: VehiclePhoto[];
}

export default function VehiclePhotoCarousel({ photos }: Props) {
  return (
    <Carousel>
      <CarouselNext
        className="top-1/3 right-5 size-8 -translate-y-1/3"
        variant="secondary"
      />
      <CarouselPrevious
        className="top-1/3 left-5 size-8 -translate-y-1/3"
        variant="secondary"
      />
      <CarouselMainContainer className="h-[350px]">
        {photos.map((photo, index) => (
          <SliderMainItem
            key={index}
            className="overflow-hidden rounded-t-xl bg-transparent p-0"
          >
            <div className="flex size-full overflow-hidden">
              <Image
                src={photo.url}
                alt={photo.lastModifiedDate}
                className="block w-full object-cover"
                width={500}
                height={500}
              />
            </div>
          </SliderMainItem>
        ))}
      </CarouselMainContainer>
      <CarouselThumbsContainer className="h-28">
        {photos.map((photo, index) => (
          <SliderThumbItem
            key={index}
            index={index}
            className="h-full bg-transparent"
          >
            <div className="flex size-full overflow-hidden rounded-xl">
              <Image
                src={photo.url}
                alt={photo.lastModifiedDate}
                className="block h-full w-full object-cover"
                width={600}
                height={600}
              />
            </div>
          </SliderThumbItem>
        ))}
      </CarouselThumbsContainer>
    </Carousel>
  );
}
