import { definitions } from "@/common/types/api/schema";

// Export the Pageable and Sort types from the schema
export type Pageable = definitions["Pageable"];
export type Sort = definitions["Sort"];

// Generic type for paginated responses based on the schema structure
export interface Page<T> {
    content: T[];
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: Pageable;
    size: number;
    sort: Sort;
    totalElements: number;
    totalPages: number;
}

// Helper function to create a type for a paginated entity from the schema
export type PageOf<T extends keyof definitions> =
    T extends keyof definitions ?
    T extends `${infer U}` ?
    `Page«${U}»` extends keyof definitions ?
    definitions[`Page«${U}»`] :
    never :
    never :
    never;
