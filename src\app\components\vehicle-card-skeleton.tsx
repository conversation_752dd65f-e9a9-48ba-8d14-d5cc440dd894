import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON>Header,
} from "@/common/components/ui/card";
import { Skeleton } from "@/common/components/ui/skeleton";

export default function VehicleCardSkeleton() {
  return (
    <Card className="overflow-hidden pt-0">
      <CardHeader className="p-0">
        <Skeleton className="h-72 w-full rounded-none" />
      </CardHeader>
      <CardContent className="grid gap-4 px-6">
        {/* Rating Skeleton */}
        <div className="flex items-center gap-1">
          <div className="flex gap-1">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-5 w-5" />
            ))}
          </div>
          <Skeleton className="h-4 w-16" />
        </div>

        {/* Title Skeleton */}
        <Skeleton className="h-7 w-4/5" />

        {/* Price Skeleton */}
        <Skeleton className="h-6 w-24" />

        {/* Specs Grid */}
        <div className="grid grid-cols-2 gap-4 py-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5" />
            <Skeleton className="h-4 w-20" />
          </div>
        </div>
      </CardContent>

      <CardFooter>
        <Skeleton className="h-10 w-full" />
      </CardFooter>
    </Card>
  );
}
