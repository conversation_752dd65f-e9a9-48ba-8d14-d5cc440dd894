"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/common/components/ui/card";
import { CheckCircle, XCircle } from "lucide-react";
import Link from "next/link";
import { Button } from "@/common/components/ui/button";
import useQuotedBooking from "@/common/hooks/use-quoted-booking";
import useReservedBooking from "@/common/hooks/use-reserved-vehicle-booking";
export function PaymentSuccessfulCard() {
  const { clearQuotedBooking } = useQuotedBooking();
  const { clearReservedBooking } = useReservedBooking();

  clearQuotedBooking();
  clearReservedBooking();

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <div className="mb-4 flex items-center justify-center">
          <CheckCircle className="h-12 w-12 text-green-500" />
        </div>
        <CardTitle className="text-center text-2xl font-bold">
          Payment Successful!
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-center">
        <p>
          Your vehicle booking has been created successfully. Thank you for your
          payment.
        </p>
        <p className="font-semibold">
          Please check your email for the booking invoice and further details.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col gap-4">
        <Button asChild className="w-full">
          <Link href="/">Search more vehicles</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

export function PaymentCancelledCard() {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <div className="mb-4 flex items-center justify-center">
          <XCircle className="h-12 w-12 text-yellow-500" />
        </div>
        <CardTitle className="text-center text-2xl font-bold">
          Payment Cancelled
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-center">
        <p>
          You&apos;ve cancelled your payment. No charges have been made to your
          account.
        </p>
        <p className="font-semibold">
          If you have any questions or would like to complete your booking,
          please don&apos;t hesitate to contact us.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col gap-4">
        <Button asChild className="w-full">
          <Link href="/">Search more vehicles</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
