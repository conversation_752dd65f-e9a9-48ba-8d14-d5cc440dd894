"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface ModalContextProps {
  isSignupModalOpen: boolean;
  setIsSignupModalOpen: (open: boolean) => void;
}

const ModalContext = createContext<ModalContextProps | undefined>(undefined);

export function ModalProvider({ children }: { children: ReactNode }) {
  const [isSignupModalOpen, setIsSignupModalOpen] = useState(false);

  return (
    <ModalContext.Provider value={{ isSignupModalOpen, setIsSignupModalOpen }}>
      {children}
    </ModalContext.Provider>
  );
}

export function useModal(): ModalContextProps {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error("useModal must be used within a ModalProvider");
  }
  return context;
}
