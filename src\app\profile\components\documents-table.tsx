"use client";

import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/common/components/ui/table";
import { Button } from "@/common/components/ui/button";
import { Eye, Upload } from "lucide-react";
import { useState, useRef } from "react";
import { ViewDocumentModal } from "./view-document-modal";
import { Client, ClientDocument } from "@/common/models";
import { useAddClientDocuments } from "@/common/hooks/use-add-client-documents";
import { useUploadDocument } from "@/common/hooks/use-upload-document";
import { useQueryClient } from "@tanstack/react-query";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/common/components/ui/tooltip";
import { getDocumentDisplayName } from "@/common/lib/document-utils";
import { toast } from "sonner";

interface DocumentsTableProps {
  client: Client;
}

const ACCEPTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export function DocumentsTable({ client }: DocumentsTableProps) {
  const documents = client.clientDocs;
  const [selectedDocument, setSelectedDocument] =
    useState<ClientDocument | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadingDocId, setUploadingDocId] = useState<number | null>(null);

  const queryClient = useQueryClient();
  const { mutateAsync: uploadDocument } = useUploadDocument();
  const { mutate: addClientDocuments } = useAddClientDocuments();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleViewDocument = (document: ClientDocument) => {
    setSelectedDocument(document);
    setIsViewDialogOpen(true);
  };

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>,
    document: ClientDocument,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      toast("Invalid file type",{
        description: "Please upload a JPEG, JPG, PNG, or WEBP image",
      });
      return;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      toast("File too large", {
        description: "File size must be less than 10MB",
      });
      return;
    }

    try {
      setUploadingDocId(document.id);
      const { fileUrl } = await uploadDocument(file);

      addClientDocuments(
        {
          clientId: client.id,
          documents: [
            {
              name: document.name,
              url: fileUrl,
            },
          ],
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["client", client.id] });
            toast("Document updated successfully", {
                duration: 3000,
            });
          },
          onError: (error) => {
            toast("Failed to update document", {
              description:
                error instanceof Error ? error.message : "Unknown error",
            duration: 3000
            });
          },
          onSettled: () => {
            setUploadingDocId(null);
            if (fileInputRef.current) {
              fileInputRef.current.value = "";
            }
          },
        },
      );
    } catch (error) {
      toast("Failed to upload file", {
        description: error instanceof Error ? error.message : "Unknown error",
      });
      setUploadingDocId(null);
    }
  };

  return (
    <>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept=".jpg,.jpeg,.png,.webp"
        onChange={(e) =>
          selectedDocument && handleFileSelect(e, selectedDocument)
        }
      />
      <div className="overflow-hidden rounded-md border">
        <Table>
          <TableHeader className="bg-slate-100">
            <TableRow>
              <TableHead>Document</TableHead>
              <TableHead>Upload Date</TableHead>
              <TableHead className="w-[120px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documents.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center">
                  No documents found
                </TableCell>
              </TableRow>
            ) : (
              documents.map((document) => (
                <TableRow key={document.id}>
                  <TableCell>{getDocumentDisplayName(document.name)}</TableCell>
                  <TableCell>{formatDate(document.createdDate)}</TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <div className="flex gap-2">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleViewDocument(document)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>View document</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setSelectedDocument(document);
                                fileInputRef.current?.click();
                              }}
                              disabled={uploadingDocId === document.id}
                            >
                              <Upload
                                className={`h-4 w-4 ${uploadingDocId === document.id ? "animate-pulse" : ""}`}
                              />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {uploadingDocId === document.id
                                ? "Updating..."
                                : "Update document"}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TooltipProvider>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {selectedDocument && (
        <ViewDocumentModal
          open={isViewDialogOpen}
          onOpenChange={setIsViewDialogOpen}
          document={selectedDocument}
        />
      )}
    </>
  );
}
