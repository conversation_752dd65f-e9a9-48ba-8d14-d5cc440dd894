"use client";

import { cn } from "@/common/lib/shadcn-utils";
import { useBrandConfig } from "@/common/hooks/use-brand-config";
import { isKarlinkBrand } from "@/common/config/brands/utils";

interface TextLogoProps {
  inverted?: boolean;
  className?: string;
}

export function TextLogo({ inverted = false, className }: TextLogoProps) {
  const brandConfig = useBrandConfig();

  // For KarLink, keep the original styling
  if (isKarlinkBrand()) {
    return (
      <span className={cn("font-bold", className)}>
        <span className={inverted ? "text-white" : "text-primary"}>My</span>
        <span className="text-secondary">Kar</span>
        <span className={inverted ? "text-white" : "text-primary"}>Link</span>
      </span>
    );
  }

  // For other brands, use their name
  return (
    <span className={cn("font-bold", className)}>
      <span className={inverted ? "text-white" : "text-secondary"}>
        {brandConfig.brandName}
      </span>
    </span>
  );
}
