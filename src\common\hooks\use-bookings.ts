import { useSession } from "next-auth/react";
import { useBrandConfig } from "./use-brand-config";
import useBookingsQuery from "./use-bookings-query";
import { useQuery } from "@tanstack/react-query";

import { vehicleBookingService } from "../services/vehicle-booking.service";

export default function useBookings() {
    const session = useSession();
    const clientId = session.data?.user?.client?.id || 0;
    const currentBrand = useBrandConfig()
    const { state } = useBookingsQuery();

    return useQuery({
        queryKey: ["bookings", state],
        queryFn: () => vehicleBookingService.getBookingsForClient(clientId.toString(), state.page, state.size, currentBrand.agencyId?.toString(), state.statuses, state.searchCriteria),
    })
}
