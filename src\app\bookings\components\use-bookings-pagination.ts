import { useMemo } from "react";

interface PaginationConfig {
    currentPage: number;
    totalPages: number;
    maxVisible?: number;
}

export function useBookingsPagination({
    currentPage,
    totalPages,
    maxVisible = 5,
}: PaginationConfig) {
    return useMemo(() => {
        if (totalPages <= maxVisible) {
            return {
                pages: Array.from({ length: totalPages }, (_, i) => i + 1),
                showEllipsis: false,
            };
        }

        const half = Math.floor(maxVisible / 2);
        let start = Math.max(1, currentPage - half);
        const end = Math.min(totalPages, start + maxVisible - 1);

        if (end - start + 1 < maxVisible) {
            start = Math.max(1, end - maxVisible + 1);
        }

        const pages = Array.from({ length: end - start + 1 }, (_, i) => start + i);
        const showEllipsis = start > 1 || end < totalPages;

        return { pages, showEllipsis };
    }, [currentPage, totalPages, maxVisible]);
}
