{"name": "karlink_webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "generate:api-schema": "npx openapi-typescript@5.4.1 http://*************:8300/v2/api-docs --output ./src/common/types/api/schema.ts", "generate:auth-api-schema": "npx openapi-typescript@5.4.1 http://*************:8203/v3/api-docs --output ./src/common/types/api/auth-schema.ts"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.2", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.5.0", "@tailwindcss/postcss": "^4.1.5", "@tanstack/query-sync-storage-persister": "^5.72.2", "@tanstack/react-query": "^5.62.0", "@tanstack/react-query-devtools": "^5.62.0", "@tanstack/react-query-persist-client": "^5.72.2", "@tanstack/react-table": "^8.20.5", "@types/nprogress": "^0.2.3", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel": "^8.5.2", "embla-carousel-react": "^8.6.0", "geist": "^1.3.1", "gray-matter": "^4.0.3", "html2canvas": "^1.4.1", "i": "^0.3.7", "input-otp": "^1.4.2", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "libphonenumber-js": "^1.12.8", "lucide-react": "^0.508.0", "next": "^15.1.0", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "npm": "^10.9.1", "nprogress": "^0.2.0", "nuqs": "^2.4.1", "pino": "^9.5.0", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-camera-pro": "^1.4.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.3", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-rehype": "^11.1.1", "sonner": "^1.7.4", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "zod": "^3.24.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.16", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.2", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.5", "typescript": "^5"}}