"use client";

import { useState } from "react";
import { Star } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/common/components/ui/card";
import { Label } from "@/common/components/ui/label";
import { Textarea } from "@/common/components/ui/textarea";
import { useParams } from "next/navigation";
import { RatingStatus } from "@/common/models";
import useRateBooking from "@/common/hooks/use-rate-booking";
import useBooking from "@/common/hooks/use-booking";
import { Button } from "@/common/components/ui/button";
import { toast } from "sonner";
import Loader from "@/common/components/loader";

interface RatingCriteria {
  label: string;
  rating: number;
  status: RatingStatus;
}

export default function VehicleRating() {
  const params = useParams();
  const { data: booking, isLoading } = useBooking(Number(params.id));
  const vehicle = booking?.vehicle;
  const bookingRating = booking?.ratings?.find(
    (rating) => rating.type === "VEHICLE",
  );
  const [criteria, setCriteria] = useState<RatingCriteria[]>([
    {
      label: "Vehicle cleanliness",
      rating: 0,
      status: RatingStatus.CLEANLINESS,
    },
    {
      label: "Vehicle condition",
      rating: 0,
      status: RatingStatus.CONDITION,
    },
    {
      label: "Vehicle comfort",
      rating: 0,
      status: RatingStatus.COMFORT,
    },
    {
      label: "Pick-up and drop-off process",
      rating: 0,
      status: RatingStatus.PROCESS,
    },
    {
      label: "Vehicle provider professionalism and communication",
      rating: 0,
      status: RatingStatus.PROFESSIONALISM,
    },
  ]);
  const [comment, setComment] = useState("");
  const { isPending, mutate } = useRateBooking();

  const handleRating = (status: RatingStatus, rating: number) => {
    setCriteria(
      criteria.map((item) =>
        item.status === status ? { ...item, rating } : item,
      ),
    );
  };

  const handleSubmit = async () => {
    if (bookingRating) {
      toast("Already Rated", {
        description: "You have already submitted a rating for this booking.",
      });
      return;
    }

    // Validate all criteria have ratings
    const hasIncompleteRatings = criteria.some((item) => item.rating === 0);

    // Validate comment is not empty
    const isCommentEmpty = comment.trim().length === 0;

    if (hasIncompleteRatings || isCommentEmpty) {
      toast("Validation Error", {
        description:
          "Please provide ratings for all criteria and add a comment before submitting",
      });
      return;
    }

    const data = {
      comment,
      ratingItems: criteria.map((item) => ({
        rate: item.rating,
        status: item.status,
      })),
    };
    mutate({ id: Number(params.id), data });
  };

  // Show loading skeleton while fetching booking data
  if (isLoading) {
    return (
      <div className="flex min-h-[74vh] items-center justify-center p-4">
        <Card className="w-full max-w-xl">
          <CardHeader className="space-y-2">
            <div className="bg-muted h-8 w-3/4 animate-pulse rounded-md" />
            <div className="bg-muted h-4 w-1/2 animate-pulse rounded-md" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="bg-muted h-4 w-1/3 animate-pulse rounded-md" />
                <div className="flex gap-1">
                  {[1, 2, 3, 4, 5].map((j) => (
                    <div
                      key={j}
                      className="bg-muted h-6 w-6 animate-pulse rounded-full"
                    />
                  ))}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Add early return if already rated
  if (bookingRating) {
    return (
      <div className="flex min-h-[74vh] items-center justify-center p-4">
        <Card className="w-full max-w-xl">
          <CardHeader className="space-y-2 text-center">
            <CardTitle className="text-2xl font-bold">Already Rated</CardTitle>
            <CardDescription>
              You have already submitted a rating for this booking. Thank you
              for your feedback!
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex min-h-[74vh] items-center justify-center p-4">
      <div className="pt-28 pb-16">
        <Card className="w-full max-w-xl">
          <CardHeader className="space-y-2 text-center">
            <CardTitle className="text-2xl font-bold">
              Rate the {vehicle?.name} {vehicle?.model}
            </CardTitle>
            <CardDescription>
              Hello, hope you&apos;re having a great day. Please rate your
              experience with the vehicle.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {criteria.map((item) => (
              <div
                key={item.status}
                className="flex items-center justify-between"
              >
                <Label className="w-1/2 text-sm">{item.label}</Label>
                <div className="flex gap-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => handleRating(item.status, star)}
                      className="transition-transform hover:scale-110"
                    >
                      <Star
                        className={`h-6 w-6 ${
                          star <= item.rating
                            ? "fill-yellow-400 stroke-yellow-400"
                            : "fill-muted stroke-muted-foreground"
                        }`}
                      />
                    </button>
                  ))}
                </div>
              </div>
            ))}
            <div className="space-y-2 pt-4">
              <Label htmlFor="comment" className="text-sm">
                Overall comment
              </Label>
              <Textarea
                id="comment"
                placeholder="Share your experience with us..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                className="min-h-[80px]"
              />
            </div>
            <Button
              onClick={handleSubmit}
              disabled={isPending}
              className="mt-4 w-full"
              size="lg"
            >
              {isPending && <Loader />}
              {isPending ? "Submitting..." : "Submit Rating"}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
