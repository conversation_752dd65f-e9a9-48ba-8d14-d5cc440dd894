import { format } from "date-fns";

export function formatToISO8601(date: Date) {
    return format(date, "yyyy-MM-dd'T'HH:mm");
}

export function formatDateWithTime(dateString: string) {
    return format(new Date(dateString), "dd MMM yyyy, 'at' hh:mmaa");
}

export function formatDateWithoutTime(dateString: string) {
    return format(new Date(dateString), "dd MMM yyyy");
}


export function formatDate(date: Date) {
    return format(date, "yyyy-MM-dd");
}

export function parseDate(dateStr: string) {
    try {
        return format(new Date(dateStr), "yyyy-MM-dd");
    } catch {
        return undefined;
    }
}

export function formatCustomDateTime(date: Date) {
    const isoString = date.toISOString();
    return isoString.slice(0, 16).replace("Z", "");
}

export function getDefaultDates() {
    const today = new Date();
    today.setHours(8, 0, 0, 0);

    const tomorrow = new Date();
    tomorrow.setHours(8, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return {
        today,
        tomorrow,
    };
}

/**
 * Calculate rental days based on business logic using intervalToDuration:
 * - Minimum 1 day rental
 * - 1-hour grace period after pickup time on return day
 * - Same day returns not allowed
 */
export function calculateRentalDays(
    fromDate: Date,
    toDate: Date,
    pickupTime: string,
    dropoffTime: string
): number {
    // Combine date and time for accurate comparison
    const [pickupHour, pickupMinute] = pickupTime.split(':').map(Number);
    const [dropoffHour, dropoffMinute] = dropoffTime.split(':').map(Number);

    const startDateTime = new Date(fromDate);
    startDateTime.setHours(pickupHour, pickupMinute, 0, 0);

    const endDateTime = new Date(toDate);
    endDateTime.setHours(dropoffHour, dropoffMinute, 0, 0);

    // Calculate day difference (midnight to midnight)
    const startDate = new Date(fromDate);
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(toDate);
    endDate.setHours(0, 0, 0, 0);
    const dayDiff = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // Always minimum 1 day rental
    let rentalDays = dayDiff < 1 ? 1 : dayDiff;

    // Grace period is always 08:00 to 09:00 on dropoff day
    const graceStart = new Date(endDate);
    graceStart.setHours(8, 0, 0, 0);
    const graceEnd = new Date(endDate);
    graceEnd.setHours(9, 0, 0, 0);

    // If dropoff is after the grace period, add an extra day
    if (endDateTime > graceEnd) {
        rentalDays += 1;
    }

    return rentalDays;
}
