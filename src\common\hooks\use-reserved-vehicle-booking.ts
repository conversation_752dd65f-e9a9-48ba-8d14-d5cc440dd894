import { useLocalStorage } from "@uidotdev/usehooks";
import { VehicleBooking } from "@/common/models";

export const RESERVED_BOOKING_KEY = "reserved-booking";

export default function useReservedBooking() {
    const [booking, setBooking] = useLocalStorage<VehicleBooking | null>(RESERVED_BOOKING_KEY, null);
    const saveReservedBooking = (newBooking: VehicleBooking | null) => {
        setBooking(newBooking);
    };
    const clearReservedBooking = () => {
        setBooking(null);
    };

    return {
        booking,
        saveReservedBooking,
        clearReservedBooking,
    };
}
