import type { MetadataRoute } from 'next'
import { getCurrentBrandConfig } from '@/common/config/brands/utils'

export default function manifest(): MetadataRoute.Manifest {
    const brandConfig = getCurrentBrandConfig()

    // Only generate manifest for Karlink brand
    if (brandConfig.name.toLowerCase() !== 'karlink') {
        return {
            name: brandConfig.brandName,
            short_name: brandConfig.name,
            start_url: '/',
            display: 'standalone',
            background_color: '#ffffff',
            theme_color: '#000000',
            icons: [],
        }
    }

    return {
        name: 'MyKarLink - Car Rental & Vehicle Sharing',
        short_name: 'MyKarLink',
        description: 'Car rental and vehicle sharing platform in Zimbabwe',
        start_url: '/',
        display: 'standalone',
        background_color: '#ffffff',
        theme_color: '#0F172A',
        orientation: 'portrait',
        scope: '/',
        categories: ['travel', 'transportation', 'business'],
        lang: 'en',
        icons: [
            {
                src: '/brands/karlink/images/icon-192x192.png',
                sizes: '192x192',
                type: 'image/png',
                purpose: 'maskable'
            },
            {
                src: '/brands/karlink/images/icon-512x512.png',
                sizes: '512x512',
                type: 'image/png',
                purpose: 'any'
            },
            {
                src: '/brands/karlink/images/apple-touch-icon.png',
                sizes: '180x180',
                type: 'image/png',
                purpose: 'any'
            }
        ],
        screenshots: [
            {
                src: '/brands/karlink/images/screenshot-wide.png',
                sizes: '1280x720',
                type: 'image/png',
                form_factor: 'wide'
            },
            {
                src: '/brands/karlink/images/screenshot-narrow.png',
                sizes: '750x1334',
                type: 'image/png',
                form_factor: 'narrow'
            }
        ],
        related_applications: [],
        prefer_related_applications: false
    }
}
