"use client";

import * as React from "react";
import { Combobox, type ComboboxOption } from "./combobox";
import { useCountries } from "@/common/lib/country_utils";
import { cn } from "@/common/lib/utils";

interface CountrySelectorProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: boolean;
}

export function CountryComboBox({
  value,
  onValueChange,
  placeholder = "Select country...",
  className,
  disabled = false,
  error = false,
}: CountrySelectorProps) {
  const countries = useCountries();

  const countryOptions: ComboboxOption[] = countries.map((country) => ({
    value: country.name,
    label: country.name,
    extra: <span className="text-lg">{country.flag}</span>,
  }));

  return (
    <Combobox
      options={countryOptions}
      placeholder={placeholder}
      searchPlaceholder="Search countries..."
      emptyMessage="No country found."
      value={value}
      onValueChange={onValueChange}
      className={cn(
        "w-full",
        error && "border-destructive focus-visible:ring-destructive",
        className,
      )}
      disabled={disabled}
    />
  );
}
