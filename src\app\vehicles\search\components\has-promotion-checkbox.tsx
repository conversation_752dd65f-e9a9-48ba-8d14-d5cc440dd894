"use client";
import { Checkbox } from "@/common/components/ui/checkbox";
import { useFilters } from "@/app/vehicles/search/components/filter-context";
import { Label } from "@/common/components/ui/label";

export default function HasPromotionCheckbox() {
  const { filters, setFilters } = useFilters();

  return (
    <div className="flex items-center gap-3">
      <Checkbox
        id="has-promotion-checkbox"
        onCheckedChange={(value) =>
          setFilters({ hasPromotion: value ? true : null })
        }
        checked={!!filters.hasPromotion}
      />
      <Label htmlFor="has-promotion-checkbox">
        Only show vehicles on promotion
      </Label>
    </div>
  );
}
