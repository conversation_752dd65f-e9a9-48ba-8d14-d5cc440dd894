import VehiclePhotoCarousel from "@/app/vehicles/[id]/components/vehicle-photo-carousel";
import VehicleDetails from "@/app/vehicles/[id]/components/vehicle-details";
import { Card, CardContent, CardHeader } from "@/common/components/ui/card";
import Separator from "@/app/vehicles/[id]/components/seperator";
import { Vehicle } from "@/common/models";
import Image from "next/image";
import placeholderVehicleImg from "@/common/assets/images/placeholder-vehicle-image.webp";

interface Props {
  vehicle: Vehicle;
}

export default function VehicleInfoCard({ vehicle }: Props) {
  return (
    <Card className="overflow-hidden pt-0">
      <CardHeader className="p-0">
        {vehicle.photos.length > 1 ? (
          <VehiclePhotoCarousel photos={vehicle.photos} />
        ) : (
          <div className="h-[350px] overflow-hidden rounded-t-xl">
            <Image
              src={
                vehicle.photos.length === 1
                  ? vehicle.photos[0].url
                  : placeholderVehicleImg
              }
              alt={`image of ${vehicle.color} ${vehicle.name} ${vehicle.model}`}
              className="h-full w-full object-cover"
              width={500}
              height={350}
            />
          </div>
        )}
      </CardHeader>
      <CardContent className="grid gap-8">
        <VehicleDetails vehicle={vehicle} />
        <div className="flex *:flex-1 lg:hidden">
          <Separator />
        </div>
      </CardContent>
    </Card>
  );
}
