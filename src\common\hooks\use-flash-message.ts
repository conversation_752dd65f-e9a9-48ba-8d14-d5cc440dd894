import { useEffect } from "react";
import {
    clearFlashMessage,
    getFlashMessage,
} from "@/common/lib/flash-message-utils";
import { toast } from "sonner";

export function useFlashMessage() {
    useEffect(() => {
        const message = getFlashMessage();
        if (message) {
            toast(message.title, {
                description: message.description,
            });
            clearFlashMessage();
        }
    }, [toast]);
}
