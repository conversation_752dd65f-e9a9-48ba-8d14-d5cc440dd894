import { useMutation } from "@tanstack/react-query";
import ClientService from "@/common/services/client.service";
import type { ClientDocument } from "@/common/models";

const clientService = new ClientService();

interface AddClientDocumentsParams {
    clientId: number;
    documents: Partial<ClientDocument>[];
}

export function useAddClientDocuments() {
    return useMutation({
        mutationFn: ({ clientId, documents }: AddClientDocumentsParams) => {
            return clientService.addDocuments(clientId, documents);
        }
    });
}