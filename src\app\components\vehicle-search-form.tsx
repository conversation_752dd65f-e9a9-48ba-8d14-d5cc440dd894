"use client";

import { ChangeEvent, useEffect, useState } from "react";
import { X } from "lucide-react";
import { But<PERSON> } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { addDays, format, isBefore, startOfDay } from "date-fns";
import { Location } from "@/common/models";
import { LocationCombobox } from "@/common/components/location-combobox";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import { VEHICLE_TYPES } from "@/app/data";
import { useLocation } from "@/common/hooks/use-location";
import { useVehicleQuery } from "@/common/hooks/use-vehicle-query";
import { usePathname, useRouter } from "next/navigation";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/common/components/ui/tooltip";
import { calculateRentalDays, formatDate } from "@/common/lib/date-utils";
import { Card, CardContent } from "@/common/components/ui/card";
import { cn } from "@/common/lib/shadcn-utils";
import { DateRange } from "react-day-picker";
import { DateSelector } from "./date-selector";

interface CarRentalSearchProps {
  variant?: "default" | "compact";
}

// TODO: Add ability to pass `className to override some styles`
// TODO: Add labels to vehicle type, location, and times like on date selectors.
export default function VehicleSearchForm({
  variant = "default",
}: CarRentalSearchProps) {
  const router = useRouter();
  const pathname = usePathname();
  const query = useVehicleQuery();
  const { data: initialLocation, isLoading } = useLocation(
    query.state.location,
  );
  const [isEditing, setIsEditing] = useState(variant === "default");
  const [location, setLocation] = useState<Location | null>(
    initialLocation ?? null,
  );
  const [pickUpDate, setPickUpDate] = useState<Date | undefined>(
    new Date(query.state.start!),
  );
  const [dropOffDate, setDropOffDate] = useState<Date | undefined>(
    new Date(query.state.end!),
  );
  const [pickUpTime, setPickUpTime] = useState(query.state.startTime);
  const [dropOffTime, setDropOffTime] = useState(query.state.endTime);
  const [vehicleType, setVehicleType] = useState(query.state.vehicleType);
  const [showTooltip, setShowTooltip] = useState(false);

  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: pickUpDate || new Date(),
    to: dropOffDate || addDays(new Date(), 2),
  });
  const today = startOfDay(new Date());
  const disabledDays = (date: Date) => isBefore(date, today);

  const isToday = (date?: Date) => {
    if (!date) return false;
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  const rentalDays = calculateRentalDays(
    pickUpDate!,
    dropOffDate!,
    pickUpTime,
    dropOffTime,
  );

  // Update location when initialLocation changes
  useEffect(() => {
    if (!isLoading) {
      setLocation(initialLocation ?? null);
    }
  }, [isLoading, initialLocation]);

  // Sync dateRange changes with individual date states
  useEffect(() => {
    if (dateRange?.from) {
      setPickUpDate(dateRange.from);
    }
    if (dateRange?.to) {
      setDropOffDate(dateRange.to);
    }
  }, [dateRange]);

  const handlePickUpTimeChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newTime = e.target.value;
    if (isToday(pickUpDate)) {
      const currentTime = new Date().toLocaleTimeString("en-US", {
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
      });
      if (newTime < currentTime) {
        setPickUpTime(currentTime);
        return;
      }
    }
    setPickUpTime(newTime);
  };

  const handleSearch = () => {
    if (!location) {
      setShowTooltip(true);
      // Auto-hide tooltip after 3 seconds
      setTimeout(() => setShowTooltip(false), 3000);
      return;
    }

    if (pathname === "/vehicles/search") {
      const selectedVehicleType = vehicleType === "ALL" ? "" : vehicleType;
      query.updateLocation(location?.id ?? null);
      query.updateStart(pickUpDate);
      query.updateEnd(dropOffDate);
      query.updateStartTime(pickUpTime);
      query.updateEndTime(dropOffTime);
      query.updateVehicleType(selectedVehicleType);
      // Collapse the form after search when using compact variant
      if (variant === "compact") {
        setIsEditing(false);
      }
    } else {
      const params = new URLSearchParams();
      const { state } = query;
      const queryParams = {
        location: location?.id,
        start: formatDate(pickUpDate!),
        end: formatDate(dropOffDate!),
        startTime: pickUpTime,
        endTime: dropOffTime,
        vehicleType: vehicleType === "ALL" ? null : vehicleType,
        page: state.page.toString(),
        size: state.size.toString(),
        agencyId: state.agencyId,
      };

      Object.entries(queryParams).forEach(([key, value]) => {
        if (value) {
          params.set(key, value.toString());
        }
      });

      router.push(`/vehicles/search?${params.toString()}`);
    }
  };

  const handleClose = () => {
    // revert the changes to local form state using what is in the query
    setLocation(initialLocation ?? null);
    const queryPickUpDate = new Date(query.state.start!);
    const queryDropOffDate = new Date(query.state.end!);
    setPickUpDate(queryPickUpDate);
    setDropOffDate(queryDropOffDate);
    setDateRange({
      from: queryPickUpDate,
      to: queryDropOffDate,
    });
    setPickUpTime(query.state.startTime);
    setDropOffTime(query.state.endTime);
    setVehicleType(query.state.vehicleType);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <Card className="mx-auto w-full">
        <CardContent className="flex flex-col items-center justify-between gap-4 md:flex-row">
          <div className="flex w-full flex-col gap-1 md:w-auto">
            <div className="text-center font-medium md:text-left">
              {location?.name?(location.name+', '):''}{location?.city}, {location?.country}
            </div>
            <div className="text-muted-foreground text-center text-sm md:text-left">
              {pickUpTime}, {format(pickUpDate!, "dd MMM")} - {dropOffTime},{" "}
              {format(dropOffDate!, "dd MMM")} ({rentalDays}{" "}
              {rentalDays === 1 ? "day" : "days"})
            </div>
          </div>
          <Button
            className="w-full md:w-auto"
            onClick={() => setIsEditing(true)}
          >
            Edit Search
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={cn({
        "border-none bg-transparent p-0 shadow-none": variant !== "compact",
      })}
    >
      <CardContent>
        <div className="mx-auto grid w-full gap-4 rounded-lg">
          {variant === "compact" && (
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium">Edit search</h2>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="icon" onClick={handleClose}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
          <div className="grid gap-4">
            <div className="flex flex-col gap-2 lg:flex-row">
              <div className="flex-1">
                <div className="relative">
                  <TooltipProvider>
                    <Tooltip open={showTooltip}>
                      <TooltipTrigger asChild>
                        <div>
                          <LocationCombobox
                            placeholder="Pick Up Location"
                            value={location}
                            onChange={setLocation}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="bottom"
                        align="start"
                        className="w-full max-w-none bg-orange-500 p-2 text-orange-50"
                      >
                        <p>You need to select a location</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 lg:grid-cols-4">
                <DateSelector
                  mode="pickup"
                  label="Pick-up Date"
                  dateRange={dateRange}
                  onDateRangeChange={setDateRange}
                  onPopoverOpenChange={(open) => {
                    if (!open && dateRange?.from && !dateRange.to) {
                      setDateRange({
                        from: dateRange.from,
                        to: addDays(dateRange.from, 1),
                      });
                    }
                  }}
                  disabledDays={disabledDays}
                  pickupTime={pickUpTime}
                  dropoffTime={dropOffTime}
                />

                <div>
                  <div>
                    <Input
                      type="time"
                      value={pickUpTime}
                      onChange={handlePickUpTimeChange}
                      className="bg-background hover:bg-accent h-12"
                      min={
                        isToday(pickUpDate)
                          ? new Date().toLocaleTimeString("en-US", {
                              hour12: false,
                              hour: "2-digit",
                              minute: "2-digit",
                            })
                          : undefined
                      }
                    />
                  </div>
                </div>

                <DateSelector
                  mode="dropoff"
                  label="Drop-off Date"
                  dateRange={dateRange}
                  onDateRangeChange={setDateRange}
                  onPopoverOpenChange={(open) => {
                    if (!open && dateRange?.from && !dateRange.to) {
                      setDateRange({
                        from: dateRange.from,
                        to: addDays(dateRange.from, 1),
                      });
                    }
                  }}
                  disabledDays={disabledDays}
                  pickupTime={pickUpTime}
                  dropoffTime={dropOffTime}
                />

                <div>
                  <div>
                    <Input
                      type="time"
                      value={dropOffTime}
                      onChange={(e) => setDropOffTime(e.target.value)}
                      className="bg-background hover:bg-accent h-12"
                    />
                  </div>
                </div>
              </div>
              <div className="flex-1">
                <Select
                  defaultValue="ALL"
                  onValueChange={(value) => setVehicleType(value)}
                  value={vehicleType === "" ? "ALL" : vehicleType}
                >
                  <SelectTrigger
                    className="hover:bg-accent bg-background h-12 w-full"
                    data-size="custom"
                  >
                    <div className="flex flex-1 flex-col items-start">
                      <span className="text-xs">Vehicle type</span>
                      <SelectValue className="mt-1 font-medium" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Vehicle Types</SelectLabel>
                      {VEHICLE_TYPES.map((type) => (
                        <SelectItem
                          value={type.value}
                          key={type.value + type.label}
                          defaultChecked
                        >
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <Button className="h-12 px-24" onClick={handleSearch}>
                Search
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
