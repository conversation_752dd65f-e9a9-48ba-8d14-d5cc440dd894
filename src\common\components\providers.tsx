"use client";
import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { SessionProvider } from "next-auth/react";
import { Session } from "next-auth";
import { LoadingIndicator } from "@/common/components/ui/loading-indicator";

const queryClient = new QueryClient();

interface Props {
  children: React.ReactNode;
  session: Session | null;
}

const Providers = ({ children, session }: Props) => {
  return (
    <>
      <SessionProvider session={session}>
        <QueryClientProvider client={queryClient}>
          {children}
          <LoadingIndicator 
            color="#0A2FFF"
            height="2px"
            showSpinner={true}
            delay={0}
          />
          <ReactQueryDevtools />
        </QueryClientProvider>
      </SessionProvider>
    </>
  );
};

export default Providers;
