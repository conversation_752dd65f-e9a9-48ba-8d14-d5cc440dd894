"use client";

import { useState } from "react";
import { But<PERSON> } from "@/common/components/ui/button";
import { Card, CardContent } from "@/common/components/ui/card";
import {
  FileIcon,
  Trash2Icon,
  UploadIcon,
  PlusCircleIcon,
  InfoIcon,
  HomeIcon,
  CheckCircleIcon,
  Info,
} from "lucide-react";
import DocumentUploadModal from "./document-upload-modal";
import { Client, ClientDocument } from "@/common/models";
import Link from "next/link";
import { useUploadDocument } from "@/common/hooks/use-upload-document";
import { useAddClientDocuments } from "@/common/hooks/use-add-client-documents";
import { useQueryClient } from "@tanstack/react-query";
import { getDocumentDisplayName } from "@/common/lib/document-utils";
import { toast } from "sonner";
import {
  Alert,
  AlertTitle,
  AlertDescription,
} from "@/common/components/ui/alert";

interface DocumentUploadProps {
  client: Client;
  documentsSubmitted?: boolean;
  existingDocuments?: Document[];
}

export interface NewDocument {
  name: ClientDocument["name"];
  file: File;
}

export default function DocumentUpload({ client }: DocumentUploadProps) {
  const isVerified = client?.verified ?? false;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [documents, setDocuments] = useState<NewDocument[]>([]);

  const queryClient = useQueryClient();
  const [isSuccess, setIsSuccess] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const { mutateAsync: uploadDocument } = useUploadDocument();
  const { mutateAsync: addClientDocuments } = useAddClientDocuments();

  const handleAddDocument = (document: NewDocument) => {
    setDocuments([...documents, document]);
    setIsModalOpen(false);
  };

  const handleRemoveDocument = (name: ClientDocument["name"]) => {
    setDocuments(documents.filter((doc) => doc.name !== name));
  };

  const handleUploadDocuments = async () => {
    if (documents.length === 0) {
      toast("No documents to upload", {
        description: "Please add at least one document before uploading.",
        duration: 3000,
      });
      return;
    }

    setIsUploading(true);

    try {
      // Upload all files first
      const uploadPromises = documents.map((doc) => uploadDocument(doc.file));
      const uploadResults = await Promise.all(uploadPromises);

      // Create the client documents array
      const clientDocs: Partial<ClientDocument>[] = documents.map(
        (doc, index) => ({
          name: doc.name,
          url: uploadResults[index].fileUrl,
        }),
      );

      // Add all documents to the client
      await addClientDocuments(
        {
          clientId: client!.id,
          documents: clientDocs,
        },
        {
          onSuccess: () =>
            queryClient.invalidateQueries({
              queryKey: ["client", client?.id],
            }),
        },
      );

      toast("Documents uploaded successfully", {
        duration: 3000,
      });

      setIsSuccess(true);
    } catch (error) {
      toast("Failed to upload documents", {
        description: error instanceof Error ? error.message : "Unknown error",
        duration: 3000,
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Get the list of already added document types
  const addedDocumentTypes = documents.map((doc) => doc.name);

  // Check if driver's license has been added
  const hasDriversLicense = addedDocumentTypes.includes("DRIVER");

  if (isSuccess) {
    return (
      <main className="mx-auto max-w-4xl">
        <Card className="mx-auto max-w-4xl">
          <CardContent className="text-center">
            <div className="mb-6">
              <CheckCircleIcon className="mx-auto mb-4 h-16 w-16 text-green-500" />
              <h2 className="mb-2 text-2xl font-bold sm:text-3xl">
                Upload Successful!
              </h2>
              <p className="mb-6 text-gray-600">
                {client?.name ? `${client.name}, your` : "Your"} documents have
                been successfully uploaded and are being processed.
              </p>

              <div className="mt-6 border-t border-gray-200 pt-6">
                <h3 className="mb-4 text-lg font-semibold">
                  Uploaded Documents
                </h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                  {documents.map((doc) => (
                    <Card key={doc.name} className="overflow-hidden bg-gray-50">
                      <CardContent className="flex items-center p-4">
                        <FileIcon className="mr-3 h-8 w-8 shrink-0 text-blue-500" />
                        <div>
                          <h4 className="text-sm font-medium">
                            {getDocumentDisplayName(doc.name)}
                          </h4>
                          <p className="text-xs text-gray-500">
                            {doc.file.name}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
            <Button className="mt-4">
              <Link href="/" className="inline-flex items-center">
                <HomeIcon className="mr-2 h-4 w-4" /> Go Home
              </Link>
            </Button>
          </CardContent>
        </Card>
      </main>
    );
  }

  return (
    <div className="mx-auto max-w-4xl px-4 sm:px-6">
      <div className="mb-6 text-center sm:mb-8">
        <h1 className="mb-2 text-2xl font-bold sm:text-3xl">
          Hi, {client?.name}
        </h1>
        {isVerified ? (
          <p className="text-sm text-gray-600 sm:text-base">
            Thank you for submitting your identification documents. Your vehicle
            hiring process is ready to proceed.
          </p>
        ) : (
          <p className="text-sm text-gray-600 sm:text-base">
            Please upload your identification documents to ensure a smooth
            vehicle hiring process.
          </p>
        )}
      </div>

      <Card>
        <CardContent>
          {isVerified ? (
            // Already verified view
            <div className="py-8">
              <div className="mb-6 text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-green-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <h2 className="mb-2 text-xl font-semibold text-gray-800">
                  Documents Verified
                </h2>
                <p className="mx-auto max-w-md text-gray-600">
                  Your identification documents have been successfully verified.
                  This will help us process your vehicle hire more efficiently.
                  You&apos;re all set to continue with your booking.
                </p>
              </div>

              <div className="mb-6 rounded-lg bg-gray-50 p-4">
                <h3 className="mb-2 font-medium text-gray-700">
                  Submitted Documents
                </h3>
                <ul className="space-y-2">
                  {client?.clientDocs.map((doc) => (
                    <li
                      key={doc.id}
                      className="flex items-center rounded-md p-2 text-sm hover:bg-gray-100"
                    >
                      <FileIcon className="mr-2 h-4 w-4 text-blue-500" />
                      <span>{getDocumentDisplayName(doc.name)}</span>
                    </li>
                  ))}
                  {!client?.clientDocs?.length && (
                    <li className="text-sm text-gray-500">
                      No documents found
                    </li>
                  )}
                </ul>
              </div>

              <div className="text-center">
                <p className="mb-4 text-sm text-gray-500">
                  Need to update your documents or have questions?
                </p>
                <Button variant="outline" asChild>
                  <Link href="/contact-us">Contact Support</Link>
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <h2 className="text-lg font-semibold sm:text-xl">
                  Your Documents
                </h2>
                <Button
                  onClick={() => setIsModalOpen(true)}
                  className="w-full sm:w-auto"
                >
                  <PlusCircleIcon className="mr-2 h-4 w-4" /> Add Document
                </Button>
              </div>

              {/* Minimum requirement notice */}
              <div className="mb-6 flex items-start gap-2 rounded-md border border-amber-100 bg-amber-50 p-3 text-amber-800">
                <InfoIcon className="mt-0.5 h-5 w-5 shrink-0" />
                <p className="text-sm">
                  <span className="font-medium">Minimum requirement:</span> At a
                  minimum, we only need your Driver&apos;s License. Other
                  documents are optional.
                </p>
              </div>

              {documents.length === 0 ? (
                <div className="rounded-lg border-2 border-dashed border-gray-200 py-8 text-center sm:py-12">
                  <PlusCircleIcon className="mx-auto h-10 w-10 text-gray-400 sm:h-12 sm:w-12" />
                  <p className="mt-2 text-sm text-gray-500 sm:text-base">
                    No documents added yet
                  </p>
                  <p className="mx-auto mt-1 mb-4 max-w-md text-xs text-gray-500">
                    Please add your Driver&apos;s License to proceed. Additional
                    documents are optional.
                  </p>
                  <Button
                    variant="outline"
                    className="mt-2"
                    onClick={() => setIsModalOpen(true)}
                  >
                    Add Document
                  </Button>
                </div>
              ) : (
                <>
                  <div className="mb-6 grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4 lg:grid-cols-3">
                    {documents.map((doc) => (
                      <Card key={doc.name} className="overflow-hidden">
                        <CardContent className="p-3 sm:p-4">
                          <div className="flex flex-col items-center">
                            <FileIcon className="mb-2 h-10 w-10 text-blue-500 sm:h-12 sm:w-12" />
                            <h3 className="text-center text-sm font-medium sm:text-base">
                              {getDocumentDisplayName(doc.name)}
                            </h3>
                            <p className="mb-1 text-center text-xs text-gray-500 sm:text-sm">
                              {doc.file.name}
                            </p>

                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-xs text-red-500 hover:bg-red-50 hover:text-red-700 sm:text-sm"
                              onClick={() => handleRemoveDocument(doc.name)}
                            >
                              <Trash2Icon className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />{" "}
                              Remove
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
                    {!hasDriversLicense && (
                      <p className="text-sm text-amber-600">
                        <InfoIcon className="mr-1 inline-block h-4 w-4" />
                        Please add your Driver&apos;s License to proceed
                      </p>
                    )}
                    <div
                      className={`${!hasDriversLicense ? "w-full sm:w-auto" : "flex w-full justify-end"}`}
                    >
                      <Button
                        onClick={handleUploadDocuments}
                        className="w-full sm:w-auto"
                        disabled={!hasDriversLicense || isUploading}
                      >
                        <UploadIcon className="mr-2 h-4 w-4" /> Upload Documents
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </>
          )}
          <PrivacyDisclaimer />
        </CardContent>
      </Card>

      <DocumentUploadModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAddDocument={handleAddDocument}
        addedDocumentTypes={addedDocumentTypes}
      />
    </div>
  );
}

// Privacy disclaimer component to be reused
function PrivacyDisclaimer() {
  return (
    <Alert className="mt-6 border-blue-100 bg-blue-50 text-blue-800">
      <Info className="h-4 w-4" />
      <AlertTitle>Privacy Notice</AlertTitle>
      <AlertDescription className="text-blue-800">
        <p>
          We value your privacy and confidentiality. Your documents will be
          processed in line with GDPR and international policies. All
          information is handled in accordance with the Cyber and Data
          Protection Act, 2021.
        </p>
        <p className="mt-2">
          For more information, please visit our{" "}
          <Link
            href="/privacy-policy"
            className="font-medium underline hover:text-blue-600"
          >
            privacy policy
          </Link>{" "}
          page.
        </p>
      </AlertDescription>
    </Alert>
  );
}
