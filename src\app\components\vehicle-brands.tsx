// import { carBrands } from "@/app/data";
// import Image from "next/image";

// export default function CarBrands() {
//   return (
//     <section className="bg-primary bg-car-brands bg-cover bg-no-repeat">
//       <div className="container-car-brands container flex flex-col items-center justify-between py-8 md:flex-row lg:py-10">
//         {carBrands.map((brand, index) => (
//           <div
//             className="flex h-20 w-20 items-center justify-center text-white opacity-50"
//             key={index}
//           >
//             <Image
//               src={brand}
//               alt="car brand"
//               className="duration-400 object-cover opacity-20 brightness-0 invert transition-all ease-in-out hover:opacity-100"
//               width={80}
//               height={80}
//             />
//           </div>
//         ))}
//       </div>
//     </section>
//   );
// }
