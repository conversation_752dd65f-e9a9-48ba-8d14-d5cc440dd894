"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { Menu } from "lucide-react";
import { cn } from "@/common/lib/shadcn-utils";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/common/components/ui/sheet";
import { Button } from "@/common/components/ui/button";
import { UserNav } from "@/app/components/user-nav";
import { useBrandConfig } from "@/common/hooks/use-brand-config";
import UserAuthButtons from "@/app/components/user-auth-buttons";
import { CurrencySelectorNav } from "@/app/components/currency-selector-nav";

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();
  const session = useSession();
  const brandConfig = useBrandConfig();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={`fixed top-0 right-0 left-0 z-50 border-b-2 transition-all duration-300 ${
        isScrolled
          ? "border-gray-200 bg-white/70 shadow-md backdrop-blur-md"
          : "border-secondary bg-white"
      }`}
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center md:justify-between">
          {/* Logo */}
          <div className="flex flex-1 items-center justify-between md:flex-initial">
            <Link href="/" className="-mt-2 shrink-0">
              <Image
                             src={brandConfig.logo.nav}                alt={`${brandConfig.brandName} logo`}
                className="w-28 object-cover"
                width={80}
                height={26}
              />
            </Link>
            <div className="md:hidden">
              {session?.status === "authenticated" && <UserNav />}
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-8">
            {brandConfig.links.primary.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className={cn(
                  "text-sm transition-all duration-300",
                  pathname === link.href
                    ? "text-nav-foreground font-bold"
                    : "text-nav-foreground/70 hover:text-nav-foreground",
                )}
              >
                {link.name}
              </Link>
            ))}
          </div>

          {/* User Navigation and Mobile Menu */}
          <div className="hidden items-center space-x-4 md:flex">
            {/* Currency Selector */}
            <CurrencySelectorNav variant="compact" />

            {session?.status === "authenticated" ? (
              <UserNav />
            ) : (
              <div className="flex items-center space-x-4">
                <Button variant="outline" asChild>
                  <Link href="/login">Log in</Link>
                </Button>
                <Button asChild variant="secondary">
                  <Link href="/signup">Sign up</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-foreground"
                  aria-label="Open Menu"
                >
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-full p-0 sm:w-[400px]">
                <div className="flex h-full flex-col bg-white">
                  <div className="border-muted border-b p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Link
                          href="/"
                          className="block"
                          onClick={() => setIsOpen(false)}
                        >
                          <Image
                src={brandConfig.logo.nav}                            alt={`${brandConfig.brandName} logo`}
                            className="w-28 object-cover"
                            width={80}
                            height={26}
                          />
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 overflow-auto">
                    <div className="flex flex-col space-y-4 p-4">
                      {brandConfig.links.primary.map((link, index) => (
                        <Link
                          key={index}
                          href={link.href}
                          className={cn(
                            "border-b py-2",
                            pathname === link.href
                              ? "border-secondary text-secondary font-bold"
                              : "border-muted text-secondary/70 hover:text-secondary",
                          )}
                          onClick={() => setIsOpen(false)}
                        >
                          {link.name}
                        </Link>
                      ))}

                      {/* {session?.status !== "authenticated" && (
                        <div className="mt-4 flex flex-col space-y-2">
                          <Button variant="outline" className="w-full" asChild>
                            <Link
                              href="/login"
                              onClick={() => setIsOpen(false)}
                            >
                              Log in
                            </Link>
                          </Button>
                          <Button className="w-full" asChild>
                            <Link
                              href="/signup"
                              onClick={() => setIsOpen(false)}
                            >
                              Sign up
                            </Link>
                          </Button>
                        </div>
                      )} */}
                    </div>
                  </div>

                  <div className="border-muted flex flex-col gap-4 border-t p-4">
                    {/* Currency Selector for Mobile */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Currency</span>
                      <CurrencySelectorNav variant="compact" />
                    </div>
                    <UserAuthButtons onClick={() => setIsOpen(false)} />
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
