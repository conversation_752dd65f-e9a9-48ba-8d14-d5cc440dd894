/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  "/api/v1/": {
    get: operations["getSettlementsByFiltersUsingGET"];
  };
  "/api/v1/admin-billing-shifts": {
    get: operations["findAdminBillingUsingGET"];
  };
  "/api/v1/admin-stats": {
    get: operations["findAllAgencyShiftsByStatusUsingGET"];
  };
  "/api/v1/agencies": {
    get: operations["findAgenciesUsingGET"];
  };
  "/api/v1/agencies/default/{page}/{size}": {
    get: operations["findDefaultAgenciesUsingGET"];
  };
  "/api/v1/agencies/{page}/{size}": {
    get: operations["findAllUsingGET"];
  };
  "/api/v1/agency": {
    put: operations["updateUsingPUT"];
    post: operations["createUsingPOST"];
  };
  "/api/v1/agency-activate/{id}": {
    put: operations["activateAgencyUsingPUT"];
  };
  "/api/v1/agency-applicants/{agencyId}/{assignmentCodeName}/{page}/{size}": {
    get: operations["findAllWorkersByCodeUsingGET"];
  };
  "/api/v1/agency-applicants/{agencyId}/{page}/{size}": {
    get: operations["findAllApplicantsUsingGET"];
  };
  "/api/v1/agency-client/{payerId}/{agencyId}": {
    put: operations["updateClientUsingPUT"];
  };
  "/api/v1/agency-clients/{agencyId}/{page}/{size}": {
    get: operations["findAllClientsUsingGET"];
  };
  "/api/v1/agency-dashboard": {
    get: operations["findNumberOfAgenciesUsingGET"];
  };
  "/api/v1/agency-deactivate": {
    put: operations["deActivateAgencyUsingPUT"];
  };
  "/api/v1/agency-search": {
    post: operations["searchAgencyUsingPOST"];
  };
  "/api/v1/agency-search/{page}/{size}": {
    post: operations["searchAgencyPathVariableUsingPOST"];
  };
  "/api/v1/agency-settings": {
    get: operations["getAgencySettingsUsingGET"];
    post: operations["createUsingPOST_2"];
  };
  "/api/v1/agency-settings/{id}": {
    put: operations["updateUsingPUT_2"];
  };
  "/api/v1/agency-shifts-dashboard/{agencyId}": {
    get: operations["findAllAgencyShiftsByStatusUsingGET_1"];
  };
  "/api/v1/agency-shifts-released/{agencyId}/{page}/{size}": {
    get: operations["findShiftsByClientIdUsingGET"];
  };
  "/api/v1/agency-shifts-status/{agencyId}/{page}/{size}/{status}": {
    get: operations["findShiftsByAgencyIdUsingGET"];
  };
  "/api/v1/agency-shifts/{agencyId}/{page}/{size}": {
    get: operations["findShiftsByAgencyIdAndStatusUsingGET"];
  };
  "/api/v1/agency-stats/{id}": {
    get: operations["getAgencyStatsUsingGET"];
  };
  "/api/v1/agency-trainings/{id} ": {
    get: operations["getForAgencyUsingGET"];
  };
  "/api/v1/agency-worker-properties": {
    post: operations["createorUpdateAgencyWorkerPropertiesUsingPOST"];
  };
  "/api/v1/agency-worker-properties/activate-applicant": {
    put: operations["activateApplicantUsingPUT"];
  };
  "/api/v1/agency-worker-properties/activate-worker": {
    put: operations["activateWorkerUsingPUT"];
  };
  "/api/v1/agency-worker-properties/deactivate-applicant": {
    put: operations["deactivateApplicantUsingPUT"];
  };
  "/api/v1/agency-worker-properties/deactivate-worker": {
    put: operations["deactivateWorkerUsingPUT"];
  };
  "/api/v1/agency-worker-properties/{id}": {
    delete: operations["deleteAgencyWorkerPropertiesUsingDELETE"];
  };
  "/api/v1/agency-worker-properties/{workerId}/{agencyId}": {
    get: operations["findAgencyWorkerPropertiesUsingGET"];
  };
  "/api/v1/agency-workers/pending-shifts/{agencyId}/{page}/{size}": {
    get: operations["findAllWorkersPendingShiftsUsingGET"];
  };
  "/api/v1/agency-workers/{agencyId}/{page}/{size}": {
    get: operations["findAllWorkersUsingGET"];
  };
  "/api/v1/agency/bank-details/{agencyId}": {
    put: operations["updateBankDetailsUsingPUT"];
  };
  "/api/v1/agency/expense-rate": {
    put: operations["updateUsingPUT_1"];
    post: operations["createUsingPOST_1"];
  };
  "/api/v1/agency/expense-rate/{agencyId}": {
    get: operations["findAgencyExpenseRatesUsingGET"];
  };
  "/api/v1/agency/invite-worker": {
    post: operations["inviteWorkerUsingPOST"];
  };
  "/api/v1/agency/logo": {
    post: operations["uploadProfileImageUsingPOST"];
  };
  "/api/v1/agency/transporter/{id}": {
    get: operations["findByIsTransporterUsingGET"];
  };
  "/api/v1/agency/{id}": {
    get: operations["findByIdUsingGET_1"];
    delete: operations["deleteUsingDELETE"];
  };
  "/api/v1/applicant-search/agency/{agencyId}/{page}/{size}": {
    get: operations["searchApplicantUsingGET"];
  };
  "/api/v1/applicants/{shiftId}/{agencyId}": {
    get: operations["getAgencyApplicantsUsingGET"];
  };
  "/api/v1/apply-shift/{shiftId}/{workerId}/{agencyId}": {
    put: operations["applyUsingPUT"];
  };
  "/api/v1/approve-applied-shift/{shiftId}/{workerId}/": {
    put: operations["bookUsingPUT"];
  };
  "/api/v1/assignment-code": {
    put: operations["updateUsingPUT_3"];
    post: operations["createUsingPOST_3"];
  };
  "/api/v1/assignment-code-agency/{id}": {
    get: operations["findByAgencyIdUsingGET"];
  };
  "/api/v1/assignment-code-client/{id}": {
    get: operations["findByClientIdUsingGET"];
  };
  "/api/v1/assignment-code-rate": {
    put: operations["updateUsingPUT_4"];
    post: operations["createUsingPOST_5"];
  };
  "/api/v1/assignment-code-rate-multiple": {
    post: operations["createUsingPOST_4"];
  };
  "/api/v1/assignment-code-rate/{id}": {
    get: operations["findByIdUsingGET_5"];
    delete: operations["deleteUsingDELETE_2"];
  };
  "/api/v1/assignment-code-rates": {
    get: operations["findByIdUsingGET_7"];
  };
  "/api/v1/assignment-code-rates-agency/{payeeId}": {
    get: operations["findByByAgentIdUsingGET"];
  };
  "/api/v1/assignment-code-rates-client/{payerId}": {
    get: operations["findByByClientIdUsingGET"];
  };
  "/api/v1/assignment-code-rates/{page}/{size}": {
    get: operations["findByIdUsingGET_6"];
  };
  "/api/v1/assignment-code/{id}": {
    get: operations["findByIdUsingGET_2"];
    delete: operations["deleteUsingDELETE_1"];
  };
  "/api/v1/assignment-codes": {
    get: operations["findByIdUsingGET_4"];
  };
  "/api/v1/assignment-codes/{code}": {
    get: operations["findByCodeUsingGET"];
  };
  "/api/v1/assignment-codes/{page}/{size}": {
    get: operations["findByIdUsingGET_3"];
  };
  "/api/v1/authorize-queried-shift/{shiftId}/{payerId}/{endTime}": {
    put: operations["authorizeQueriedUsingPUT"];
  };
  "/api/v1/authorize-shift/{shiftId}": {
    put: operations["authorizeUsingPUT"];
  };
  "/api/v1/availability": {
    post: operations["updateAvailabilityUsingPOST"];
  };
  "/api/v1/availability/{id}": {
    delete: operations["deleteAvailabiltyUsingDELETE"];
  };
  "/api/v1/availability/{workerId}/{page}/{size}": {
    get: operations["findWorkerAvailabilityUsingGET"];
  };
  "/api/v1/bank": {
    post: operations["createUsingPOST_6"];
  };
  "/api/v1/bank/worker/{id}": {
    get: operations["findByWorkerIdUsingGET"];
  };
  "/api/v1/bank/{id}": {
    get: operations["findByIdUsingGET_8"];
    delete: operations["deleteUsingDELETE_3"];
  };
  "/api/v1/billing/debit-note/create": {
    post: operations["createDebitNoteUsingPOST_1"];
  };
  "/api/v1/billing/debit-note/status": {
    put: operations["SetDebitNotePaidStatusUsingPUT_1"];
  };
  "/api/v1/billing/pending/{page}/{size}": {
    post: operations["listAllBillsByStatusUsingPOST_1"];
  };
  "/api/v1/billing/{id}": {
    get: operations["findByIdUsingGET_11"];
  };
  "/api/v1/billing/{page}/{size}": {
    get: operations["listAllBillsUsingGET_1"];
  };
  "/api/v1/book-shift/{shiftId}/{workerId}/{agencyId}": {
    put: operations["bookUsingPUT_1"];
  };
  "/api/v1/broadcast": {
    post: operations["createUsingPOST_7"];
  };
  "/api/v1/broadcast-attachment": {
    post: operations["createWithAttachemntUsingPOST"];
  };
  "/api/v1/cancel-shift-by-worker/{shiftId}/{workerId}/{reason}": {
    put: operations["cancelWorkerShiftUsingPUT"];
  };
  "/api/v1/cancel-shift/{shiftId}/{payerId}/{reason}": {
    put: operations["cancelUsingPUT"];
  };
  "/api/v1/charge-rate": {
    get: operations["findAllUsingGET_1"];
    put: operations["updateUsingPUT_5"];
    post: operations["createUsingPOST_8"];
  };
  "/api/v1/charge-rate/{id}": {
    get: operations["findByIdUsingGET_9"];
    delete: operations["deleteUsingDELETE_5"];
  };
  "/api/v1/charge-rate/{page}/{size}": {
    get: operations["findByIdUsingGET_10"];
  };
  "/api/v1/chat-groups/group-messages/{shiftId}/{workerId}": {
    get: operations["findChatGroupMessagesByShiftIdAndWorkerIdUsingGET"];
  };
  "/api/v1/chat-groups/groupId/{groupId}": {
    get: operations["findChatGroupMessagesByChatGroupIdUsingGET"];
  };
  "/api/v1/chat-groups/{shiftId}": {
    get: operations["findChatGroupByShiftIdUsingGET"];
  };
  "/api/v1/chat-groups/{shiftId}/{workerId}": {
    get: operations["findChatGroupByShiftIdAndWorkerIdUsingGET"];
  };
  "/api/v1/client": {
    put: operations["updateUsingPUT_6"];
    post: operations["createUsingPOST_9"];
  };
  "/api/v1/client-admin/{id}": {
    get: operations["getClientAdminUsingGET"];
  };
  "/api/v1/client-agencies/{clientId}/{page}/{size}": {
    get: operations["findAllAgenciesUsingGET"];
  };
  "/api/v1/client-counters/{id}": {
    get: operations["getClientAgenciesUsingGET"];
  };
  "/api/v1/client-dashboard": {
    get: operations["findNumberOfClientsUsingGET"];
  };
  "/api/v1/client-link/{payerId}/{payeeId}": {
    put: operations["linkAgentToClientUsingPUT"];
  };
  "/api/v1/client-search/{page}/{size}": {
    post: operations["searchClientUsingPOST"];
  };
  "/api/v1/client-shifts": {
    post: operations["findShiftsByClientIdUsingPOST"];
  };
  "/api/v1/client-shifts-dashboard/{clientId}": {
    get: operations["findAllClientShiftsByStatusUsingGET"];
  };
  "/api/v1/client-stats/{id}": {
    get: operations["getClientStatsUsingGET"];
  };
  "/api/v1/client-workers/{payerId}/{page}/{size}": {
    get: operations["getClientWorkersUsingGET"];
  };
  "/api/v1/client/agency/search/{agencyId}/{searchCriteria}/{page}/{size}": {
    get: operations["searchAgencyClientsUsingGET"];
  };
  "/api/v1/client/docs": {
    post: operations["addClientDocsUsingPOST"];
  };
  "/api/v1/client/profile-image": {
    post: operations["uploadProfileImageUsingPOST_1"];
  };
  "/api/v1/client/{id}": {
    get: operations["findByIdUsingGET_12"];
    delete: operations["deleteUsingDELETE_6"];
  };
  "/api/v1/clients": {
    get: operations["findAllClientsUsingGET_1"];
  };
  "/api/v1/clients/{page}/{size}": {
    get: operations["findByIdUsingGET_13"];
  };
  "/api/v1/compliance": {
    get: operations["findAllUsingGET_2"];
    put: operations["updateUsingPUT_7"];
    post: operations["createUsingPOST_10"];
  };
  "/api/v1/compliance/{id}": {
    get: operations["findByIdUsingGET_14"];
    delete: operations["deleteUsingDELETE_7"];
  };
  "/api/v1/compliance/{page}/{size}": {
    get: operations["findAllPagedUsingGET"];
  };
  "/api/v1/create-payment-intent": {
    post: operations["chargeUsingPOST"];
  };
  "/api/v1/deputy/shift": {
    post: operations["createUsingPOST_11"];
  };
  "/api/v1/device": {
    post: operations["registerWorkerDeviceUsingPOST"];
  };
  "/api/v1/direct-book-shift/{shiftId}/{workerId}": {
    put: operations["bookWorkerDirectlyUsingPUT"];
  };
  "/api/v1/expense-rate": {
    get: operations["findAllUsingGET_3"];
    put: operations["updateUsingPUT_8"];
    post: operations["createUsingPOST_12"];
  };
  "/api/v1/expense-rate/{id}": {
    get: operations["findByIdUsingGET_15"];
    delete: operations["deleteUsingDELETE_8"];
  };
  "/api/v1/expense-rate/{page}/{size}": {
    get: operations["findExpenseRatesUsingGET"];
  };
  "/api/v1/files/download/{fileName}": {
    get: operations["downloadFileUsingGET"];
  };
  "/api/v1/files/upload": {
    post: operations["uploadFileUsingPOST"];
  };
  "/api/v1/invoice/admin-agency/view": {
    get: operations["getAgencyInvoicesByAdminUsingGET"];
  };
  "/api/v1/invoice/admin-agency/view-reports": {
    get: operations["getAgencyInvoiceReportsByAdminUsingGET"];
  };
  "/api/v1/invoice/admin-billing/agency/{agencyId}": {
    post: operations["generateAgencyAdminBillsUsingPOST"];
  };
  "/api/v1/invoice/admin/view": {
    get: operations["getAdminInvoicesUsingGET"];
  };
  "/api/v1/invoice/admin/{invoiceId}": {
    delete: operations["deleteAdminInvoiceUsingDELETE"];
  };
  "/api/v1/invoice/agency/acknowledge": {
    put: operations["acknowledgeUsingPUT"];
  };
  "/api/v1/invoice/agency/create": {
    post: operations["createAgencynvoiceUsingPOST"];
  };
  "/api/v1/invoice/agency/discount": {
    put: operations["setDiscountUsingPUT"];
  };
  "/api/v1/invoice/agency/publish": {
    put: operations["publishUsingPUT"];
  };
  "/api/v1/invoice/agency/view": {
    get: operations["getAgencyInvoicesUsingGET"];
  };
  "/api/v1/invoice/agency/view-reports": {
    get: operations["getAgencyInvoiceReportsUsingGET"];
  };
  "/api/v1/invoice/client/create": {
    post: operations["createClientInvoiceUsingPOST"];
  };
  "/api/v1/invoice/client/view": {
    get: operations["getClientInvoicesUsingGET"];
  };
  "/api/v1/invoice/client/view/invoice": {
    get: operations["getClientInvoiceUsingGET"];
  };
  "/api/v1/invoice/download": {
    get: operations["downloadInvoiceUsingGET"];
  };
  "/api/v1/invoice/trainer/agency/{agencyId}/{page}/{size}": {
    get: operations["getTrainerAgencyInvoicesUsingGET"];
  };
  "/api/v1/invoice/trainer/invoice-trainings": {
    post: operations["createAgencyTrainingsInvoiceUsingPOST"];
  };
  "/api/v1/invoice/trainer/worker/{agencyId}/{page}/{size}": {
    get: operations["getTrainerWorkerInvoicesUsingGET"];
  };
  "/api/v1/invoice/worker/{workerId}/{page}/{size}": {
    get: operations["getWorkerInvoicesUsingGET"];
  };
  "/api/v1/invoice/{id}": {
    get: operations["findByIdUsingGET_16"];
  };
  "/api/v1/invoice/{invoiceId}": {
    delete: operations["deleteUsingDELETE_9"];
  };
  "/api/v1/invoices/agency/settlement": {
    get: operations["getPaidInvoicesDueForSettlementUsingGET"];
  };
  "/api/v1/invoices/{carRentalId}/settle": {
    post: operations["settleInvoicesUsingPOST"];
  };
  "/api/v1/location": {
    put: operations["updateUsingPUT_9"];
    post: operations["createUsingPOST_13"];
  };
  "/api/v1/location/{id}": {
    get: operations["findByIdUsingGET_17"];
    delete: operations["deleteUsingDELETE_10"];
  };
  "/api/v1/locations": {
    get: operations["findAllLocationsUsingGET"];
  };
  "/api/v1/locations/search/{query}": {
    get: operations["searchUsingGET"];
  };
  "/api/v1/locations/{page}/{size}": {
    get: operations["findByIdUsingGET_18"];
  };
  "/api/v1/memory-status": {
    get: operations["getMemoryStatisticsUsingGET"];
  };
  "/api/v1/note": {
    post: operations["createNoteUsingPOST"];
  };
  "/api/v1/note/{id}": {
    delete: operations["deleteNoteUsingDELETE"];
  };
  "/api/v1/note/{workerId}/{page}/{size}": {
    get: operations["findWorkerNotesUsingGET"];
  };
  "/api/v1/notification/{id}": {
    delete: operations["deleteUsingDELETE_4"];
  };
  "/api/v1/notifications/{workerId}/{page}/{size}": {
    get: operations["getWorkerNotificationsUsingGET"];
  };
  "/api/v1/online-trainings/certificate/{enrolmentId}": {
    get: operations["getCertificateUsingGET"];
  };
  "/api/v1/online-trainings/register-worker/{workerId}": {
    post: operations["registerToIHascoUsingPOST"];
  };
  "/api/v1/pay-advice/{id}": {
    delete: operations["deleteUsingDELETE_11"];
  };
  "/api/v1/payAdvice/agency-worker/view": {
    get: operations["getWorkerPayAdvicesUsingGET"];
  };
  "/api/v1/payAdvice/agency/acknowledge": {
    put: operations["acknowledgeUsingPUT_1"];
  };
  "/api/v1/payAdvice/agency/view": {
    get: operations["getAgencyPayAdvicesUsingGET"];
  };
  "/api/v1/payAdvice/download": {
    get: operations["downloadPayAdviceUsingGET"];
  };
  "/api/v1/payAdvice/worker/create": {
    post: operations["createWorkerPayAdviceUsingPOST"];
  };
  "/api/v1/payAdvice/worker/send-pop": {
    put: operations["popUsingPUT"];
  };
  "/api/v1/payAdvice/worker/view": {
    get: operations["getWorkerPayAdvicesUsingGET_1"];
  };
  "/api/v1/payAdvice/worker/view/payadvice": {
    get: operations["getWorkerPayAdviceUsingGET"];
  };
  "/api/v1/payadvice/bacs-payment-csv/download/{date}/{agencyId}": {
    get: operations["downloadBacsPaymentCsvUsingGET"];
  };
  "/api/v1/payroll-workers/search/{agencyId}/{query}/{page}/{size}": {
    get: operations["searchAllAgencyWorkersforPaslipUsingGET"];
  };
  "/api/v1/payroll-workers/{agencyId}/{page}/{size}": {
    get: operations["findAllAgencyWorkersforPaslipUsingGET"];
  };
  "/api/v1/payslip-upload": {
    post: operations["uploadPayslipUsingPOST"];
  };
  "/api/v1/promo-code": {
    get: operations["findAllUsingGET_4"];
    put: operations["updateUsingPUT_10"];
    post: operations["createUsingPOST_14"];
  };
  "/api/v1/promo-code/activate/{id}": {
    put: operations["activateUsingPUT"];
  };
  "/api/v1/promo-code/{id}": {
    get: operations["findByIdUsingGET_19"];
    delete: operations["deleteUsingDELETE_12"];
  };
  "/api/v1/query-shift/{shiftId}/{workerId}/{reason}": {
    put: operations["queryUsingPUT"];
  };
  "/api/v1/redirect-to-payslip/{payslip}/{workerId}": {
    get: operations["findShiftsByWorkerIdAndStatusUsingGET"];
  };
  "/api/v1/release-shift/{shiftId}/{workerId}": {
    put: operations["releaseShiftUsingPUT"];
  };
  "/api/v1/remove-worker/{shiftId}/{workerId}": {
    put: operations["removeWorkerUsingPUT"];
  };
  "/api/v1/run-schedules": {
    get: operations["runSchedulerUsingGET"];
  };
  "/api/v1/sent-notifications/admin": {
    get: operations["findAdminSentUsingGET"];
  };
  "/api/v1/sent-notifications/agency/{id}": {
    get: operations["findAgencySentUsingGET"];
  };
  "/api/v1/sent-notifications/client/{id}": {
    get: operations["findClientSentUsingGET"];
  };
  "/api/v1/service": {
    put: operations["updateUsingPUT_11"];
    post: operations["createUsingPOST_15"];
  };
  "/api/v1/service/{id}": {
    get: operations["findByIdUsingGET_20"];
    delete: operations["deleteUsingDELETE_13"];
  };
  "/api/v1/services": {
    get: operations["findByIdUsingGET_22"];
  };
  "/api/v1/services/{page}/{size}": {
    get: operations["findByIdUsingGET_21"];
  };
  "/api/v1/shift": {
    put: operations["updateUsingPUT_12"];
    post: operations["createUsingPOST_16"];
  };
  "/api/v1/shift-applicants/{shiftId}": {
    get: operations["getApplicantsUsingGET"];
  };
  "/api/v1/shift-directorate": {
    put: operations["updateUsingPUT_13"];
    post: operations["createUsingPOST_17"];
  };
  "/api/v1/shift-directorate-by-location/{id}": {
    get: operations["findDirectorateByLocationByIdUsingGET"];
  };
  "/api/v1/shift-directorate/{id}": {
    get: operations["findByIdUsingGET_26"];
    delete: operations["deleteUsingDELETE_15"];
  };
  "/api/v1/shift-directorates-agency/{agencyId}/{page}/{size}": {
    get: operations["findByAgencyIdUsingGET_1"];
  };
  "/api/v1/shift-directorates-client/{payerId}/{page}/{size}": {
    get: operations["findByClientIdUsingGET_1"];
  };
  "/api/v1/shift-directorates/filter/": {
    get: operations["findByAllWithFilterUsingGET"];
  };
  "/api/v1/shift-directorates/{page}/{size}": {
    get: operations["findAllUsingGET_6"];
  };
  "/api/v1/shift-filter/{workerId}/{page}/{size}": {
    post: operations["searchShiftUsingPOST"];
  };
  "/api/v1/shift-type": {
    put: operations["updateUsingPUT_15"];
    post: operations["createUsingPOST_19"];
  };
  "/api/v1/shift-type-activate/{id}": {
    put: operations["activateShiftTypeUsingPUT"];
  };
  "/api/v1/shift-type-deactivate/{id}": {
    put: operations["deactivateShiftTypeUsingPUT"];
  };
  "/api/v1/shift-type/{id}": {
    get: operations["findByIdUsingGET_27"];
    delete: operations["deleteUsingDELETE_16"];
  };
  "/api/v1/shift-types": {
    get: operations["findByIdUsingGET_29"];
  };
  "/api/v1/shift-types/{page}/{size}": {
    get: operations["findByIdUsingGET_28"];
  };
  "/api/v1/shift/expense-claim": {
    put: operations["updateUsingPUT_14"];
    post: operations["createUsingPOST_18"];
  };
  "/api/v1/shift/expense-claim/{shiftIds}": {
    get: operations["findShiftExpenseClaimsUsingGET"];
  };
  "/api/v1/shift/pooling/workers/{shiftId}": {
    get: operations["carPoolingWorkerListUsingGET"];
  };
  "/api/v1/shift/worker/remind-authorisation/{id}": {
    put: operations["authorisationReminderUsingPUT"];
  };
  "/api/v1/shift/{id}": {
    get: operations["findByIdUsingGET_23"];
    delete: operations["deleteUsingDELETE_14"];
  };
  "/api/v1/shifts": {
    get: operations["findAllUsingGET_5"];
  };
  "/api/v1/shifts-clients-status/{page}/{size}/{payerId}/{status}": {
    get: operations["findByShiftStatusByClientIdUsingGET"];
  };
  "/api/v1/shifts-dashboard": {
    get: operations["findAllShiftsByStatusUsingGET"];
  };
  "/api/v1/shifts-status/{page}/{size}/{status}": {
    get: operations["findByIdUsingGET_24"];
  };
  "/api/v1/shifts/compliance/issues/{agencyId}/{page}/{size}": {
    get: operations["findShiftComplianceIssuesUsingGET"];
  };
  "/api/v1/shifts/job-count/{workerId}": {
    get: operations["findJobCountUsingGET"];
  };
  "/api/v1/shifts/{page}/{size}": {
    get: operations["findByIdUsingGET_25"];
  };
  "/api/v1/shifts/{page}/{size}/{payerId}/{status}": {
    get: operations["findByShiftStatusByClientUsingGET"];
  };
  "/api/v1/shifts/{page}/{size}/{status}": {
    get: operations["findByShiftStatusUsingGET"];
  };
  "/api/v1/stripe/webhook": {
    post: operations["handleStripeWebhookUsingPOST"];
  };
  "/api/v1/taxCode/{page}/{size}": {
    get: operations["findByIdUsingGET_30"];
  };
  "/api/v1/taxcode": {
    put: operations["updateUsingPUT_16"];
    post: operations["createUsingPOST_20"];
  };
  "/api/v1/taxcode/{id}": {
    get: operations["findByIdUsingGET_31"];
    delete: operations["deleteUsingDELETE_17"];
  };
  "/api/v1/taxcodes": {
    get: operations["findByIdUsingGET_32"];
  };
  "/api/v1/trainers": {
    get: operations["findTrainersUsingGET"];
  };
  "/api/v1/training": {
    get: operations["findAllUsingGET_7"];
    put: operations["updateUsingPUT_17"];
    post: operations["createUsingPOST_21"];
  };
  "/api/v1/training-booking/apply/{agencyId}/{trainingSessionId}/{workerId}": {
    put: operations["applyUsingPUT_2"];
  };
  "/api/v1/training-booking/approval/{bookingId}/{approveBooking}": {
    put: operations["applicationApprovalUsingPUT"];
  };
  "/api/v1/training-booking/auth-reminder/{id}": {
    post: operations["authorizationReminderUsingPOST"];
  };
  "/api/v1/training-booking/authorize": {
    put: operations["authorizeCompleteUsingPUT"];
  };
  "/api/v1/training-booking/book/{agencyId}/{trainingSessionId}/{workerIds}": {
    put: operations["bookUsingPUT_2"];
  };
  "/api/v1/training-booking/feedback": {
    get: operations["findPendingFeedbackUsingGET"];
  };
  "/api/v1/training-booking/show-certificate/{bookingId}/{show}": {
    put: operations["showCertificateUsingPUT"];
  };
  "/api/v1/training-booking/training-session/{trainingSessionId}": {
    get: operations["findByTrainingSessionUsingGET"];
  };
  "/api/v1/training-booking/{id}": {
    get: operations["findByIdUsingGET_44"];
    delete: operations["cancelUsingDELETE_1"];
  };
  "/api/v1/training-bookings/agency/{agencyId}/{status}/{page}/{size}": {
    get: operations["findForAgencyUsingGET_1"];
  };
  "/api/v1/training-bookings/trainer/{agencyId}/{status}/{page}/{size}": {
    get: operations["findForTrainerUsingGET_1"];
  };
  "/api/v1/training-feedback/booking/{id}": {
    get: operations["findByBookingUsingGET"];
  };
  "/api/v1/training-feedback/{bookingId}": {
    post: operations["createUsingPOST_22"];
  };
  "/api/v1/training-feedback/{id}": {
    get: operations["findByIdUsingGET_34"];
    delete: operations["deleteUsingDELETE_19"];
  };
  "/api/v1/training-session": {
    put: operations["updateUsingPUT_18"];
    post: operations["createUsingPOST_23"];
  };
  "/api/v1/training-session/agency/report/{agencyId}": {
    get: operations["findAgencySessionCountUsingGET"];
  };
  "/api/v1/training-session/trainer/report/{trainerId}": {
    get: operations["findTrainerSessionCountUsingGET"];
  };
  "/api/v1/training-session/{id}": {
    get: operations["findByIdUsingGET_35"];
    delete: operations["cancelUsingDELETE"];
  };
  "/api/v1/training-sessions/agency/{agencyId}/{status}/{page}/{size}": {
    get: operations["findForAgencyUsingGET"];
  };
  "/api/v1/training-sessions/trainer/billing/{trainerId}/{agencyId}/{page}/{size}": {
    get: operations["findForTrainerBillingUsingGET"];
  };
  "/api/v1/training-sessions/trainer/{agencyId}/{status}/{page}/{size}": {
    get: operations["findForTrainerUsingGET"];
  };
  "/api/v1/training-sessions/worker/trainings/{workerId}": {
    get: operations["availableTrainingsForWorkerUsingGET"];
  };
  "/api/v1/training-sessions/worker/{workerId}/{trainingId}": {
    get: operations["findForWorkerUsingGET"];
  };
  "/api/v1/training-sessions/{status}/{page}/{size}": {
    get: operations["findAllPagedUsingGET_2"];
  };
  "/api/v1/training/agency/{agencyId}/{trainingId}": {
    put: operations["addTrainingUsingPUT"];
  };
  "/api/v1/training/{id}": {
    get: operations["findByIdUsingGET_33"];
    delete: operations["deleteUsingDELETE_18"];
  };
  "/api/v1/training/{page}/{size}": {
    get: operations["findAllPagedUsingGET_1"];
  };
  "/api/v1/trainings-agency/{agencyId}": {
    get: operations["findAlwlPagedUsingGET"];
  };
  "/api/v1/transport": {
    put: operations["updateUsingPUT_19"];
    post: operations["createUsingPOST_24"];
  };
  "/api/v1/transport-booking/approve-booking/{workerTransportBookingId}": {
    put: operations["approveBookingUsingPUT"];
  };
  "/api/v1/transport-booking/authorization-reminder/{transportBookingId}": {
    put: operations["authorizationReminderUsingPUT"];
  };
  "/api/v1/transport-booking/authorize/{transportBookingId}": {
    put: operations["authorizeTransportBookingUsingPUT"];
  };
  "/api/v1/transport-booking/book/{workerId}/{specId}": {
    post: operations["bookWorkersDirectlyUsingPOST"];
  };
  "/api/v1/transport-booking/cancel/{transportBookingId}": {
    delete: operations["cancelTransportBookingUsingDELETE"];
  };
  "/api/v1/transport-booking/reject-booking/{transportBookingId}": {
    put: operations["rejectBookingUsingPUT"];
  };
  "/api/v1/transport-booking/{agencyId}/{page}/{size}": {
    get: operations["agencyTransportBookingsUsingGET"];
  };
  "/api/v1/transport-booking/{workerId}/{transportId}": {
    put: operations["applyUsingPUT_1"];
  };
  "/api/v1/transport/accept/{transportId}/{agencyId}": {
    put: operations["acceptUsingPUT"];
  };
  "/api/v1/transport/agency/dashboard/{agencyId}": {
    get: operations["findAllAgencyShiftsByStatusUsingGET_2"];
  };
  "/api/v1/transport/agency/{agencyId}/{status}/{page}/{size}": {
    get: operations["findByAgencyIdUsingGET_2"];
  };
  "/api/v1/transport/authorize": {
    put: operations["authorizeTransportUsingPUT"];
  };
  "/api/v1/transport/cleaning": {
    put: operations["updateCleaningCheckUsingPUT"];
  };
  "/api/v1/transport/client/dashboard/{clientId}": {
    get: operations["findAllClientShiftsByStatusUsingGET_1"];
  };
  "/api/v1/transport/client/{clientId}/{status}/{page}/{size}": {
    get: operations["findByClientIdUsingGET_2"];
  };
  "/api/v1/transport/commit/{transportId}": {
    put: operations["commitJobUsingPUT"];
  };
  "/api/v1/transport/legible-workers/{transportId}": {
    get: operations["getTransportLegibleWorkersUsingGET"];
  };
  "/api/v1/transport/logs/agency/dashboard/{agencyId}": {
    get: operations["findAllAgencyLogsShiftsByStatusUsingGET"];
  };
  "/api/v1/transport/mileage": {
    put: operations["updateMileageUsingPUT"];
  };
  "/api/v1/transport/pick-driver/{id}/{transportId}": {
    put: operations["pickDriverUsingPUT"];
  };
  "/api/v1/transport/pick-team-leader/{id}/{transportId}": {
    put: operations["pickTeamLeaderUsingPUT"];
  };
  "/api/v1/transport/rate-provider/{transportId}/{rating}": {
    put: operations["rateProviderUsingPUT"];
  };
  "/api/v1/transport/team-leader/entry": {
    put: operations["teamLeaderUpdateUsingPUT"];
  };
  "/api/v1/transport/upload-patient-document-one/{transportId}": {
    post: operations["uploadPatientDocumentOneUsingPOST"];
  };
  "/api/v1/transport/upload-patient-document-three/{transportId}": {
    post: operations["uploadPatientDocumentThreeUsingPOST"];
  };
  "/api/v1/transport/upload-patient-document-two/{transportId}": {
    post: operations["uploadPatientDocumentTwoUsingPOST"];
  };
  "/api/v1/transport/upload-risk-document/{transportId}": {
    post: operations["uploadRiskDocumentUsingPOST"];
  };
  "/api/v1/transport/vehicle-check": {
    put: operations["updateVehicleCheckUsingPUT"];
  };
  "/api/v1/transport/vehicle/{id}/{transportId}": {
    put: operations["transportVehicleUsingPUT"];
  };
  "/api/v1/transport/worker-times": {
    put: operations["teamLeaderUpdateUsingPUT_1"];
  };
  "/api/v1/transport/worker/driver/{driverId}/{status}/{page}/{size}": {
    get: operations["findByDriverUsingGET"];
  };
  "/api/v1/transport/worker/team-leader/{leaderId}/{status}/{page}/{size}": {
    get: operations["findByTeamLeaderUsingGET"];
  };
  "/api/v1/transport/{id}": {
    get: operations["findByIdUsingGET_36"];
    delete: operations["cancelJobUsingDELETE"];
  };
  "/api/v1/transport/{status}/{size}/{page}": {
    get: operations["findAllUsingGET_8"];
  };
  "/api/v1/upload-csv-file": {
    post: operations["downloadBacsPaymentCsvUsingPOST"];
  };
  "/api/v1/vat-rate": {
    get: operations["findAllUsingGET_9"];
    put: operations["updateUsingPUT_20"];
    post: operations["createUsingPOST_25"];
  };
  "/api/v1/vat-rate/{id}": {
    get: operations["findByIdUsingGET_37"];
    delete: operations["deleteUsingDELETE_20"];
  };
  "/api/v1/vat-rate/{page}/{size}": {
    get: operations["findByIdUsingGET_38"];
  };
  "/api/v1/vehicle": {
    put: operations["updateUsingPUT_22"];
    post: operations["createUsingPOST_27"];
  };
  "/api/v1/vehicle-booking": {
    put: operations["updateUsingPUT_21"];
    post: operations["createUsingPOST_26"];
  };
  "/api/v1/vehicle-booking/agency": {
    post: operations["createAsAgencyUsingPOST"];
  };
  "/api/v1/vehicle-booking/cancel/{bookingId}": {
    post: operations["cancelBookingUsingPOST"];
  };
  "/api/v1/vehicle-booking/client/rating/{vehicleBookingId}": {
    post: operations["rateClientForBookingUsingPOST"];
  };
  "/api/v1/vehicle-booking/client/{clientId}/{page}/{size}": {
    get: operations["findForClientUsingGET"];
  };
  "/api/v1/vehicle-booking/deposit": {
    post: operations["createDepositUsingPOST"];
  };
  "/api/v1/vehicle-booking/email/{id}/{email}": {
    get: operations["findByIdAndEmailUsingGET"];
  };
  "/api/v1/vehicle-booking/handover": {
    put: operations["handOverVehicleUsingPUT"];
  };
  "/api/v1/vehicle-booking/paynow-paid/{quoteId}": {
    get: operations["bookingPaidPaynowUsingGET"];
    put: operations["bookingPaidPaynowPutUsingPUT"];
    post: operations["bookingPaidPaynowPostUsingPOST"];
  };
  "/api/v1/vehicle-booking/quote": {
    post: operations["getQuoteUsingPOST"];
  };
  "/api/v1/vehicle-booking/rating/{vehicleBookingId}": {
    post: operations["rateVehicleBookingUsingPOST"];
  };
  "/api/v1/vehicle-booking/resend-email/{bookingId}/{emailType}": {
    post: operations["resendBookingEmailUsingPOST"];
  };
  "/api/v1/vehicle-booking/returnVehicle": {
    put: operations["returnVehicleUsingPUT"];
  };
  "/api/v1/vehicle-booking/vehicle/{id}/{status}/{page}/{size}": {
    get: operations["findVehicleLogsUsingGET"];
  };
  "/api/v1/vehicle-booking/{bookingId}/deposits": {
    get: operations["getDepositsByBookingIdUsingGET"];
  };
  "/api/v1/vehicle-booking/{id}": {
    get: operations["findByIdUsingGET_39"];
  };
  "/api/v1/vehicle-booking/{page}/{size}": {
    get: operations["findByAgencyIdUsingGET_3"];
  };
  "/api/v1/vehicle-log/authorize/{vehicleLogId}/{authorizer}": {
    put: operations["authorizeVehicleLogUsingPUT"];
  };
  "/api/v1/vehicle-log/comment/{vehicleLogId}/{comment}": {
    put: operations["commentVehicleLogUsingPUT"];
  };
  "/api/v1/vehicle-log/upload-damage-report/{logId}": {
    post: operations["uploadRiskDamageReportUsingPOST"];
  };
  "/api/v1/vehicle-logs/agency/{agencyId}/{status}/{page}/{size}": {
    get: operations["findByAgencyAndStatusUsingGET"];
  };
  "/api/v1/vehicle-logs/worker/{workerId}/{status}/{page}/{size}": {
    get: operations["findByWorkerAndStatusUsingGET"];
  };
  "/api/v1/vehicle/addons/document": {
    post: operations["addDocumentUsingPOST"];
  };
  "/api/v1/vehicle/addons/document/{id}": {
    delete: operations["deleteDocumentsUsingDELETE"];
  };
  "/api/v1/vehicle/addons/inventory": {
    post: operations["addInventoryUsingPOST"];
  };
  "/api/v1/vehicle/addons/inventory/{id}": {
    delete: operations["deleteInventoryUsingDELETE"];
  };
  "/api/v1/vehicle/addons/photo/{id}": {
    delete: operations["deletePhotoUsingDELETE"];
  };
  "/api/v1/vehicle/addons/photo/{vehicleId}": {
    post: operations["addPhotoUsingPOST"];
  };
  "/api/v1/vehicle/addons/set-main-photo/{id}": {
    put: operations["setMainPhotoUsingPUT"];
  };
  "/api/v1/vehicle/admin/{page}/{size}": {
    get: operations["findByAdminIdUsingGET"];
  };
  "/api/v1/vehicle/agency/find-available/{agencyId}/{page}/{size}": {
    get: operations["findAgencyAvailableVehiclesUsingGET"];
  };
  "/api/v1/vehicle/agency/type/{agencyId}/{page}/{size}": {
    get: operations["findByAgencyIdUsingGET_4"];
  };
  "/api/v1/vehicle/approve/{vehicleId}": {
    put: operations["approveVehicleUsingPUT"];
  };
  "/api/v1/vehicle/disable/{vehicleId}": {
    put: operations["disableVehicleUsingPUT"];
  };
  "/api/v1/vehicle/enable/{vehicleId}": {
    put: operations["enableVehicleUsingPUT"];
  };
  "/api/v1/vehicle/public-search/{page}/{size}": {
    get: operations["findPublicVehiclesUsingGET"];
  };
  "/api/v1/vehicle/rates": {
    put: operations["updateRatesUsingPUT"];
  };
  "/api/v1/vehicle/reject/{vehicleId}": {
    put: operations["rejectVehicleUsingPUT"];
  };
  "/api/v1/vehicle/search/filtered/{page}/{size}": {
    post: operations["getFilteredVehiclesUsingPOST"];
  };
  "/api/v1/vehicle/{id}": {
    get: operations["findByIdUsingGET_40"];
  };
  "/api/v1/vehicle/{id}/{status}/{page}/{size}": {
    get: operations["findVehicleLogsUsingGET_1"];
  };
  "/api/v1/vehicle/{vehicleId}/availability": {
    get: operations["getVehicleAvailabilityUsingGET"];
    post: operations["updateVehicleAvailabilityUsingPOST"];
  };
  "/api/v1/vehicle/{vehicleId}/availability/all": {
    delete: operations["clearAllVehicleAvailabilityUsingDELETE"];
  };
  "/api/v1/worker": {
    put: operations["updateUsingPUT_23"];
    post: operations["createUsingPOST_28"];
  };
  "/api/v1/worker-agencies/{workerId}/{page}/{size}": {
    get: operations["findAllAgenciesUsingGET_1"];
  };
  "/api/v1/worker-agency-shift-status/{workerId}/{page}/{size}/{status}": {
    get: operations["findShiftsByWorkerIdAndStatusUsingGET_1"];
  };
  "/api/v1/worker-agency-shift/billing/{workerId}/{agencyId}/{page}/{size}": {
    get: operations["findShiftsByWorkerIdforBillingUsingGET"];
  };
  "/api/v1/worker-agency-shift/{workerId}/{page}/{size}": {
    get: operations["findShiftsByClientIdUsingGET_1"];
  };
  "/api/v1/worker-agency/{workerId}/{page}/{size}": {
    get: operations["getMyAgenciesUsingGET"];
  };
  "/api/v1/worker-applied-shifts/{workerId}/{page}/{size}": {
    get: operations["findAppliedShiftsForWorkerIdUsingGET"];
  };
  "/api/v1/worker-banking/banking-upload": {
    post: operations["uploadGeneralSignatureUsingPOST"];
  };
  "/api/v1/worker-clients/{workerId}/{page}/{size}": {
    get: operations["findAllClientsUsingGET_2"];
  };
  "/api/v1/worker-compliance": {
    put: operations["responseWorkerComplianceUsingPUT"];
    post: operations["createWorkerComplianceUsingPOST"];
  };
  "/api/v1/worker-compliance/{id}": {
    delete: operations["deleteWorkerComplianceUsingDELETE"];
  };
  "/api/v1/worker-compliance/{workerId}/{agencyId}/{page}/{size}": {
    get: operations["findAgencyWorkerCompliancesUsingGET"];
  };
  "/api/v1/worker-compliance/{workerId}/{page}/{size}": {
    get: operations["findWorkerCompliancesUsingGET"];
  };
  "/api/v1/worker-dashboard": {
    get: operations["findNumberOfWorkersUsingGET"];
  };
  "/api/v1/worker-link/{workerId}/{agencyId}": {
    put: operations["linkAgencyToWorkerUsingPUT"];
  };
  "/api/v1/worker-pay-advices/{workerId}/{page}/{size}": {
    get: operations["getWorkerPayAdvicesUsingGET_2"];
  };
  "/api/v1/worker-payslips/{workerId}/{page}/{size}": {
    get: operations["getWorkerPayslipsUsingGET"];
  };
  "/api/v1/worker-search/agency/{agencyId}/{page}/{size}": {
    get: operations["searchWorkerUsingGET"];
  };
  "/api/v1/worker-search/client/{payerId}/{page}/{size}": {
    get: operations["searchClientWorkerUsingGET"];
  };
  "/api/v1/worker-search/{agencyId}/{page}/{size}": {
    get: operations["searchAllWorkersUsingGET"];
  };
  "/api/v1/worker-shifts-dashboard/{workerId}": {
    get: operations["findAllWorkerShiftsByStatusUsingGET"];
  };
  "/api/v1/worker-stats/{id}": {
    get: operations["getWorkerStatsUsingGET"];
  };
  "/api/v1/worker-training": {
    put: operations["responseWorkerTrainingUsingPUT"];
    post: operations["createWorkerTrainingUsingPOST"];
  };
  "/api/v1/worker-training/{id}": {
    get: operations["findWorkerTrainingUsingGET"];
    delete: operations["deleteWorkerTrainingUsingDELETE"];
  };
  "/api/v1/worker-training/{workerId}/{agencyId}/{page}/{size}": {
    get: operations["findAgencyWorkerTrainingsUsingGET"];
  };
  "/api/v1/worker-training/{workerId}/{page}/{size}": {
    get: operations["findWorkerTrainingsUsingGET"];
  };
  "/api/v1/worker/profile-image": {
    post: operations["uploadProfileImageUsingPOST_2"];
  };
  "/api/v1/worker/upload-compliance-doc": {
    post: operations["uploadComplianceDocUsingPOST"];
  };
  "/api/v1/worker/upload-training-doc": {
    post: operations["uploadTrainingDocUsingPOST"];
  };
  "/api/v1/worker/{id}": {
    get: operations["findByIdUsingGET_41"];
    delete: operations["deleteUsingDELETE_21"];
  };
  "/api/v1/workers": {
    get: operations["findByIdUsingGET_43"];
  };
  "/api/v1/workers-client/{page}/{size}": {
    get: operations["findClientWorkersUsingGET"];
  };
  "/api/v1/workers-under-agency/{assignmentCodeName}/{gender}": {
    post: operations["findAgencyWorkersUsingPOST"];
  };
  "/api/v1/workers/{page}/{size}": {
    get: operations["findByIdUsingGET_42"];
  };
  "/api/v1/worklink/billing/debit-note/create": {
    post: operations["createDebitNoteUsingPOST"];
  };
  "/api/v1/worklink/billing/debit-note/status": {
    put: operations["SetDebitNotePaidStatusUsingPUT"];
  };
  "/api/v1/worklink/billing/pending/{page}/{size}": {
    post: operations["listAllBillsByStatusUsingPOST"];
  };
  "/api/v1/worklink/billing/{id}": {
    get: operations["findByIdUsingGET"];
  };
  "/api/v1/worklink/billing/{page}/{size}": {
    get: operations["listAllBillsUsingGET"];
  };
}

export interface definitions {
  /** Address */
  Address: {
    county?: string;
    firstLine?: string;
    postcode?: string;
    secondLine?: string;
    town?: string;
  };
  /** AddressResultDTO */
  AddressResultDTO: {
    firstLine?: string;
    postcode?: string;
    town?: string;
  };
  /** AddressUpdateDto */
  AddressUpdateDto: {
    county?: string;
    firstLine?: string;
    postcode?: string;
    town?: string;
  };
  /** AdminStats */
  AdminStats: {
    /** Format: int32 */
    numberOfAgencies?: number;
    /** Format: int32 */
    numberOfClients?: number;
    /** Format: int32 */
    numberOfShifts?: number;
    /** Format: int32 */
    numberOfWorkers?: number;
    /** Format: int64 */
    vehicles?: number;
    /** Format: int64 */
    vehiclesPending?: number;
  };
  /** AdministratorCreateDto */
  AdministratorCreateDto: {
    adminEmail?: string;
    firstname?: string;
    lastname?: string;
  };
  /** Agency */
  Agency: {
    address?: definitions["Address"];
    /** @enum {string} */
    agencyType?: "DEFAULT" | "TRAINER" | "TRANSPORTER";
    bankDetails?: definitions["BankDetails"];
    billingEmail?: string;
    corrupted?: boolean;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    deputyEnabled?: boolean;
    deputyToken?: string;
    deputyUrl?: string;
    email?: string;
    /** Format: int64 */
    id?: number;
    isTrainer?: boolean;
    isTransporter?: boolean;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    logo?: string;
    name?: string;
    refPrefix?: string;
    service?: definitions["Services"];
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    telephone?: string;
    transporter?: boolean;
    /** Format: int64 */
    version?: number;
  };
  /** AgencyBillDto */
  AgencyBillDto: {
    agency?: string;
    /** Format: int64 */
    agentId?: number;
    billEmailAddress?: string;
    chargeRate?: number;
    client?: string;
    discountCharge?: number;
    /** Format: date-time */
    dueDate?: string;
    /** Format: int64 */
    id?: number;
    /** Format: date-time */
    issueDate?: string;
    notes?: string;
    paid?: boolean;
    /** Format: date-time */
    paidDate?: string;
    paymentRef?: string;
    /** Format: int64 */
    shiftId?: number;
    /** @enum {string} */
    status?: "PENDING" | "PAID";
    subTotal?: number;
    totalCharge?: number;
    totalDue?: number;
    totalUnits?: string;
    vatRate?: number;
    worker?: string;
  };
  /** AgencyBillStatusDto */
  AgencyBillStatusDto: {
    /** Format: int64 */
    billId?: number;
    paid?: boolean;
    paymentRef?: string;
  };
  /** AgencyCreateDto */
  AgencyCreateDto: {
    address?: definitions["Address"];
    administratorCreateDto?: definitions["AdministratorCreateDto"];
    /** @enum {string} */
    agencyType?: "DEFAULT" | "TRAINER" | "TRANSPORTER";
    billingEmail?: string;
    email?: string;
    logo?: string;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    telephone?: string;
  };
  /** AgencyExpenseRate */
  AgencyExpenseRate: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    expenseRate?: definitions["ExpenseRate"];
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    rate?: number;
    /** Format: int64 */
    version?: number;
  };
  /** AgencyExpenseRateDto */
  AgencyExpenseRateDto: {
    /** Format: int64 */
    agencyId?: number;
    /** Format: int64 */
    expenseRateId?: number;
    /** Format: int64 */
    id?: number;
    rate?: number;
  };
  /** AgencyList */
  AgencyList: {
    agencyId?: number[];
  };
  /** AgencyResultDto */
  AgencyResultDto: {
    address?: definitions["AddressResultDTO"];
    /** Format: int64 */
    agencyId?: number;
    /** @enum {string} */
    agencyType?: "DEFAULT" | "TRAINER" | "TRANSPORTER";
    bankDetails?: definitions["BankDetails"];
    billingEmail?: string;
    createdBy?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    email?: string;
    /** Format: int64 */
    id?: number;
    logo?: string;
    name?: string;
    service?: string;
    status?: string;
    telephone?: string;
    /** Format: int64 */
    totalVehicles?: number;
  };
  /** AgencySettings */
  AgencySettings: {
    /** Format: int64 */
    agencyId?: number;
    chargeVat?: boolean;
    /** Format: int64 */
    id?: number;
    vatPercentage?: number;
  };
  /** AgencySettingsCreateDto */
  AgencySettingsCreateDto: {
    /** Format: int64 */
    agencyId?: number;
    chargeVat?: boolean;
    vatPercentage?: number;
  };
  /** AgencyStats */
  AgencyStats: {
    /** Format: int32 */
    numberOfRegisteredClients?: number;
    /** Format: int32 */
    numberOfRegisteredWorkers?: number;
    /** Format: int32 */
    numberOfShifts?: number;
  };
  /** AgencyUpdateDto */
  AgencyUpdateDto: {
    address?: definitions["AddressUpdateDto"];
    /** Format: int64 */
    agencyId?: number;
    baseCurrency?: string;
    billingEmail?: string;
    email?: string;
    logo?: string;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
    telephone?: string;
  };
  /** AgencyWorkerProperties */
  AgencyWorkerProperties: {
    agency?: definitions["Agency"];
    approver?: string;
    comment?: string;
    /** Format: date */
    contractEndDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** Format: date */
    dbsExpiry?: string;
    dbsNumber?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    eligible?: boolean;
    /** Format: date */
    employmentStartDate?: string;
    /** Format: date */
    expiry?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: date */
    nextCheckDate?: string;
    paperwork?: boolean;
    paycycle?: string;
    paymentMethod?: string;
    position?: string;
    proof?: string;
    /** Format: int32 */
    rating?: number;
    /** Format: date */
    restrictionExpiry?: string;
    restrictions?: string;
    /** @enum {string} */
    rightToWork?: "RESIDENT" | "VISA";
    rtiId?: string;
    /** Format: date */
    signDate?: string;
    signed?: string;
    startBasis?: string;
    /** @enum {string} */
    status?: "APPLICANT" | "APPROVED" | "WAITING" | "REJECTED";
    /** Format: int64 */
    version?: number;
    visa?: string;
    /** Format: date */
    visaExpiry?: string;
    weekHrs?: string;
    worker?: definitions["Worker"];
  };
  /** AgencyWorkerPropertiesCreateDto */
  AgencyWorkerPropertiesCreateDto: {
    /** Format: int64 */
    agencyId?: number;
    approver?: string;
    comment?: string;
    /** @example yyyy-MM-dd */
    contractEndDate?: string;
    /** @example yyyy-MM-dd */
    dbsExpiry?: string;
    dbsNumber?: string;
    eligible?: boolean;
    /** @example yyyy-MM-dd */
    employmentStartDate?: string;
    /** @example yyyy-MM-dd */
    expiry?: string;
    /** @example yyyy-MM-dd */
    nextCheckDate?: string;
    paperwork?: boolean;
    paycycle?: string;
    paymentMethod?: string;
    position?: string;
    proof?: string;
    /** Format: int32 */
    rating?: number;
    /** @example yyyy-MM-dd */
    restrictionExpiry?: string;
    restrictions?: string;
    rightToWork?: string;
    rtiId?: string;
    /** @example yyyy-MM-dd */
    signDate?: string;
    signed?: string;
    startBasis?: string;
    visa?: string;
    /** @example yyyy-MM-dd */
    visaExpiry?: string;
    weekHrs?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** AssignmentCode */
  AssignmentCode: {
    assignmentRates?: definitions["AssignmentRate"][];
    code?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    services?: definitions["Services"];
    /** Format: int64 */
    version?: number;
  };
  /** AssignmentCodeCreateDto */
  AssignmentCodeCreateDto: {
    code?: string;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
  };
  /** AssignmentCodeRateCreateDto */
  AssignmentCodeRateCreateDto: {
    /** Format: int64 */
    agencyId?: number;
    /** Format: int64 */
    assignmentCodeId?: number;
    /** Format: int64 */
    clientId?: number;
    clientRate?: number;
    /** @enum {string} */
    dayOfWeek?:
      | "MONDAY"
      | "TUESDAY"
      | "WEDNESDAY"
      | "THURSDAY"
      | "FRIDAY"
      | "SATURDAY"
      | "SUNDAY";
    /** Format: int64 */
    directorateId?: number;
    endTime?: definitions["LocalTime"];
    /** Format: int64 */
    locationId?: number;
    payeRate?: number;
    privateRate?: number;
    /** Format: int64 */
    shiftTypeId?: number;
    startTime?: definitions["LocalTime"];
    umbrellaRate?: number;
  };
  /** AssignmentCodeRateResultDto */
  AssignmentCodeRateResultDto: {
    /** Format: int64 */
    agencyId?: number;
    agencyName?: string;
    assignmentCode?: string;
    /** Format: int64 */
    assignmentCodeId?: number;
    /** Format: int64 */
    clientId?: number;
    clientName?: string;
    clientRate?: number;
    createdBy?: string;
    /** @enum {string} */
    dayOfWeek?:
      | "MONDAY"
      | "TUESDAY"
      | "WEDNESDAY"
      | "THURSDAY"
      | "FRIDAY"
      | "SATURDAY"
      | "SUNDAY";
    directorate?: string;
    /** @example HH:mm */
    endTime?: string;
    /** Format: int64 */
    id?: number;
    location?: string;
    payeRate?: number;
    privateRate?: number;
    shiftType?: string;
    /** @example HH:mm */
    startTime?: string;
    umbrellaRate?: number;
  };
  /** AssignmentCodeRateUpdateDto */
  AssignmentCodeRateUpdateDto: {
    /** Format: int64 */
    agencyId?: number;
    /** Format: int64 */
    assignmentCodeId?: number;
    /** Format: int64 */
    clientId?: number;
    clientRate?: number;
    /** @enum {string} */
    dayOfWeek?:
      | "MONDAY"
      | "TUESDAY"
      | "WEDNESDAY"
      | "THURSDAY"
      | "FRIDAY"
      | "SATURDAY"
      | "SUNDAY";
    /** Format: int64 */
    directorateId?: number;
    endTime?: definitions["LocalTime"];
    /** Format: int64 */
    id?: number;
    /** Format: int64 */
    locationId?: number;
    payeRate?: number;
    privateRate?: number;
    /** Format: int64 */
    shiftTypeId?: number;
    startTime?: definitions["LocalTime"];
    umbrellaRate?: number;
  };
  /** AssignmentCodeResultDto */
  AssignmentCodeResultDto: {
    code?: string;
    createdBy?: string;
    /** Format: int64 */
    id?: number;
    name?: string;
    rate?: number;
    serviceName?: string;
  };
  /** AssignmentCodeUpdateDto */
  AssignmentCodeUpdateDto: {
    code?: string;
    /** Format: int64 */
    id?: number;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
  };
  /** AssignmentRate */
  AssignmentRate: {
    agent?: definitions["Agency"];
    assignmentCode?: definitions["AssignmentCode"];
    client?: definitions["Client"];
    clientRate?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @enum {string} */
    dayOfTheWeek?:
      | "MONDAY"
      | "TUESDAY"
      | "WEDNESDAY"
      | "THURSDAY"
      | "FRIDAY"
      | "SATURDAY"
      | "SUNDAY";
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    endTime?: definitions["LocalTime"];
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    location?: definitions["Location"];
    payeRate?: number;
    privateRate?: number;
    shiftDirectorate?: definitions["ShiftDirectorate"];
    shiftType?: definitions["ShiftType"];
    startTime?: definitions["LocalTime"];
    umbrellaRate?: number;
    /** Format: int64 */
    version?: number;
  };
  /** AuthorizeTransportDto */
  AuthorizeTransportDto: {
    /** Format: float */
    breakTime?: number;
    /** @example yyyy-MM-dd'T'HH:mm */
    end?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    start?: string;
    /** Format: int64 */
    transportId?: number;
  };
  /** Availability */
  Availability: {
    /** Format: date */
    date?: string;
    endTime?: definitions["LocalTime"];
    /** Format: int64 */
    id?: number;
    reason?: string;
    startTime?: definitions["LocalTime"];
    worker?: definitions["Worker"];
  };
  /** AvailabilityCreateDto */
  AvailabilityCreateDto: {
    /** @example yyyy-MM-dd */
    date?: string;
    /** @example HH:mm */
    endTime?: string;
    isAvailable?: boolean;
    reason?: string;
    /** @example HH:mm */
    startTime?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** AvailableTraininingsResultsDto */
  AvailableTraininingsResultsDto: {
    /** Format: int64 */
    availableTrainings?: number;
    /** Format: int64 */
    id?: number;
    trainingName?: string;
  };
  /** Bank */
  Bank: {
    account?: string;
    bank?: string;
    code?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    fullname?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    /** Format: date */
    signDate?: string;
    signed?: string;
    /** Format: int64 */
    version?: number;
  };
  /** BankCreateDto */
  BankCreateDto: {
    account?: string;
    bank?: string;
    code?: string;
    fullname?: string;
    lastModifiedDate?: string;
    name?: string;
    /** @example yyyy-MM-dd */
    signDate?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** BankDetails */
  BankDetails: {
    accountName?: string;
    accountNumber?: string;
    sortCode?: string;
  };
  /** BankResultDto */
  BankResultDto: {
    account?: string;
    bank?: string;
    code?: string;
    fullname?: string;
    /** Format: int64 */
    id?: number;
    lastModifiedDate?: string;
    name?: string;
    /** @example yyyy-MM-dd */
    signDate?: string;
    signed?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** BookingResultDto */
  BookingResultDto: {
    /** @example yyyy-MM-dd'T'HH:mm */
    actualStart?: string;
    agencies?: number[];
    agency?: string;
    /** Format: int64 */
    agencyId?: number;
    applicantCount?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    appliedDate?: string;
    /** @enum {string} */
    appliedStatus?:
      | "NEW"
      | "CANCELLED"
      | "BOOKED"
      | "AWAITING_AUTHORIZATION"
      | "IN_QUERY"
      | "AUTHORIZED"
      | "EXPIRED"
      | "DELETED"
      | "APPLIED"
      | "BILLED"
      | "PROCESSED"
      | "PAID"
      | "RELEASED"
      | "APPROVED"
      | "WAITING_APPROVAL"
      | "CLOSED"
      | "REJECTED";
    assignmentCode?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    authorizedDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    bookedDate?: string;
    /** @enum {string} */
    bookingType?: "SHIFT" | "TRAINING" | "TRANSPORT";
    bpostCode?: string;
    breakTime?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    cancelledDate?: string;
    cancelledReason?: string;
    carPooling?: boolean;
    /** Format: int64 */
    carPoolingChatGroupId?: number;
    carPoolingChatGroupName?: string;
    carPoolingShiftSet?: number[];
    client?: string;
    /** Format: int64 */
    clientId?: number;
    cost?: string;
    createdBy?: string;
    daddress?: string;
    directorate?: string;
    dpostCode?: string;
    dward?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    end?: string;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int32 */
    hoursBeforeBroadcasting?: number;
    /** Format: int64 */
    id?: number;
    isAgencyBilled?: boolean;
    /** Format: int64 */
    lastAuthorisationReminder?: number;
    /** @example yyyy-MM-dd'T'HH:mm */
    lastModifiedDate?: string;
    notes?: string;
    /** Format: int32 */
    numberOfStaff?: number;
    paddress?: string;
    payer?: string;
    phoneNumber?: string;
    postCode?: string;
    ppostCode?: string;
    pward?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    queriedDate?: string;
    queriedReason?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    releaseDate?: string;
    released?: boolean;
    requireApplicationByWorkers?: boolean;
    shiftLocation?: string;
    shiftStatus?: string;
    shiftType?: string;
    showNoteToAgency?: boolean;
    showNoteToFw?: boolean;
    /** @example yyyy-MM-dd'T'HH:mm */
    start?: string;
    trainer?: string;
    trainingName?: string;
    worker?: string;
    /** Format: int64 */
    workerId?: number;
    /** Format: int64 */
    workerSpecId?: number;
  };
  /** ChargeRate */
  ChargeRate: {
    assignmentCode?: definitions["AssignmentCode"];
    chargeRate?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    shiftType?: definitions["ShiftType"];
    /** Format: int64 */
    version?: number;
  };
  /** ChargeRateDto */
  ChargeRateDto: {
    /** Format: int64 */
    agencyId?: number;
    /** Format: int64 */
    assignmentCodeId?: number;
    chargeRate?: number;
    /** Format: int64 */
    shiftTypeId?: number;
  };
  /** ChargeRateUpdateDto */
  ChargeRateUpdateDto: {
    chargeRate?: number;
    /** Format: int64 */
    id?: number;
  };
  /** ChatGroup */
  ChatGroup: {
    groupName?: string;
    /** Format: int64 */
    id?: number;
  };
  /** ChatGroupMessageResponseDto */
  ChatGroupMessageResponseDto: {
    /** Format: int64 */
    chatGroupMessageId?: number;
    chatGroupName?: string;
    message?: string;
    messageType?: string;
    sender?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    sentAt?: string;
  };
  /** Client */
  Client: {
    address?: definitions["Address"];
    billingEmail?: string;
    clientDocs?: definitions["ClientDocs"][];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    email?: string;
    /** Format: int64 */
    id?: number;
    invoices?: definitions["Invoice"][];
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    logo?: string;
    name?: string;
    purchaseOrder?: string;
    sbsCode?: string;
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    telephone?: string;
    validVehicleBookings?: definitions["VehicleBooking"][];
    verified?: boolean;
    /** Format: int64 */
    version?: number;
  };
  /** ClientDocs */
  ClientDocs: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** @example dd-MM-yyyy */
    expiryDate?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** @enum {string} */
    name?: "ID" | "DRIVER" | "PASSPORT" | "PROOF_RESIDENCE";
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    url?: string;
    /** Format: int64 */
    version?: number;
  };
  /** ClientDto */
  ClientDto: {
    address?: definitions["Address"];
    administratorCreateDto?: definitions["AdministratorCreateDto"];
    /** Format: int64 */
    agencyId?: number;
    billingEmail?: string;
    clientDocs?: definitions["ClientDocs"][];
    email?: string;
    /** Format: int64 */
    id?: number;
    logo?: string;
    name?: string;
    purchaseOrder?: string;
    /** Format: float */
    rating?: number;
    sbsCode?: string;
    /** Format: int64 */
    serviceId?: number;
    telephone?: string;
    /** Format: int32 */
    totalBooking?: number;
    verified?: boolean;
  };
  /** ClientStats */
  ClientStats: {
    /** Format: int32 */
    numberOfAgencies?: number;
    /** Format: int32 */
    numberOfShiftsCreated?: number;
    /** Format: int32 */
    numberOfWorkersWhoBookedShifts?: number;
  };
  /** CommentDto */
  CommentDto: {
    comment?: string;
  };
  /** Compliance */
  Compliance: {
    code?: string;
    description?: string;
    /** Format: int64 */
    id?: number;
    name?: string;
    services?: definitions["Services"];
  };
  /** ComplianceCreateDto */
  ComplianceCreateDto: {
    code?: string;
    description?: string;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
  };
  /** ComplianceUpdateDto */
  ComplianceUpdateDto: {
    code?: string;
    /** Format: int64 */
    complianceId?: number;
    description?: string;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
  };
  /** CreatePaymentResponse */
  CreatePaymentResponse: {
    clientSecret?: string;
  };
  /** DamageInfo */
  DamageInfo: {
    area?: string;
    /** Format: double */
    charge?: number;
    description?: string;
    /** Format: int64 */
    id?: number;
    /** @enum {string} */
    type?: "EXTRA" | "OUT" | "IN";
  };
  /** DeviceWorkerUpdateDto */
  DeviceWorkerUpdateDto: {
    fcmToken?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** Duration */
  Duration: {
    /** Format: int32 */
    nano?: number;
    negative?: boolean;
    /** Format: int64 */
    seconds?: number;
    units?: definitions["TemporalUnit"][];
    zero?: boolean;
  };
  /** ExpenseRate */
  ExpenseRate: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    unit?: string;
    /** Format: int64 */
    version?: number;
  };
  /** ExpenseRateDto */
  ExpenseRateDto: {
    name?: string;
    rate?: number;
    unit?: string;
  };
  /** ExpenseRateUpdateDto */
  ExpenseRateUpdateDto: {
    /** Format: int64 */
    expenseRateId?: number;
    name?: string;
    rate?: number;
    unit?: string;
  };
  /** File */
  File: {
    absolute?: boolean;
    absoluteFile?: definitions["File"];
    absolutePath?: string;
    canonicalFile?: definitions["File"];
    canonicalPath?: string;
    directory?: boolean;
    file?: boolean;
    /** Format: int64 */
    freeSpace?: number;
    hidden?: boolean;
    name?: string;
    parent?: string;
    parentFile?: definitions["File"];
    path?: string;
    /** Format: int64 */
    totalSpace?: number;
    /** Format: int64 */
    usableSpace?: number;
  };
  /** FileDto */
  FileDto: {
    fileName?: string;
    fileUrl?: string;
  };
  /** GeneralResponse */
  GeneralResponse: {
    message?: string;
    responseCode?: string;
  };
  /** IAgency */
  IAgency: {
    address?: string;
    email?: string;
    /** Format: int64 */
    id?: number;
    logo?: string;
    name?: string;
    service?: string;
    status?: string;
    telephone?: string;
  };
  /** IAgencyWorkerProperties */
  IAgencyWorkerProperties: {
    /** Format: int64 */
    agencyId?: number;
    approver?: string;
    comment?: string;
    contractEndDate?: string;
    dbsExpiry?: string;
    dbsNumber?: string;
    eligible?: boolean;
    employmentStartDate?: string;
    expiry?: string;
    /** Format: int64 */
    id?: number;
    nextCheckDate?: string;
    paperwork?: boolean;
    paymentMethod?: string;
    position?: string;
    proof?: string;
    restrictionExpiry?: string;
    restrictions?: string;
    rightToWork?: string;
    signDate?: string;
    signed?: string;
    status?: string;
    trainingDate?: string;
    trainingExpiry?: string;
    visa?: string;
    visaExpiry?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** IAvailabilityResultDto */
  IAvailabilityResultDto: {
    date?: string;
    endTime?: string;
    /** Format: int64 */
    id?: number;
    reason?: string;
    startTime?: string;
  };
  /** IPayAdvice */
  IPayAdvice: {
    dayOfTheWeek?: string;
    endDate?: string;
    endTime?: string;
    /** Format: int64 */
    id?: number;
    /** Format: double */
    numberOfHoursWorked?: number;
    rate?: number;
    /** Format: int64 */
    shiftId?: number;
    startDate?: string;
    startTime?: string;
    total?: number;
  };
  /** IShiftCompliance */
  IShiftCompliance: {
    firstname?: string;
    lastname?: string;
    /** Format: date */
    shiftDate?: string;
    /** Format: int64 */
    shiftId?: number;
    /** Format: int32 */
    totalCompliance?: number;
    /** Format: int32 */
    totalTrainings?: number;
    /** Format: int64 */
    workerId?: number;
  };
  /** IShiftReportStatus */
  IShiftReportStatus: {
    /** Format: int32 */
    authorized?: number;
    /** Format: int32 */
    billed?: number;
    /** Format: int32 */
    booked?: number;
    /** Format: int32 */
    cancelled?: number;
    /** Format: int32 */
    newShift?: number;
    /** Format: int32 */
    queried?: number;
  };
  /** IWorkerComplianceResultDto */
  IWorkerComplianceResultDto: {
    agencyId?: string;
    code?: string;
    complianceDate?: string;
    complianceExpiry?: string;
    complianceId?: string;
    description?: string;
    document?: string;
    /** Format: int64 */
    id?: number;
    name?: string;
    status?: string;
    uploaded?: boolean;
    workerId?: string;
  };
  /** InputStream */
  InputStream: { [key: string]: unknown };
  /** InviteWorkerRequestDto */
  InviteWorkerRequestDto: {
    agencyName?: string;
    emails?: string[];
    link?: string;
  };
  /** Invoice */
  Invoice: {
    amountDue?: number;
    clientSecret?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    discount?: number;
    /** Format: date */
    dueDate?: string;
    /** Format: int64 */
    id?: number;
    /** Format: date */
    invoiceDate?: string;
    invoiceItems?: definitions["InvoiceItem"][];
    /** @enum {string} */
    invoiceStatus?: "QUOTATION" | "UNPAID" | "PAID" | "PARTLY" | "CANCELLED";
    /** @enum {string} */
    invoiceType?: "CLIENT" | "AGENCY" | "AGENCYTRAINING" | "WORKERTRAINING";
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    notes?: string;
    /** Format: int64 */
    payeeId?: number;
    /** @enum {string} */
    paymentGatewayType?: "PAYNOW" | "STRIPE";
    payments?: definitions["Payment"][];
    pollUrl?: string;
    published?: boolean;
    redirectUrl?: string;
    serviceCharge?: number;
    serviceChargeDesc?: string;
    /** @enum {string} */
    settlementStatus?:
      | "UNPAID"
      | "PENDING"
      | "PROCESSED"
      | "SUBMITTED"
      | "CANCELLED";
    subTotalAmount?: number;
    totalAmount?: number;
    totalAmountPaid?: number;
    vatAmount?: number;
    vatPercentage?: number;
    /** Format: int64 */
    version?: number;
    /** Format: int64 */
    workerId?: number;
  };
  /** InvoiceCreateDto */
  InvoiceCreateDto: {
    /** Format: date */
    dueDate?: string;
    email?: boolean;
    /** @enum {string} */
    invoiceType?: "CLIENT" | "AGENCY" | "AGENCYTRAINING" | "WORKERTRAINING";
    notes?: string;
    shiftIds?: number[];
    trainingSessionIds?: number[];
    workerTrainingSessionIds?: number[];
  };
  /** InvoiceItem */
  InvoiceItem: {
    assignmentCode?: string;
    client?: string;
    /** Format: int64 */
    clientId?: number;
    dayOfTheWeek?: string;
    description?: string;
    directorate?: string;
    endDate?: string;
    endTime?: string;
    /** Format: int64 */
    id?: number;
    invoice?: definitions["Invoice"];
    /** Format: double */
    numberOfHoursWorked?: number;
    rate?: number;
    /** Format: int64 */
    shiftId?: number;
    shiftType?: string;
    startDate?: string;
    startTime?: string;
    total?: number;
    /** Format: int64 */
    trainingId?: number;
    worker?: string;
  };
  /** InvoiceItemResult */
  InvoiceItemResult: {
    assignmentCode?: string;
    client?: string;
    /** Format: int64 */
    clientId?: number;
    dayOfTheWeek?: string;
    description?: string;
    directorate?: string;
    endDate?: string;
    endTime?: string;
    /** Format: int64 */
    id?: number;
    /** Format: double */
    numberOfHoursWorked?: number;
    rate?: number;
    /** Format: int64 */
    shiftId?: number;
    shiftType?: string;
    startDate?: string;
    startTime?: string;
    total?: number;
    /** Format: int64 */
    trainingId?: number;
    worker?: string;
  };
  /** InvoiceResult */
  InvoiceResult: {
    /** Format: int64 */
    agencyId?: number;
    agencyName?: string;
    client?: definitions["ClientDto"];
    /** Format: int64 */
    clientId?: number;
    clientName?: string;
    clientSecret?: string;
    description?: string;
    discount?: number;
    /** @example yyyy-MM-dd */
    dueDate?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    invoiceDate?: string;
    invoiceItemResult?: definitions["InvoiceItemResult"][];
    invoiceStatus?: string;
    /** @enum {string} */
    invoiceType?: "CLIENT" | "AGENCY" | "AGENCYTRAINING" | "WORKERTRAINING";
    /** Format: int64 */
    payeeId?: number;
    paymentRef?: definitions["Payment"][];
    payments?: definitions["Payment"][];
    published?: boolean;
    redirectUrl?: string;
    serviceCharge?: number;
    serviceChargeDesc?: string;
    subTotalAmount?: number;
    totalAmount?: number;
    vatAmount?: number;
    vatPercentage?: number;
    /** Format: int64 */
    vehicleBookingId?: number;
    /** Format: int64 */
    workerId?: number;
    workerName?: string;
  };
  /** LegibleWorkerDto */
  LegibleWorkerDto: {
    available?: boolean;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int64 */
    workerId?: number;
    workerName?: string;
  };
  /** LegibleWorkersDto */
  LegibleWorkersDto: {
    /** Format: int64 */
    workerSpecId?: number;
    workers?: definitions["LegibleWorkerDto"][];
  };
  /** LocalTime */
  LocalTime: {
    /** Format: int32 */
    hour?: number;
    /** Format: int32 */
    minute?: number;
    /** Format: int32 */
    nano?: number;
    /** Format: int32 */
    second?: number;
  };
  /** Location */
  Location: {
    adminName?: string;
    capital?: string;
    city?: string;
    cityAscii?: string;
    country?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    iso2?: string;
    iso3?: string;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: float */
    lat?: number;
    /** Format: float */
    lng?: number;
    timeZone?: string;
    timeZoneId?: definitions["ZoneId"];
    /** Format: int64 */
    version?: number;
  };
  /** MemoryStats */
  MemoryStats: {
    heapFreeSize?: string;
    heapMaxSize?: string;
    heapSize?: string;
  };
  /** Note */
  Note: {
    agency?: definitions["Worker"];
    /** Format: int64 */
    id?: number;
    note?: string;
    worker?: definitions["Worker"];
  };
  /** NoteCreateDto */
  NoteCreateDto: {
    /** Format: int64 */
    agencyId?: number;
    note?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** Notification */
  Notification: {
    body?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    file?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** @enum {string} */
    recipientType?: "CLIENT" | "AGENCY" | "ADMIN" | "WORKER";
    sendToAll?: boolean;
    /** Format: int64 */
    senderId?: number;
    /** @enum {string} */
    senderType?: "CLIENT" | "AGENCY" | "ADMIN" | "WORKER";
    /** Format: date-time */
    sentTime?: string;
    title?: string;
    token?: string;
    /** Format: int64 */
    version?: number;
  };
  /** NotificationResultDto */
  NotificationResultDto: {
    /** Format: int64 */
    agencyId?: number;
    body?: string;
    /** Format: int64 */
    clientId?: number;
    /** Format: int64 */
    id?: number;
    title?: string;
    token?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** Number */
  Number: { [key: string]: unknown };
  /** Optional«WorkerTrainingSession» */
  "Optional«WorkerTrainingSession»": {
    empty?: boolean;
    present?: boolean;
  };
  /** Pageable */
  Pageable: {
    /** Format: int64 */
    offset?: number;
    /** Format: int32 */
    pageNumber?: number;
    /** Format: int32 */
    pageSize?: number;
    paged?: boolean;
    sort?: definitions["Sort"];
    unpaged?: boolean;
  };
  /** Page«AgencyResultDto» */
  "Page«AgencyResultDto»": {
    content?: definitions["AgencyResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«AssignmentCodeRateResultDto» */
  "Page«AssignmentCodeRateResultDto»": {
    content?: definitions["AssignmentCodeRateResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«AssignmentCodeResultDto» */
  "Page«AssignmentCodeResultDto»": {
    content?: definitions["AssignmentCodeResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«BookingResultDto» */
  "Page«BookingResultDto»": {
    content?: definitions["BookingResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«ChargeRate» */
  "Page«ChargeRate»": {
    content?: definitions["ChargeRate"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«ClientDto» */
  "Page«ClientDto»": {
    content?: definitions["ClientDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«Compliance» */
  "Page«Compliance»": {
    content?: definitions["Compliance"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«ExpenseRate» */
  "Page«ExpenseRate»": {
    content?: definitions["ExpenseRate"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«IAgency» */
  "Page«IAgency»": {
    content?: definitions["IAgency"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«IAvailabilityResultDto» */
  "Page«IAvailabilityResultDto»": {
    content?: definitions["IAvailabilityResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«IShiftCompliance» */
  "Page«IShiftCompliance»": {
    content?: definitions["IShiftCompliance"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«IWorkerComplianceResultDto» */
  "Page«IWorkerComplianceResultDto»": {
    content?: definitions["IWorkerComplianceResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«InvoiceResult» */
  "Page«InvoiceResult»": {
    content?: definitions["InvoiceResult"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«PayAdviceResult» */
  "Page«PayAdviceResult»": {
    content?: definitions["PayAdviceResult"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«PayslipResultDto» */
  "Page«PayslipResultDto»": {
    content?: definitions["PayslipResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«PromotionDto» */
  "Page«PromotionDto»": {
    content?: definitions["PromotionDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«ServiceResultDto» */
  "Page«ServiceResultDto»": {
    content?: definitions["ServiceResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«SettlementStatement» */
  "Page«SettlementStatement»": {
    content?: definitions["SettlementStatement"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«ShiftDirectorateResultDto» */
  "Page«ShiftDirectorateResultDto»": {
    content?: definitions["ShiftDirectorateResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«ShiftLocationResultDto» */
  "Page«ShiftLocationResultDto»": {
    content?: definitions["ShiftLocationResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«ShiftType» */
  "Page«ShiftType»": {
    content?: definitions["ShiftType"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«TaxCode» */
  "Page«TaxCode»": {
    content?: definitions["TaxCode"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«TrainingSessionResultDto» */
  "Page«TrainingSessionResultDto»": {
    content?: definitions["TrainingSessionResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«Training» */
  "Page«Training»": {
    content?: definitions["Training"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«TransportApplicantsResultDto» */
  "Page«TransportApplicantsResultDto»": {
    content?: definitions["TransportApplicantsResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«TransportDto» */
  "Page«TransportDto»": {
    content?: definitions["TransportDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«VatRate» */
  "Page«VatRate»": {
    content?: definitions["VatRate"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«VehicleBookingDto» */
  "Page«VehicleBookingDto»": {
    content?: definitions["VehicleBookingDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«VehicleDto» */
  "Page«VehicleDto»": {
    content?: definitions["VehicleDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«VehicleLogDto» */
  "Page«VehicleLogDto»": {
    content?: definitions["VehicleLogDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«WorkerResultDto» */
  "Page«WorkerResultDto»": {
    content?: definitions["WorkerResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«WorkerTrainingResultDto» */
  "Page«WorkerTrainingResultDto»": {
    content?: definitions["WorkerTrainingResultDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** Page«WorkerTrainingSessionResultsDto» */
  "Page«WorkerTrainingSessionResultsDto»": {
    content?: definitions["WorkerTrainingSessionResultsDto"][];
    empty?: boolean;
    first?: boolean;
    last?: boolean;
    /** Format: int32 */
    number?: number;
    /** Format: int32 */
    numberOfElements?: number;
    pageable?: definitions["Pageable"];
    /** Format: int32 */
    size?: number;
    sort?: definitions["Sort"];
    /** Format: int64 */
    totalElements?: number;
    /** Format: int32 */
    totalPages?: number;
  };
  /** PayAdviceCreateDto */
  PayAdviceCreateDto: {
    shiftIds?: number[];
  };
  /** PayAdviceItemResult */
  PayAdviceItemResult: {
    dayOfTheWeek?: string;
    directorate?: string;
    endDate?: string;
    endTime?: string;
    getPayDate?: string;
    getYearGross?: number;
    /** Format: int64 */
    id?: number;
    /** Format: double */
    numberOfHoursWorked?: number;
    rate?: number;
    /** Format: int64 */
    shiftId?: number;
    startDate?: string;
    startTime?: string;
    total?: number;
  };
  /** PayAdviceResult */
  PayAdviceResult: {
    /** Format: int64 */
    agentId?: number;
    /** Format: int64 */
    id?: number;
    ipayAdviceItemResult?: definitions["IPayAdvice"][];
    payAdviceDate?: string;
    payAdviceItemResult?: definitions["PayAdviceItemResult"][];
    payAdviceStatus?: string;
    paymentRef?: string;
    shiftExpenseClaims?: definitions["ShiftExpenseClaimResultDto"][];
    totalAmount?: number;
    workerGross?: string;
    /** Format: int64 */
    workerId?: number;
    workerName?: string;
  };
  /** Payment */
  Payment: {
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd'T'HH:mmXXX */
    paymentDate?: string;
    /** @enum {string} */
    paymentType?: "STRIPE" | "PAYNOW" | "MANUAL";
    ref?: string;
    /** @enum {string} */
    status?: "QUOTATION" | "UNPAID" | "PAID" | "PARTLY" | "CANCELLED";
    total?: number;
  };
  /** Payslip */
  Payslip: {
    agency?: definitions["Agency"];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    date?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    payslipPdf?: string;
    /** Format: int64 */
    version?: number;
    worker?: definitions["Worker"];
  };
  /** PayslipResultDto */
  PayslipResultDto: {
    agencyId?: string;
    date?: string;
    /** Format: int64 */
    id?: number;
    payslipPDF?: string;
    workerId?: string;
  };
  /** PromotionDto */
  PromotionDto: {
    /** Format: date-time */
    activatedDate?: string;
    adminDiscount?: boolean;
    agency?: definitions["AgencyResultDto"];
    /** Format: date-time */
    cancelledDate?: string;
    code?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** Format: int32 */
    daysHired?: number;
    description?: string;
    /** Format: float */
    discount?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss.SSS'Z' */
    expiryDate?: string;
    /** Format: int32 */
    extraDays?: number;
    /** Format: int32 */
    extraMileage?: number;
    htmlBody?: string;
    /** Format: int64 */
    id?: number;
    /** @enum {string} */
    promotionType?: "DISCOUNT" | "EXTRA_MILEAGE" | "EXTRA_DAYS" | "OTHER_AWARD";
    /** @example yyyy-MM-dd'T'HH:mm:ss.SSS'Z' */
    startDate?: string;
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    title?: string;
    /** Format: int32 */
    usageCount?: number;
    /** Format: int32 */
    usageLimit?: number;
    vehicleFilter?: definitions["VehicleFilterDto"];
  };
  /** Rating */
  Rating: {
    comment?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    /** Format: int32 */
    rating?: number;
    ratingItems?: definitions["RatingItem"][];
    /** @enum {string} */
    type?: "CLIENT" | "VEHICLE";
    /** Format: int64 */
    version?: number;
  };
  /** RatingItem */
  RatingItem: {
    /** Format: int64 */
    id?: number;
    /** Format: int32 */
    rate?: number;
    /** @enum {string} */
    status?:
      | "CLEANLINESS"
      | "CONDITION"
      | "COMFORT"
      | "PROCESS"
      | "PROFESSIONALISM"
      | "TIMELINESS"
      | "REPEAT";
  };
  /** Resource */
  Resource: {
    description?: string;
    file?: definitions["File"];
    filename?: string;
    inputStream?: definitions["InputStream"];
    open?: boolean;
    readable?: boolean;
    uri?: definitions["URI"];
    url?: definitions["URL"];
  };
  /** ResponseEntity */
  ResponseEntity: {
    body?: { [key: string]: unknown };
    /** @enum {string} */
    statusCode?:
      | "100 CONTINUE"
      | "101 SWITCHING_PROTOCOLS"
      | "102 PROCESSING"
      | "103 CHECKPOINT"
      | "200 OK"
      | "201 CREATED"
      | "202 ACCEPTED"
      | "203 NON_AUTHORITATIVE_INFORMATION"
      | "204 NO_CONTENT"
      | "205 RESET_CONTENT"
      | "206 PARTIAL_CONTENT"
      | "207 MULTI_STATUS"
      | "208 ALREADY_REPORTED"
      | "226 IM_USED"
      | "300 MULTIPLE_CHOICES"
      | "301 MOVED_PERMANENTLY"
      | "302 FOUND"
      | "302 MOVED_TEMPORARILY"
      | "303 SEE_OTHER"
      | "304 NOT_MODIFIED"
      | "305 USE_PROXY"
      | "307 TEMPORARY_REDIRECT"
      | "308 PERMANENT_REDIRECT"
      | "400 BAD_REQUEST"
      | "401 UNAUTHORIZED"
      | "402 PAYMENT_REQUIRED"
      | "403 FORBIDDEN"
      | "404 NOT_FOUND"
      | "405 METHOD_NOT_ALLOWED"
      | "406 NOT_ACCEPTABLE"
      | "407 PROXY_AUTHENTICATION_REQUIRED"
      | "408 REQUEST_TIMEOUT"
      | "409 CONFLICT"
      | "410 GONE"
      | "411 LENGTH_REQUIRED"
      | "412 PRECONDITION_FAILED"
      | "413 PAYLOAD_TOO_LARGE"
      | "413 REQUEST_ENTITY_TOO_LARGE"
      | "414 URI_TOO_LONG"
      | "414 REQUEST_URI_TOO_LONG"
      | "415 UNSUPPORTED_MEDIA_TYPE"
      | "416 REQUESTED_RANGE_NOT_SATISFIABLE"
      | "417 EXPECTATION_FAILED"
      | "418 I_AM_A_TEAPOT"
      | "419 INSUFFICIENT_SPACE_ON_RESOURCE"
      | "420 METHOD_FAILURE"
      | "421 DESTINATION_LOCKED"
      | "422 UNPROCESSABLE_ENTITY"
      | "423 LOCKED"
      | "424 FAILED_DEPENDENCY"
      | "425 TOO_EARLY"
      | "426 UPGRADE_REQUIRED"
      | "428 PRECONDITION_REQUIRED"
      | "429 TOO_MANY_REQUESTS"
      | "431 REQUEST_HEADER_FIELDS_TOO_LARGE"
      | "451 UNAVAILABLE_FOR_LEGAL_REASONS"
      | "500 INTERNAL_SERVER_ERROR"
      | "501 NOT_IMPLEMENTED"
      | "502 BAD_GATEWAY"
      | "503 SERVICE_UNAVAILABLE"
      | "504 GATEWAY_TIMEOUT"
      | "505 HTTP_VERSION_NOT_SUPPORTED"
      | "506 VARIANT_ALSO_NEGOTIATES"
      | "507 INSUFFICIENT_STORAGE"
      | "508 LOOP_DETECTED"
      | "509 BANDWIDTH_LIMIT_EXCEEDED"
      | "510 NOT_EXTENDED"
      | "511 NETWORK_AUTHENTICATION_REQUIRED";
    /** Format: int32 */
    statusCodeValue?: number;
  };
  /** ServiceCreateDto */
  ServiceCreateDto: {
    name?: string;
  };
  /** ServiceResultDto */
  ServiceResultDto: {
    createdBy?: string;
    /** Format: int64 */
    id?: number;
    name?: string;
  };
  /** ServiceUpdateDto */
  ServiceUpdateDto: {
    /** Format: int64 */
    id?: number;
    name?: string;
  };
  /** Services */
  Services: {
    agency?: definitions["Agency"];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    /** Format: int64 */
    version?: number;
  };
  /** SettlementStatement */
  SettlementStatement: {
    agency?: definitions["Agency"];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    invoices?: definitions["Invoice"][];
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: date */
    settlementDate?: string;
    /** Format: double */
    totalAmount?: number;
    /** Format: int64 */
    version?: number;
  };
  /** Shift */
  Shift: {
    applicantCount?: definitions["Number"];
    /** @example dd-MM-yyyy HH:mm:ss */
    appliedDate?: string;
    /** @enum {string} */
    appliedStatus?:
      | "NEW"
      | "CANCELLED"
      | "BOOKED"
      | "AWAITING_AUTHORIZATION"
      | "IN_QUERY"
      | "AUTHORIZED"
      | "EXPIRED"
      | "DELETED"
      | "APPLIED"
      | "BILLED"
      | "PROCESSED"
      | "PAID"
      | "RELEASED"
      | "APPROVED"
      | "WAITING_APPROVAL"
      | "CLOSED"
      | "REJECTED";
    /** @example dd-MM-yyyy HH:mm:ss */
    authorizedDate?: string;
    booked?: boolean;
    /** @example dd-MM-yyyy HH:mm:ss */
    bookedDate?: string;
    breakTime?: string;
    cancelReason?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    cancelledDate?: string;
    carPooling?: boolean;
    carPoolingShiftSet?: number[];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    dateTimeBooked?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    directlyBookByWorker?: boolean;
    directorate?: definitions["ShiftDirectorate"];
    /** @example dd-MM-yyyy HH:mm:ss */
    end?: string;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int32 */
    hoursBeforeBroadcasting?: number;
    /** Format: int64 */
    id?: number;
    isAdminBilled?: boolean;
    isAgencyBilled?: boolean;
    /** @example dd-MM-yyyy HH:mm:ss */
    lastAuthorisationReminder?: string;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    notes?: string;
    publishToAllWorkers?: boolean;
    /** @example dd-MM-yyyy HH:mm:ss */
    queriedDate?: string;
    queryReason?: string;
    queryResponse?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    releaseDate?: string;
    released?: boolean;
    requireApplicationByWorkers?: boolean;
    shiftChatGroup?: definitions["ChatGroup"];
    /** @enum {string} */
    shiftWorkerStatus?:
      | "NEW"
      | "CANCELLED"
      | "BOOKED"
      | "AWAITING_AUTHORIZATION"
      | "IN_QUERY"
      | "AUTHORIZED"
      | "EXPIRED"
      | "DELETED"
      | "APPLIED"
      | "BILLED"
      | "PROCESSED"
      | "PAID"
      | "RELEASED"
      | "APPROVED"
      | "WAITING_APPROVAL"
      | "CLOSED"
      | "REJECTED";
    showNoteToAgency?: boolean;
    showNoteToFw?: boolean;
    /** @example dd-MM-yyyy HH:mm:ss */
    start?: string;
    /** @enum {string} */
    status?:
      | "NEW"
      | "CANCELLED"
      | "BOOKED"
      | "AWAITING_AUTHORIZATION"
      | "IN_QUERY"
      | "AUTHORIZED"
      | "EXPIRED"
      | "DELETED"
      | "APPLIED"
      | "BILLED"
      | "PROCESSED"
      | "PAID"
      | "RELEASED"
      | "APPROVED"
      | "WAITING_APPROVAL"
      | "CLOSED"
      | "REJECTED";
    /** Format: int64 */
    version?: number;
    workerNotified?: boolean;
    workerSpec?: definitions["TransportWorkerSpec"];
  };
  /** ShiftBillDto */
  ShiftBillDto: {
    /** Format: int64 */
    agencyId?: number;
    /** Format: date-time */
    dueDate?: string;
    /** Format: int64 */
    id?: number;
    notes?: string;
    sendToBilling?: boolean;
    shiftIds?: number[];
  };
  /** ShiftCarPoolingDto */
  ShiftCarPoolingDto: {
    carPoolingLocation?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    carPoolingTime?: string;
    firstName?: string;
    gender?: string;
    lastName?: string;
    postCode?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** ShiftCreateDto */
  ShiftCreateDto: {
    /** Format: int64 */
    agency?: number;
    agentIdList?: number[];
    /** Format: int64 */
    assignmentCodeId?: number;
    breakTime?: string;
    /** Format: int64 */
    clientId?: number;
    directBooking?: boolean;
    /** Format: date-time */
    end?: string;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int32 */
    hoursBeforeBroadcasting?: number;
    /** Format: int64 */
    id?: number;
    noneAttendance?: boolean;
    notes?: string;
    publishToAllWorkers?: boolean;
    queryResponse?: string;
    requireApplicationByWorkers?: boolean;
    /** Format: int64 */
    shiftDirectorateId?: number;
    shiftEndTime?: definitions["LocalTime"];
    /** Format: int64 */
    shiftLocationId?: number;
    shiftStartTime?: definitions["LocalTime"];
    /** @enum {string} */
    shiftStatus?:
      | "NEW"
      | "CANCELLED"
      | "BOOKED"
      | "AWAITING_AUTHORIZATION"
      | "IN_QUERY"
      | "AUTHORIZED"
      | "EXPIRED"
      | "DELETED"
      | "APPLIED"
      | "BILLED"
      | "PROCESSED"
      | "PAID"
      | "RELEASED"
      | "APPROVED"
      | "WAITING_APPROVAL"
      | "CLOSED"
      | "REJECTED";
    /** Format: int64 */
    shiftType?: number;
    showNoteToAgency?: boolean;
    showNoteToFw?: boolean;
    /** Format: date-time */
    start?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** ShiftDirectorate */
  ShiftDirectorate: {
    client?: definitions["Client"];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    deputyId?: number;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    location?: definitions["Location"];
    name?: string;
    phoneNumber?: string;
    postCode?: string;
    /** Format: int64 */
    version?: number;
  };
  /** ShiftDirectorateCreateDto */
  ShiftDirectorateCreateDto: {
    /** Format: int64 */
    clientId?: number;
    /** Format: int64 */
    deputyId?: number;
    name?: string;
    phoneNumber?: string;
    postCode?: string;
    /** Format: int64 */
    shiftLocationId?: number;
  };
  /** ShiftDirectorateResultDto */
  ShiftDirectorateResultDto: {
    client?: string;
    createdBy?: string;
    /** Format: int64 */
    id?: number;
    location?: string;
    name?: string;
    phoneNumber?: string;
    postCode?: string;
  };
  /** ShiftDirectorateUpdateDto */
  ShiftDirectorateUpdateDto: {
    /** Format: int64 */
    clientId?: number;
    /** Format: int64 */
    id?: number;
    /** Format: int64 */
    locationId?: number;
    name?: string;
    phoneNumber?: string;
    postCode?: string;
  };
  /** ShiftExpenseClaim */
  ShiftExpenseClaim: {
    agencyExpenseRate?: definitions["AgencyExpenseRate"];
    amount?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    description?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    rate?: number;
    shift?: definitions["Shift"];
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    /** Format: int64 */
    version?: number;
  };
  /** ShiftExpenseClaimDto */
  ShiftExpenseClaimDto: {
    /** Format: int64 */
    agencyExpenseRateId?: number;
    amount?: number;
    description?: string;
    /** Format: int64 */
    id?: number;
    rate?: number;
    /** Format: int64 */
    shiftId?: number;
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
  };
  /** ShiftExpenseClaimResultDto */
  ShiftExpenseClaimResultDto: {
    agencyExpenseRate?: definitions["AgencyExpenseRate"];
    amount?: number;
    description?: string;
    /** Format: int64 */
    id?: number;
    rate?: number;
    /** Format: int64 */
    shiftId?: number;
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
  };
  /** ShiftLocationCreateDto */
  ShiftLocationCreateDto: {
    /** Format: int64 */
    clientId?: number;
    name?: string;
    phoneNumber?: string;
    postcode?: string;
  };
  /** ShiftLocationResultDto */
  ShiftLocationResultDto: {
    adminName?: string;
    capital?: string;
    city?: string;
    cityAscii?: string;
    country?: string;
    /** Format: int64 */
    id?: number;
    iso2?: string;
    iso3?: string;
    /** Format: float */
    lat?: number;
    /** Format: float */
    lng?: number;
  };
  /** ShiftLocationUpdateDto */
  ShiftLocationUpdateDto: {
    /** Format: int64 */
    id?: number;
    name?: string;
    phoneNumber?: string;
    postcode?: string;
  };
  /** ShiftReportStatus */
  ShiftReportStatus: {
    /** Format: int64 */
    applied?: number;
    /** Format: int64 */
    authorized?: number;
    /** Format: int64 */
    awaiting?: number;
    /** Format: int64 */
    billed?: number;
    /** Format: int64 */
    booked?: number;
    /** Format: int64 */
    cancelled?: number;
    /** Format: int64 */
    closed?: number;
    /** Format: int64 */
    newShift?: number;
    /** Format: int64 */
    pending?: number;
    /** Format: int64 */
    queried?: number;
  };
  /** ShiftRequest */
  ShiftRequest: {
    /** Format: int64 */
    clientId?: number;
    /** Format: int32 */
    page?: number;
    /** Format: int32 */
    size?: number;
  };
  /** ShiftType */
  ShiftType: {
    /** @enum {string} */
    bookingType?: "SHIFT" | "TRAINING" | "TRANSPORT";
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    /** Format: int64 */
    version?: number;
  };
  /** ShiftUpdateDto */
  ShiftUpdateDto: {
    /** Format: int64 */
    assignmentCode?: number;
    breakTime?: string;
    /** Format: date-time */
    end?: string;
    /** Format: int32 */
    hoursBeforeBroadcasting?: number;
    /** Format: int64 */
    id?: number;
    noneAttendance?: boolean;
    notes?: string;
    queryResponse?: string;
    /** Format: int64 */
    shiftDirectorateId?: number;
    shiftEndTime?: definitions["LocalTime"];
    /** Format: int64 */
    shiftLocationId?: number;
    shiftStartTime?: definitions["LocalTime"];
    /** @enum {string} */
    shiftStatus?:
      | "NEW"
      | "CANCELLED"
      | "BOOKED"
      | "AWAITING_AUTHORIZATION"
      | "IN_QUERY"
      | "AUTHORIZED"
      | "EXPIRED"
      | "DELETED"
      | "APPLIED"
      | "BILLED"
      | "PROCESSED"
      | "PAID"
      | "RELEASED"
      | "APPROVED"
      | "WAITING_APPROVAL"
      | "CLOSED"
      | "REJECTED";
    showNoteToAgency?: boolean;
    showNoteToFw?: boolean;
    /** Format: date-time */
    start?: string;
  };
  /** Sort */
  Sort: {
    empty?: boolean;
    sorted?: boolean;
    unsorted?: boolean;
  };
  /** TaxCode */
  TaxCode: {
    code?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    deduction?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: int64 */
    version?: number;
  };
  /** TaxCodeDto */
  TaxCodeDto: {
    code?: string;
    deduction?: number;
  };
  /** TaxCodeUpdateDto */
  TaxCodeUpdateDto: {
    code?: string;
    deduction?: number;
    /** Format: int64 */
    taxId?: number;
  };
  /** TemporalUnit */
  TemporalUnit: {
    dateBased?: boolean;
    duration?: definitions["Duration"];
    durationEstimated?: boolean;
    timeBased?: boolean;
  };
  /** Training */
  Training: {
    code?: string;
    description?: string;
    /** Format: int64 */
    hascoId?: number;
    /** Format: int64 */
    id?: number;
    name?: string;
    services?: definitions["Services"];
  };
  /** TrainingCreateDto */
  TrainingCreateDto: {
    code?: string;
    description?: string;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
  };
  /** TrainingFeedback */
  TrainingFeedback: {
    booking?: definitions["WorkerTrainingSession"];
    comment?: string;
    /** Format: int32 */
    content?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int32 */
    facility?: number;
    /** Format: int64 */
    id?: number;
    isRelevant?: boolean;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: int32 */
    method?: number;
    skillsDev?: boolean;
    /** Format: int32 */
    trainer?: number;
    /** Format: int64 */
    version?: number;
  };
  /** TrainingFeedbacksRes */
  TrainingFeedbacksRes: {
    /** Format: int32 */
    attendance?: number;
    feedbackList?: definitions["TrainingFeedback"][];
    trainingSession?: definitions["TrainingSession"];
  };
  /** TrainingSession */
  TrainingSession: {
    address?: string;
    /** Format: int64 */
    applicantCount?: number;
    /** Format: float */
    breakTimeMins?: number;
    /** @example yyyy-MM-dd'T'HH:mm */
    endDateTime?: string;
    getFeedback?: boolean;
    /** Format: int64 */
    id?: number;
    isAgencyPaying?: boolean;
    location?: definitions["Location"];
    name?: string;
    notes?: string;
    postCode?: string;
    publishToAllAgencies?: boolean;
    publishToAllWorkers?: boolean;
    /** @example yyyy-MM-dd'T'HH:mm */
    startDateTime?: string;
    supervisor?: string;
    trainer?: definitions["Agency"];
    training?: definitions["Training"];
    /** Format: double */
    trainingCost?: number;
    /** @enum {string} */
    trainingStatus?: "NEW" | "CLOSED" | "CANCELLED";
    /** Format: int32 */
    vacancies?: number;
  };
  /** TrainingSessionCreateDto */
  TrainingSessionCreateDto: {
    address?: string;
    agencyIds?: number[];
    /** Format: float */
    breakTime?: number;
    /** @example yyyy-MM-dd'T'HH:mm */
    endDateTime?: string;
    getFeedback?: boolean;
    isAgencyPaying?: boolean;
    name?: string;
    notes?: string;
    postCode?: string;
    publishToAllAgencies?: boolean;
    publishToAllWorkers?: boolean;
    /** Format: int64 */
    shiftLocationId?: number;
    /** @example yyyy-MM-dd'T'HH:mm */
    startDateTime?: string;
    supervisor?: string;
    /** Format: int64 */
    trainerId?: number;
    /** Format: double */
    trainingCost?: number;
    /** Format: int64 */
    trainingId?: number;
    /** @enum {string} */
    trainingStatus?:
      | "NEW"
      | "WAITING_APPROVAL"
      | "REJECTED"
      | "CLOSED"
      | "BOOKED"
      | "APPROVED"
      | "CANCELLED";
    /** Format: int32 */
    vacancies?: number;
  };
  /** TrainingSessionReportStatus */
  TrainingSessionReportStatus: {
    /** Format: int64 */
    awaitingBookings?: number;
    /** Format: int64 */
    closedBookings?: number;
    /** Format: int64 */
    newTrainingSession?: number;
  };
  /** TrainingSessionResultDto */
  TrainingSessionResultDto: {
    address?: string;
    agencyIds?: number[];
    /** Format: int64 */
    applicantCount?: number;
    bookings?: definitions["WorkerTrainingSessionResultsDto"][];
    /** Format: double */
    breakTime?: number;
    /** @example dd/MM/yyyy HH:mm */
    endDateTime?: string;
    /** Format: int64 */
    id?: number;
    isAgencyPaying?: boolean;
    name?: string;
    notes?: string;
    postCode?: string;
    publishToAllAgencies?: boolean;
    publishToAllWorkers?: boolean;
    /** Format: int64 */
    shiftLocationId?: number;
    shiftLocationName?: string;
    /** @example dd/MM/yyyy HH:mm */
    startDateTime?: string;
    /** Format: int64 */
    trainerId?: number;
    trainerName?: string;
    /** Format: double */
    trainingCost?: number;
    /** Format: int64 */
    trainingId?: number;
    trainingName?: string;
    trainingStatus?: string;
    /** Format: int32 */
    vacancies?: number;
  };
  /** TrainingSessionUpdateDto */
  TrainingSessionUpdateDto: {
    address?: string;
    /** Format: int64 */
    agencyId?: number;
    /** Format: float */
    breakTime?: number;
    /** @example dd-MM-yyyy HH:mm:ss */
    endDateTime?: string;
    /** Format: int64 */
    id?: number;
    isAgencyPaying?: boolean;
    name?: string;
    notes?: string;
    postCode?: string;
    /** Format: int64 */
    shiftLocationId?: number;
    /** @example dd-MM-yyyy HH:mm:ss */
    startDateTime?: string;
    /** Format: double */
    trainingCost?: number;
    /** Format: int64 */
    trainingId?: number;
    /** @enum {string} */
    trainingStatus?: "NEW" | "CLOSED" | "CANCELLED";
    /** Format: int32 */
    vacancies?: number;
  };
  /** TrainingUpdateDto */
  TrainingUpdateDto: {
    code?: string;
    description?: string;
    name?: string;
    /** Format: int64 */
    serviceId?: number;
    /** Format: int64 */
    trainingId?: number;
  };
  /** Transport */
  Transport: {
    /** @enum {string} */
    absconsionRisk?: "S" | "H" | "M" | "L" | "U";
    agency?: definitions["Agency"];
    allWorkers?: definitions["Worker"][];
    allergies?: string;
    /** @enum {string} */
    assaultStaff?: "S" | "H" | "M" | "L" | "U";
    authority?: string;
    baddress?: string;
    bemail?: string;
    binvoice?: string;
    bname?: string;
    bphone?: string;
    bpostCode?: string;
    /** Format: float */
    breakTime?: number;
    canOfferFood?: boolean;
    /** Format: float */
    cashHandover?: number;
    client?: definitions["Client"];
    clientInvoice?: definitions["Invoice"];
    covid?: boolean;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    dateTimeBooked?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    dateTimeRequired?: string;
    dbusiness?: string;
    dcontact?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    demail?: string;
    destination?: string;
    destinationContactNumber?: string;
    destinationPostCode?: string;
    diagnosis?: string;
    dname?: string;
    driver?: definitions["Worker"];
    /** Format: date-time */
    dropTime?: string;
    dward?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    end?: string;
    fullyBooked?: boolean;
    /** @enum {string} */
    genderIssues?: "S" | "H" | "M" | "L" | "U";
    /** Format: float */
    hoursSpent?: number;
    /** Format: int64 */
    id?: number;
    infectionControl?: string;
    isPassengerAwareOfTransport?: boolean;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    medication?: string;
    medicationItems?: string;
    mha?: string;
    /** Format: float */
    mileage?: number;
    newAddress?: string;
    newEmail?: string;
    newPhone?: string;
    newPostCode?: string;
    nhs?: string;
    offerFood?: string;
    otherRestraints?: string;
    otherRisks?: string;
    /** @enum {string} */
    passengerGender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    passengerRequiresRestraints?: boolean;
    patientDocumentOne?: string;
    patientDocumentThree?: string;
    patientDocumentTwo?: string;
    patientDocuments?: string;
    patientName?: string;
    patientRecipient?: string;
    pcaddress?: string;
    pcbusiness?: string;
    pcemail?: string;
    pcomment?: string;
    /** Format: date */
    pdob?: string;
    pdroppedOff?: boolean;
    pfAdvice?: string;
    pfCleanliness?: string;
    pfComfort?: string;
    pfCourtesy?: string;
    pfExperience?: string;
    pfKnowledge?: string;
    pfTreatment?: string;
    /** @enum {string} */
    physicalAggression?: "S" | "H" | "M" | "L" | "U";
    physicalHealth?: string;
    pickupLocationContactNumber?: string;
    pickupLocationDirectorate?: definitions["ShiftDirectorate"];
    pickupPostCode?: string;
    pmeds?: string;
    pname?: string;
    porderNum?: string;
    propertyItems?: string;
    pward?: string;
    /** @enum {string} */
    racialIssues?: "S" | "H" | "M" | "L" | "U";
    /** @enum {string} */
    rapidStatus?: "YES" | "NO" | "NA";
    rapidTranq?: string;
    rating?: definitions["Rating"];
    reasonsForRestrains?: string;
    recipientContact?: string;
    recipientRole?: string;
    recipientSignature?: string;
    ref?: string;
    /** Format: int32 */
    requiredStaff?: number;
    riskDescriptions?: string;
    riskDoc?: string;
    sbsCode?: string;
    /** @enum {string} */
    selfHarm?: "S" | "H" | "M" | "L" | "U";
    /** @enum {string} */
    selfNeglect?: "S" | "H" | "M" | "L" | "U";
    semail?: string;
    /** @enum {string} */
    sexuallyInappropriate?: "S" | "H" | "M" | "L" | "U";
    signature?: string;
    specialRequests?: string;
    sphone?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    start?: string;
    submittedBy?: string;
    teamLeader?: definitions["Worker"];
    transportReason?: string;
    /** @enum {string} */
    transportStatus?:
      | "NEW"
      | "WAITING"
      | "CLOSED"
      | "BOOKED"
      | "AUTHORIZED"
      | "PENDING"
      | "CANCELLED"
      | "EXPIRED";
    vehicle?: definitions["Vehicle"];
    /** @enum {string} */
    verballyAggressive?: "S" | "H" | "M" | "L" | "U";
    /** Format: int64 */
    version?: number;
    walk?: boolean;
    walkInfo?: string;
    /** Format: int32 */
    wardEscort?: number;
    workerSpec?: definitions["TransportWorkerSpec"][];
    workersToNotify?: definitions["Worker"][];
  };
  /** TransportApplicantsResultDto */
  TransportApplicantsResultDto: {
    applicationStatus?: string;
    assignmentCode?: string;
    cancelReason?: string;
    firstName?: string;
    lastName?: string;
    /** Format: int64 */
    transportBooking?: number;
    /** Format: int64 */
    transportId?: number;
  };
  /** TransportDto */
  TransportDto: {
    /** @enum {string} */
    absconsionRisk?: "S" | "H" | "M" | "L" | "U";
    absconsionRiskDesc?: string;
    /** Format: int64 */
    agencyId?: number;
    agencyName?: string;
    allergies?: string;
    /** @enum {string} */
    assaultStaff?: "S" | "H" | "M" | "L" | "U";
    assaultStaffDesc?: string;
    authority?: string;
    baddress?: string;
    bemail?: string;
    binvoice?: string;
    bname?: string;
    bphone?: string;
    bpostCode?: string;
    /** Format: float */
    breakTime?: number;
    canOfferFood?: boolean;
    /** Format: float */
    cashHandover?: number;
    /** Format: int64 */
    clientId?: number;
    clientName?: string;
    covid?: boolean;
    createdBy?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    dateTimeRequired?: string;
    dbusiness?: string;
    dcontact?: string;
    demail?: string;
    destination?: string;
    destinationContactNumber?: string;
    destinationPostCode?: string;
    diagnosis?: string;
    dname?: string;
    driver?: definitions["WorkerResultDto"];
    /** @example yyyy-MM-dd'T'HH:mm */
    dropTime?: string;
    dward?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    end?: string;
    endMileage?: string;
    escortServiceRisk?: string;
    /** @enum {string} */
    genderIssues?: "S" | "H" | "M" | "L" | "U";
    genderIssuesDesc?: string;
    /** Format: int64 */
    id?: number;
    infectionControl?: string;
    isPassengerAwareOfTransport?: boolean;
    lastModifiedBy?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    lastModifiedDate?: string;
    medication?: string;
    medicationList?: string[];
    mentalHealthStatus?: string;
    mha?: string;
    /** Format: float */
    mileage?: number;
    mobilityIssues?: string;
    multipartPatientDocumentOne?: unknown;
    multipartPatientDocumentThree?: unknown;
    multipartPatientDocumentTwo?: unknown;
    newAddress?: string;
    newEmail?: string;
    newPhone?: string;
    newPostCode?: string;
    nhs?: string;
    offerFood?: string;
    otherMobilityIssues?: string;
    otherRisks?: string;
    passengerAdditionalRisks?: string;
    /** Format: int32 */
    passengerAge?: number;
    /** @enum {string} */
    passengerGender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    passengerRequiresRestraints?: boolean;
    passengerWalkInfo?: string;
    patientDocumentOne?: string;
    patientDocumentThree?: string;
    patientDocumentTwo?: string;
    patientDocuments?: string;
    patientName?: string;
    patientRecipient?: string;
    pcaddress?: string;
    pcbusiness?: string;
    pcemail?: string;
    pcomment?: string;
    /** @example yyyy-MM-dd */
    pdob?: string;
    pdroppedOff?: boolean;
    /** Format: int32 */
    pfAdvice?: number;
    /** Format: int32 */
    pfCleanliness?: number;
    /** Format: int32 */
    pfComfort?: number;
    /** Format: int32 */
    pfCourtesy?: number;
    /** Format: int32 */
    pfExperience?: number;
    /** Format: int32 */
    pfKnowledge?: number;
    /** Format: int32 */
    pfTreatment?: number;
    /** @enum {string} */
    physicalAggression?: "S" | "H" | "M" | "L" | "U";
    physicalAggressionDesc?: string;
    physicalHealth?: string;
    /** Format: int64 */
    pickupDirectorateId?: number;
    pickupLocationContactNumber?: string;
    pickupPostCode?: string;
    pmeds?: string;
    pname?: string;
    porderNum?: string;
    propertyList?: string[];
    pward?: string;
    /** @enum {string} */
    racialIssues?: "S" | "H" | "M" | "L" | "U";
    racialIssuesDesc?: string;
    /** @enum {string} */
    rapidStatus?: "YES" | "NO" | "NA";
    rapidTranq?: string;
    reasonForTransport?: string;
    reasonsForRestrains?: string;
    recipientContact?: string;
    recipientRole?: string;
    recipientSignature?: string;
    refPrefix?: string;
    riskDoc?: string;
    riskDocFile?: unknown;
    sbsCode?: string;
    /** @enum {string} */
    selfHarm?: "S" | "H" | "M" | "L" | "U";
    selfHarmDesc?: string;
    /** @enum {string} */
    selfNeglect?: "S" | "H" | "M" | "L" | "U";
    selfNeglectDesc?: string;
    semail?: string;
    /** @enum {string} */
    sexuallyInappropriate?: "S" | "H" | "M" | "L" | "U";
    sexuallyInappropriateDesc?: string;
    signature?: string;
    specialRequests?: string;
    sphone?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    start?: string;
    startMileage?: string;
    submittedBy?: string;
    teamLeader?: definitions["WorkerResultDto"];
    /** Format: int64 */
    totalMinutes?: number;
    transportLegibleAgencyIds?: number[];
    /** @enum {string} */
    transportStatus?:
      | "NEW"
      | "WAITING"
      | "CLOSED"
      | "BOOKED"
      | "AUTHORIZED"
      | "PENDING"
      | "CANCELLED"
      | "EXPIRED";
    transportWorkerSpecList?: definitions["TransportWorkerSpecDto"][];
    vehicle?: definitions["VehicleDto"];
    vehicleLog?: definitions["VehicleLogDto"];
    /** @enum {string} */
    verballyAggressive?: "S" | "H" | "M" | "L" | "U";
    verballyAggressiveDesc?: string;
    walk?: boolean;
    walkInfo?: string;
    /** Format: int32 */
    wardEscort?: number;
  };
  /** TransportTeamLeaderUpdateDto */
  TransportTeamLeaderUpdateDto: {
    /** Format: float */
    cashHandover?: number;
    /** Format: date-time */
    dropTime?: string;
    /** Format: int64 */
    id?: number;
    medicationList?: string[];
    newAddress?: string;
    newEmail?: string;
    newPhone?: string;
    newPostCode?: string;
    patientRecipient?: string;
    pcomment?: string;
    pdroppedOff?: boolean;
    pfAdvice?: string;
    pfCleanliness?: string;
    pfComfort?: string;
    pfCourtesy?: string;
    pfExperience?: string;
    pfKnowledge?: string;
    pfTreatment?: string;
    propertyList?: string[];
    recipientContact?: string;
    recipientRole?: string;
    recipientSignature?: string;
    signature?: string;
  };
  /** TransportWorkerSpec */
  TransportWorkerSpec: {
    bookings?: definitions["Shift"][];
    fullyBooked?: boolean;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int64 */
    id?: number;
    newBooking?: definitions["Shift"];
    /** Format: int32 */
    numberOfStaff?: number;
    transport?: definitions["Transport"];
  };
  /** TransportWorkerSpecDto */
  TransportWorkerSpecDto: {
    /** Format: int64 */
    assignmentCode?: number;
    assignmentCodeName?: string;
    bookings?: definitions["BookingResultDto"][];
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int64 */
    id?: number;
    /** Format: int32 */
    numberOfStaff?: number;
  };
  /** TransportWorkerTimesDto */
  TransportWorkerTimesDto: {
    /** Format: int64 */
    id?: number;
    times?: definitions["WorkerTimesDto"][];
  };
  /** URI */
  URI: {
    absolute?: boolean;
    authority?: string;
    fragment?: string;
    host?: string;
    opaque?: boolean;
    path?: string;
    /** Format: int32 */
    port?: number;
    query?: string;
    rawAuthority?: string;
    rawFragment?: string;
    rawPath?: string;
    rawQuery?: string;
    rawSchemeSpecificPart?: string;
    rawUserInfo?: string;
    scheme?: string;
    schemeSpecificPart?: string;
    userInfo?: string;
  };
  /** URL */
  URL: {
    authority?: string;
    content?: { [key: string]: unknown };
    /** Format: int32 */
    defaultPort?: number;
    file?: string;
    host?: string;
    path?: string;
    /** Format: int32 */
    port?: number;
    protocol?: string;
    query?: string;
    ref?: string;
    userInfo?: string;
  };
  /** URLStreamHandler */
  URLStreamHandler: { [key: string]: unknown };
  /** UserResponse */
  UserResponse: {
    /** Format: int64 */
    agentId?: number;
    /** Format: int64 */
    clientId?: number;
    email?: string;
    enabled?: string;
    firstname?: string;
    /** Format: int64 */
    id?: number;
    lastname?: string;
    username?: string;
  };
  /** VatRate */
  VatRate: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    vatRate?: number;
    /** Format: int64 */
    version?: number;
  };
  /** VatRateCreateDto */
  VatRateCreateDto: {
    vatRate?: number;
  };
  /** VatRateDto */
  VatRateDto: {
    /** Format: int64 */
    id?: number;
    vatRate?: number;
  };
  /** Vehicle */
  Vehicle: {
    airConditioning?: boolean;
    approved?: boolean;
    /** Format: float */
    capacity?: number;
    color?: string;
    contactAddress?: string;
    contactPerson?: string;
    contactPhone?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: float */
    depositAmt?: number;
    description?: string;
    /** Format: int32 */
    doors?: number;
    engineNumber?: string;
    engineSize?: string;
    /** Format: float */
    excessMileageRate?: number;
    forRental?: boolean;
    /** @enum {string} */
    fuelType?:
      | "PETROL"
      | "PHYBRID"
      | "DIESEL"
      | "DHYBRID"
      | "ELECTRIC"
      | "HYDROGEN";
    /** Format: float */
    hourlyRate?: number;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    mainPhoto?: string;
    /** Format: int32 */
    maxAge?: number;
    /** Format: float */
    maxDailyMileage?: number;
    /** Format: float */
    mileage?: number;
    /** Format: float */
    mileageRate?: number;
    /** Format: int32 */
    minAge?: number;
    /** Format: int32 */
    minHireDays?: number;
    model?: string;
    name?: string;
    notes?: string;
    regno?: string;
    /** Format: int32 */
    seats?: number;
    /** @enum {string} */
    status?: "AWAITING" | "EDITED" | "DISABLED" | "AVAILABLE" | "REJECTED";
    trackerId?: string;
    /** @enum {string} */
    transmissionType?: "AUTO" | "MANUAL";
    /** @enum {string} */
    type?:
      | "SUV"
      | "PICKUP"
      | "DEFAULT"
      | "SEDAN"
      | "HATCHBACK"
      | "COUPE"
      | "SPECIAL"
      | "CONVERTIBLE"
      | "MPV"
      | "MINIVAN"
      | "STATION_WAGON"
      | "CROSSOVER"
      | "SPORTS_CAR";
    vehicleAvailabilities?: definitions["VehicleAvailability"][];
    vehicleDocuments?: definitions["VehicleDocument"][];
    vehicleInventories?: definitions["VehicleInventory"][];
    vehiclePhotos?: definitions["VehiclePhoto"][];
    /** Format: int64 */
    version?: number;
    zone?: definitions["ZoneId"];
  };
  /** VehicleAvailability */
  VehicleAvailability: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd */
    date?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: int64 */
    version?: number;
  };
  /** VehicleAvailabilityDto */
  VehicleAvailabilityDto: {
    bookedDates?: definitions["LocalDate"][];
    message?: string;
    unavailableDates?: definitions["LocalDate"][];
    /** Format: int64 */
    vehicleId?: number;
  };
  /** VehicleAvailabilityUpdateDto */
  VehicleAvailabilityUpdateDto: {
    dates?: definitions["LocalDate"][];
  };
  /** VehicleBooking */
  VehicleBooking: {
    byAgency?: boolean;
    cancelReason?: string;
    checkoutItems?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    damageImage?: string;
    damageImageIn?: string;
    damageInfoIn?: definitions["DamageInfo"][];
    damageInfoOut?: definitions["DamageInfo"][];
    damagePhotos?: definitions["VehicleBookingPhoto"][];
    datesBooked?: definitions["LocalDate"][];
    /** Format: int32 */
    daysHired?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    email?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    end?: string;
    /** Format: date-time */
    endZoned?: string;
    extraCharges?: definitions["DamageInfo"][];
    firstname?: string;
    /** Format: int32 */
    fuelIn?: number;
    /** Format: int32 */
    fuelOut?: number;
    /** Format: int64 */
    id?: number;
    invoices?: definitions["Invoice"][];
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: int32 */
    mileageIn?: number;
    /** Format: int32 */
    mileageOut?: number;
    phone?: string;
    rating?: definitions["Rating"];
    reminderSent?: boolean;
    returnItems?: string;
    signatureIn?: string;
    /** Format: date-time */
    signatureInDate?: string;
    /** Format: date-time */
    signatureInDateZoned?: string;
    signatureInName?: string;
    signatureOut?: string;
    /** Format: date-time */
    signatureOutDate?: string;
    /** Format: date-time */
    signatureOutDateZoned?: string;
    signatureOutName?: string;
    /** @example dd-MM-yyyy HH:mm:ss */
    start?: string;
    /** Format: date-time */
    startZoned?: string;
    /** @enum {string} */
    status?:
      | "RESERVED"
      | "BOOKED"
      | "AUTHORIZED"
      | "WAITINGAUTH"
      | "COMPLETE"
      | "CANCELLED";
    surname?: string;
    /** Format: int64 */
    version?: number;
  };
  /** VehicleBookingDepositDto */
  VehicleBookingDepositDto: {
    /** Format: int64 */
    bookingId?: number;
    depositAmount?: number;
    /** @example yyyy-MM-dd'T'HH:mm */
    depositDate?: string;
    /** Format: int64 */
    id?: number;
    paymentMethod?: string;
  };
  /** VehicleBookingDto */
  VehicleBookingDto: {
    byAgency?: boolean;
    cancelReason?: string;
    checkoutItems?: string;
    client?: definitions["ClientDto"];
    /** Format: int64 */
    clientId?: number;
    damageImage?: string;
    damageImageIn?: string;
    damageInfoIn?: definitions["DamageInfo"][];
    damageInfoOut?: definitions["DamageInfo"][];
    damagePhotos?: definitions["VehicleBookingPhoto"][];
    deposit?: number;
    discount?: number;
    email?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    end?: string;
    extraCharges?: definitions["DamageInfo"][];
    firstname?: string;
    /** Format: int32 */
    fuelIn?: number;
    /** Format: int32 */
    fuelOut?: number;
    fullPayment?: boolean;
    /** @enum {string} */
    gatewayType?: "PAYNOW" | "STRIPE";
    /** Format: int64 */
    id?: number;
    invoices?: definitions["InvoiceResult"][];
    location?: definitions["Location"];
    /** Format: int32 */
    mileageIn?: number;
    /** Format: int32 */
    mileageOut?: number;
    /** Format: double */
    paidAmount?: number;
    paymentRedirectUrl?: string;
    phone?: string;
    promoCode?: string;
    promotion?: definitions["PromotionDto"];
    rating?: definitions["Rating"];
    ratings?: definitions["Rating"][];
    returnItems?: string;
    signatureIn?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    signatureInDate?: string;
    signatureInName?: string;
    signatureOut?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    signatureOutDate?: string;
    signatureOutName?: string;
    skipEmails?: boolean;
    /** @example yyyy-MM-dd'T'HH:mm */
    start?: string;
    /** @enum {string} */
    status?:
      | "RESERVED"
      | "BOOKED"
      | "AUTHORIZED"
      | "WAITINGAUTH"
      | "COMPLETE"
      | "CANCELLED";
    surname?: string;
    vehicle?: definitions["VehicleDto"];
    vehicleAddons?: definitions["VehicleInventory"][];
    vehicleAddonsIds?: number[];
    /** Format: int64 */
    vehicleId?: number;
  };
  /** VehicleBookingPhoto */
  VehicleBookingPhoto: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    url?: string;
    /** Format: int64 */
    version?: number;
  };
  /** VehicleDocument */
  VehicleDocument: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** @example yyyy-MM-dd */
    expiryDate?: string;
    /** Format: int32 */
    expiryMileage?: number;
    /** Format: int64 */
    id?: number;
    /** Format: date */
    lastDate?: string;
    /** Format: int32 */
    lastMileage?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    /** @enum {string} */
    status?:
      | "NEW"
      | "WAITING_APPROVAL"
      | "REJECTED"
      | "CLOSED"
      | "BOOKED"
      | "APPROVED"
      | "CANCELLED";
    url?: string;
    /** Format: int64 */
    version?: number;
  };
  /** VehicleDto */
  VehicleDto: {
    agency?: definitions["Agency"];
    airConditioning?: boolean;
    /** Format: float */
    capacity?: number;
    color?: string;
    contactAddress?: string;
    contactPerson?: string;
    contactPhone?: string;
    /** Format: float */
    depositAmt?: number;
    description?: string;
    documents?: definitions["VehicleDocument"][];
    /** Format: int32 */
    doors?: number;
    engineNumber?: string;
    engineSize?: string;
    /** Format: float */
    excessMileageRate?: number;
    forRental?: boolean;
    /** @enum {string} */
    fuelType?:
      | "PETROL"
      | "PHYBRID"
      | "DIESEL"
      | "DHYBRID"
      | "ELECTRIC"
      | "HYDROGEN";
    /** Format: float */
    hourlyRate?: number;
    /** Format: int64 */
    id?: number;
    inventory?: definitions["VehicleInventory"][];
    /** Format: int64 */
    jobCount?: number;
    location?: definitions["Location"];
    mainPhoto?: string;
    /** Format: int32 */
    maxAge?: number;
    /** Format: float */
    maxDailyMileage?: number;
    /** Format: float */
    mileage?: number;
    /** Format: float */
    mileageRate?: number;
    /** Format: int32 */
    minAge?: number;
    /** Format: int32 */
    minHireDays?: number;
    model?: string;
    name?: string;
    notes?: string;
    photos?: definitions["VehiclePhoto"][];
    promotions?: definitions["PromotionDto"][];
    /** Format: float */
    rating?: number;
    ratings?: definitions["Rating"][];
    regno?: string;
    /** Format: int32 */
    seats?: number;
    /** @enum {string} */
    status?: "AWAITING" | "EDITED" | "DISABLED" | "AVAILABLE" | "REJECTED";
    trackerId?: string;
    /** @enum {string} */
    transmissionType?: "AUTO" | "MANUAL";
    /** @enum {string} */
    type?:
      | "SUV"
      | "PICKUP"
      | "DEFAULT"
      | "SEDAN"
      | "HATCHBACK"
      | "COUPE"
      | "SPECIAL"
      | "CONVERTIBLE"
      | "MPV"
      | "MINIVAN"
      | "STATION_WAGON"
      | "CROSSOVER"
      | "SPORTS_CAR";
    vehicleAddons?: definitions["VehicleInventory"][];
    vehicleAvailabilities?: definitions["VehicleAvailability"][];
    vehicleRates?: definitions["VehicleRate"][];
  };
  /** VehicleFilterDto */
  VehicleFilterDto: {
    /** Format: int64 */
    agencyId?: number;
    /** @enum {string} */
    colorOperator?:
      | "EQUALITY"
      | "NON_EQUALITY"
      | "GREATER_THAN"
      | "LESS_THAN"
      | "GREATER_THAN_OR_EQUAL"
      | "LESS_THAN_OR_EQUAL"
      | "CONTAINS"
      | "NOT_CONTAINS"
      | "IN"
      | "NOT_IN";
    colors?: string[];
    /** @enum {string} */
    fuelTypeOperator?:
      | "EQUALITY"
      | "NON_EQUALITY"
      | "GREATER_THAN"
      | "LESS_THAN"
      | "GREATER_THAN_OR_EQUAL"
      | "LESS_THAN_OR_EQUAL"
      | "CONTAINS"
      | "NOT_CONTAINS"
      | "IN"
      | "NOT_IN";
    fuelTypes?: (
      | "PETROL"
      | "PHYBRID"
      | "DIESEL"
      | "DHYBRID"
      | "ELECTRIC"
      | "HYDROGEN"
    )[];
    /** Format: int64 */
    id?: number;
    locationIds?: number[];
    /** @enum {string} */
    locationOperator?:
      | "EQUALITY"
      | "NON_EQUALITY"
      | "GREATER_THAN"
      | "LESS_THAN"
      | "GREATER_THAN_OR_EQUAL"
      | "LESS_THAN_OR_EQUAL"
      | "CONTAINS"
      | "NOT_CONTAINS"
      | "IN"
      | "NOT_IN";
    /** @enum {string} */
    modelOperator?:
      | "EQUALITY"
      | "NON_EQUALITY"
      | "GREATER_THAN"
      | "LESS_THAN"
      | "GREATER_THAN_OR_EQUAL"
      | "LESS_THAN_OR_EQUAL"
      | "CONTAINS"
      | "NOT_CONTAINS"
      | "IN"
      | "NOT_IN";
    models?: string[];
    /** @enum {string} */
    nameOperator?:
      | "EQUALITY"
      | "NON_EQUALITY"
      | "GREATER_THAN"
      | "LESS_THAN"
      | "GREATER_THAN_OR_EQUAL"
      | "LESS_THAN_OR_EQUAL"
      | "CONTAINS"
      | "NOT_CONTAINS"
      | "IN"
      | "NOT_IN";
    names?: string[];
    /** @enum {string} */
    typeOperator?:
      | "EQUALITY"
      | "NON_EQUALITY"
      | "GREATER_THAN"
      | "LESS_THAN"
      | "GREATER_THAN_OR_EQUAL"
      | "LESS_THAN_OR_EQUAL"
      | "CONTAINS"
      | "NOT_CONTAINS"
      | "IN"
      | "NOT_IN";
    types?: (
      | "SUV"
      | "PICKUP"
      | "DEFAULT"
      | "SEDAN"
      | "HATCHBACK"
      | "COUPE"
      | "SPECIAL"
      | "CONVERTIBLE"
      | "MPV"
      | "MINIVAN"
      | "STATION_WAGON"
      | "CROSSOVER"
      | "SPORTS_CAR"
    )[];
    vehicleIds?: number[];
    /** @enum {string} */
    vehicleOperator?:
      | "EQUALITY"
      | "NON_EQUALITY"
      | "GREATER_THAN"
      | "LESS_THAN"
      | "GREATER_THAN_OR_EQUAL"
      | "LESS_THAN_OR_EQUAL"
      | "CONTAINS"
      | "NOT_CONTAINS"
      | "IN"
      | "NOT_IN";
  };
  /** VehicleInventory */
  VehicleInventory: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd */
    dateInstalled?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    description?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    name?: string;
    /** @example yyyy-MM-dd */
    nextCheckDate?: string;
    photoUrl1?: string;
    photoUrl2?: string;
    photoUrl3?: string;
    photoUrl4?: string;
    /** Format: float */
    price?: number;
    /** Format: int64 */
    vehicleId?: number;
    /** Format: int64 */
    version?: number;
  };
  /** VehicleLogDto */
  VehicleLogDto: {
    adblue?: boolean;
    approvedBy?: string;
    approvedByName?: string;
    battery?: boolean;
    body?: boolean;
    brakes?: boolean;
    cellArea?: boolean;
    comment?: string;
    coolant?: boolean;
    /** Format: date-time */
    createdDate?: string;
    damageDescriptions?: string;
    damageDoc?: string;
    damageReport?: string;
    doors?: boolean;
    drivingControls?: boolean;
    drivingcontrol?: boolean;
    /** Format: float */
    endMileage?: number;
    engineCheckLight?: boolean;
    equipment?: boolean;
    exhaust?: boolean;
    feedback?: string;
    fluidleaks?: boolean;
    formComplete?: string;
    fuel?: boolean;
    hardSurface?: boolean;
    horn?: boolean;
    /** Format: int64 */
    id?: number;
    indicators?: boolean;
    instrumentalPanel?: boolean;
    lamp?: boolean;
    litter?: boolean;
    markers?: boolean;
    mirror?: boolean;
    mirrors?: boolean;
    notes?: string;
    oil?: boolean;
    reflectors?: boolean;
    sanitizer?: boolean;
    seatbelt?: boolean;
    seats?: boolean;
    sideReapter?: boolean;
    speedometer?: boolean;
    /** Format: float */
    startMileage?: number;
    /** @enum {string} */
    status?: "NEW" | "WAITING_APPROVAL" | "APPROVED";
    stoplamp?: boolean;
    /** Format: float */
    totalMileage?: number;
    /** Format: int64 */
    totalMinutes?: number;
    trailercoupling?: boolean;
    transport?: definitions["TransportDto"];
    /** Format: int64 */
    transportId?: number;
    /** @enum {string} */
    type?: "DRIVER" | "CLEANING";
    tyreInflation?: boolean;
    vehicle?: string;
    /** Format: int64 */
    vehicleId?: number;
    warningLight?: boolean;
    warningdevices?: boolean;
    washers?: boolean;
    wheelCondition?: boolean;
    windscreen?: boolean;
    wipers?: boolean;
    worker?: string;
  };
  /** VehiclePhoto */
  VehiclePhoto: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    url?: string;
    /** Format: int64 */
    version?: number;
  };
  /** VehiclePhotoDto */
  VehiclePhotoDto: {
    urls?: string[];
    /** Format: int64 */
    vehicleId?: number;
  };
  /** VehicleRate */
  VehicleRate: {
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** Format: float */
    rate?: number;
    /** Format: int64 */
    version?: number;
    /** @enum {string} */
    weekDay?:
      | "MON"
      | "TUE"
      | "WED"
      | "THU"
      | "FRI"
      | "SAT"
      | "SUN"
      | "MTF"
      | "SP";
  };
  /** Worker */
  Worker: {
    address?: string;
    assignmentCode?: definitions["AssignmentCode"];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    cv?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    /** Format: int64 */
    deputyId?: number;
    /** Format: date */
    dob?: string;
    email?: string;
    employmentNumber?: string;
    firstname?: string;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int64 */
    hascoId?: number;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    lastname?: string;
    nationality?: string;
    phoneNumber?: string;
    postcode?: string;
    profilePic?: string;
    /** @enum {string} */
    status?: "APPLICANT" | "APPROVED" | "WAITING" | "REJECTED";
    username?: string;
    /** Format: int64 */
    version?: number;
  };
  /** WorkerComplianceResultDto */
  WorkerComplianceResultDto: {
    agencyId?: string;
    code?: string;
    comment?: string;
    complianceDate?: string;
    complianceExpiry?: string;
    complianceId?: string;
    description?: string;
    document?: string;
    getComplianceId?: string;
    /** Format: int64 */
    id?: number;
    name?: string;
    serviceId?: string;
    status?: string;
    uploaded?: boolean;
    workerId?: string;
  };
  /** WorkerComplianceUpdateDto */
  WorkerComplianceUpdateDto: {
    /** Format: int64 */
    agencyId?: number;
    comment?: string;
    /** @example yyyy-MM-dd */
    complianceDate?: string;
    /** @example yyyy-MM-dd */
    complianceExpiry?: string;
    description?: string;
    /** Format: int64 */
    id?: number;
    status?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** WorkerCreateDto */
  WorkerCreateDto: {
    address?: string;
    /** Format: int64 */
    agencyId?: number;
    /** Format: int64 */
    assignmentCode?: number;
    /** Format: date-time */
    createdDate?: string;
    cv?: string;
    /** Format: int64 */
    deputyId?: number;
    /** @example yyyy-MM-dd */
    dob?: string;
    email?: string;
    employmentNumber?: string;
    firstname?: string;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    lastname?: string;
    nationality?: string;
    phoneNumber?: string;
    postcode?: string;
    profilePic?: string;
    username?: string;
  };
  /** WorkerResultDto */
  WorkerResultDto: {
    address?: string;
    /** Format: int64 */
    agencyId?: number;
    agencyName?: string;
    assignmentCode?: string;
    /** Format: int64 */
    assignmentCodeId?: number;
    assignmentName?: string;
    compliant?: boolean;
    createdBy?: string;
    cv?: string;
    /** @example dd/MM/yyyy */
    dob?: string;
    email?: string;
    employmentNumber?: string;
    firstname?: string;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    grossPay?: string;
    /** Format: int64 */
    id?: number;
    /** Format: int64 */
    ihascoId?: number;
    lastname?: string;
    nationality?: string;
    phoneNumber?: string;
    postcode?: string;
    profilePic?: string;
    /** @enum {string} */
    status?: "APPLICANT" | "APPROVED" | "WAITING" | "REJECTED";
    totalPayAdvices?: string;
    totalPayslips?: string;
    username?: string;
  };
  /** WorkerStats */
  WorkerStats: {
    /** Format: int32 */
    numberOfClients?: number;
    /** Format: int32 */
    numberOfRegisteredAgencies?: number;
    /** Format: int32 */
    numberOfShifts?: number;
  };
  /** WorkerTimesDto */
  WorkerTimesDto: {
    /** Format: int64 */
    bookingId?: number;
    /** @example yyyy-MM-dd'T'HH:mm */
    end?: string;
    /** @example yyyy-MM-dd'T'HH:mm */
    start?: string;
  };
  /** WorkerTrainingResultDto */
  WorkerTrainingResultDto: {
    agency?: definitions["AgencyResultDto"];
    code?: string;
    comment?: string;
    description?: string;
    document?: string;
    /** Format: int64 */
    hascoCourseId?: number;
    /** Format: int64 */
    hascoId?: number;
    /** Format: int64 */
    id?: number;
    name?: string;
    serviceId?: string;
    showCertificate?: boolean;
    status?: string;
    supervisor?: string;
    trainingDate?: string;
    trainingExpiry?: string;
    /** @enum {string} */
    type?: "EXTERNAL" | "ONLINE" | "PHYSICAL";
    uploaded?: boolean;
    worker?: definitions["WorkerResultDto"];
    workerId?: string;
  };
  /** WorkerTrainingSession */
  WorkerTrainingSession: {
    agency?: definitions["Agency"];
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** @example dd-MM-yyyy */
    dateUploaded?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    feedbackSkipped?: boolean;
    /** Format: int64 */
    id?: number;
    isAdminBilled?: boolean;
    isAgencyBilled?: boolean;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    passedTraining?: boolean;
    showCertificate?: boolean;
    skippedTraining?: boolean;
    training?: definitions["Training"];
    /** @example dd-MM-yyyy */
    trainingExpiryDate?: string;
    /** Format: int32 */
    trainingScore?: number;
    trainingSession?: definitions["TrainingSession"];
    /** @enum {string} */
    trainingStatus?:
      | "NEW"
      | "AUTHORIZED"
      | "WAITING_AUTHORIZATION"
      | "CLOSED"
      | "BOOKED"
      | "CANCELLED"
      | "REJECTED";
    /** Format: int64 */
    version?: number;
    worker?: definitions["Worker"];
  };
  /** WorkerTrainingSessionAuthorizeDto */
  WorkerTrainingSessionAuthorizeDto: {
    generateCertificate?: boolean;
    passedTraining?: boolean;
    /** Format: int32 */
    score?: number;
    skippedTraining?: boolean;
    /** @example yyyy-MM-dd */
    trainingExpiryDate?: string;
    /** Format: int64 */
    workerTrainingBookingId?: number;
  };
  /** WorkerTrainingSessionResultsDto */
  WorkerTrainingSessionResultsDto: {
    address?: string;
    agencyName?: string;
    /** Format: double */
    breakTime?: number;
    /** Format: double */
    costPerTrainee?: number;
    /** @example dd/MM/yyyy */
    dateUploaded?: string;
    /** @example dd/MM/yyyy HH:mm */
    endDateTime?: string;
    /** Format: int64 */
    id?: number;
    isAgencyPaying?: boolean;
    notes?: string;
    passedTraining?: boolean;
    postCode?: string;
    shiftLocationName?: string;
    showCertificate?: boolean;
    skippedTraining?: boolean;
    /** @example dd/MM/yyyy HH:mm */
    startDateTime?: string;
    /** Format: int64 */
    trainerId?: number;
    trainerName?: string;
    /** @example dd/MM/yyyy */
    trainingExpiryDate?: string;
    /** Format: int64 */
    trainingId?: number;
    trainingName?: string;
    /** Format: int32 */
    trainingScore?: number;
    trainingSessionName?: string;
    /** @enum {string} */
    trainingStatus?:
      | "NEW"
      | "AUTHORIZED"
      | "WAITING_AUTHORIZATION"
      | "CLOSED"
      | "BOOKED"
      | "CANCELLED"
      | "REJECTED";
    workerName?: string;
  };
  /** WorkerTrainingUpdateDto */
  WorkerTrainingUpdateDto: {
    /** Format: int64 */
    agencyId?: number;
    comment?: string;
    description?: string;
    /** Format: int64 */
    id?: number;
    status?: string;
    /** @example yyyy-MM-dd */
    trainingDate?: string;
    /** @example yyyy-MM-dd */
    trainingExpiry?: string;
    /** Format: int64 */
    workerId?: number;
  };
  /** WorkerUpdateDto */
  WorkerUpdateDto: {
    address?: string;
    /** Format: int64 */
    agencyId?: number;
    /** Format: int64 */
    assignmentCodeId?: number;
    cv?: string;
    /** @example yyyy-MM-dd */
    dob?: string;
    email?: string;
    employmentNumber?: string;
    firstname?: string;
    /** @enum {string} */
    gender?: "MALE" | "FEMALE" | "NO_PREFERENCE";
    /** Format: int64 */
    id?: number;
    lastname?: string;
    nationality?: string;
    phoneNumber?: string;
    postcode?: string;
    profilePic?: string;
    /** @enum {string} */
    status?:
      | "ACTIVE"
      | "INACTIVE"
      | "APPROVED"
      | "WAITING"
      | "CANCELLED"
      | "COMPLETED"
      | "REJECTED"
      | "NEW";
    username?: string;
  };
  /** ZoneId */
  ZoneId: {
    id?: string;
    rules?: definitions["ZoneRules"];
  };
  /** ZoneOffset */
  ZoneOffset: {
    id?: string;
    rules?: definitions["ZoneRules"];
    /** Format: int32 */
    totalSeconds?: number;
  };
  /** ZoneOffsetTransition */
  ZoneOffsetTransition: {
    /** Format: date-time */
    dateTimeAfter?: string;
    /** Format: date-time */
    dateTimeBefore?: string;
    duration?: definitions["Duration"];
    gap?: boolean;
    /** Format: date-time */
    instant?: string;
    offsetAfter?: definitions["ZoneOffset"];
    offsetBefore?: definitions["ZoneOffset"];
    overlap?: boolean;
  };
  /** ZoneOffsetTransitionRule */
  ZoneOffsetTransitionRule: {
    /** Format: int32 */
    dayOfMonthIndicator?: number;
    /** @enum {string} */
    dayOfWeek?:
      | "MONDAY"
      | "TUESDAY"
      | "WEDNESDAY"
      | "THURSDAY"
      | "FRIDAY"
      | "SATURDAY"
      | "SUNDAY";
    localTime?: definitions["LocalTime"];
    midnightEndOfDay?: boolean;
    /** @enum {string} */
    month?:
      | "JANUARY"
      | "FEBRUARY"
      | "MARCH"
      | "APRIL"
      | "MAY"
      | "JUNE"
      | "JULY"
      | "AUGUST"
      | "SEPTEMBER"
      | "OCTOBER"
      | "NOVEMBER"
      | "DECEMBER";
    offsetAfter?: definitions["ZoneOffset"];
    offsetBefore?: definitions["ZoneOffset"];
    standardOffset?: definitions["ZoneOffset"];
    /** @enum {string} */
    timeDefinition?: "UTC" | "WALL" | "STANDARD";
  };
  /** ZoneRules */
  ZoneRules: {
    fixedOffset?: boolean;
    transitionRules?: definitions["ZoneOffsetTransitionRule"][];
    transitions?: definitions["ZoneOffsetTransition"][];
  };
}

export interface operations {
  getSettlementsByFiltersUsingGET: {
    parameters: {
      query: {
        /** amount */
        amount?: number;
        /** carRentalId */
        carRentalId?: number;
        /** createdDateEnd */
        createdDateEnd?: string;
        /** createdDateStart */
        createdDateStart?: string;
        /** id */
        id?: number;
        /** page */
        page?: number;
        /** size */
        size?: number;
        /** status */
        status?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«SettlementStatement»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAdminBillingUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId?: number;
        /** endDate */
        endDate?: string;
        /** page */
        page: number;
        /** size */
        size: number;
        /** startDate */
        startDate?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllAgencyShiftsByStatusUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["AdminStats"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAgenciesUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findDefaultAgenciesUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AgencyResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET: {
    parameters: {
      query: {
        /** agencyTypeFilter */
        agencyTypeFilter?: "DEFAULT" | "TRAINER" | "TRANSPORTER";
        /** direction */
        direction?: string;
        /** searchQuery */
        searchQuery?: string;
        /** sort */
        sort?: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AgencyResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT: {
    parameters: {
      body: {
        /** agencyUpdateDto */
        agencyUpdateDto: definitions["AgencyUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST: {
    parameters: {
      body: {
        /** agencyCreateDto */
        agencyCreateDto: definitions["AgencyCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  activateAgencyUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllWorkersByCodeUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** assignmentCodeName */
        assignmentCodeName: string;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllApplicantsUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateClientUsingPUT: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** payerId */
        payerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllClientsUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ClientDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findNumberOfAgenciesUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: number;
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deActivateAgencyUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchAgencyUsingPOST: {
    parameters: {
      query: {
        /** direction */
        direction?: string;
        /** page */
        page?: number;
        /** searchCriteria */
        searchCriteria?: string;
        /** size */
        size?: number;
        /** sort */
        sort?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AgencyResultDto»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchAgencyPathVariableUsingPOST: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AgencyResultDto»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencySettingsUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencySettings"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_2: {
    parameters: {
      body: {
        /** settingsCreateDto */
        settingsCreateDto: definitions["AgencySettingsCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencySettings"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_2: {
    parameters: {
      body: {
        /** createDto */
        createDto: definitions["AgencySettingsCreateDto"];
      };
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencySettings"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllAgencyShiftsByStatusUsingGET_1: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByClientIdUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByAgencyIdUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: string;
      };
      query: {
        /** endDate */
        endDate?: string;
        /** payerId */
        payerId?: number;
        /** startDate */
        startDate?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByAgencyIdAndStatusUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** endDate */
        endDate: string;
        /** startDate */
        startDate: string;
        /** status */
        status?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencyStatsUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyStats"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getForAgencyUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Training"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createorUpdateAgencyWorkerPropertiesUsingPOST: {
    parameters: {
      body: {
        /** agencyWorkerPropertiesCreateDto */
        agencyWorkerPropertiesCreateDto: definitions["AgencyWorkerPropertiesCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyWorkerProperties"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  activateApplicantUsingPUT: {
    parameters: {
      body: {
        /** agencyWorkerPropertiesCreateDto */
        agencyWorkerPropertiesCreateDto: definitions["AgencyWorkerPropertiesCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyWorkerProperties"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  activateWorkerUsingPUT: {
    parameters: {
      body: {
        /** agencyWorkerPropertiesCreateDto */
        agencyWorkerPropertiesCreateDto: definitions["AgencyWorkerPropertiesCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyWorkerProperties"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deactivateApplicantUsingPUT: {
    parameters: {
      body: {
        /** agencyWorkerPropertiesCreateDto */
        agencyWorkerPropertiesCreateDto: definitions["AgencyWorkerPropertiesCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deactivateWorkerUsingPUT: {
    parameters: {
      body: {
        /** agencyWorkerPropertiesCreateDto */
        agencyWorkerPropertiesCreateDto: definitions["AgencyWorkerPropertiesCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyWorkerProperties"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteAgencyWorkerPropertiesUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAgencyWorkerPropertiesUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["IAgencyWorkerProperties"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllWorkersPendingShiftsUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllWorkersUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateBankDetailsUsingPUT: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
      body: {
        /** bankDetails */
        bankDetails: definitions["BankDetails"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_1: {
    parameters: {
      body: {
        /** expenseRateUpdateDto */
        expenseRateUpdateDto: definitions["AgencyExpenseRateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_1: {
    parameters: {
      body: {
        /** expenseRateDto */
        expenseRateDto: definitions["AgencyExpenseRateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ExpenseRate"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAgencyExpenseRatesUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyExpenseRate"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  inviteWorkerUsingPOST: {
    parameters: {
      body: {
        /** inviteWorkerRequestDto */
        inviteWorkerRequestDto: definitions["InviteWorkerRequestDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadProfileImageUsingPOST: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
      };
      formData: {
        /** file */
        file: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIsTransporterUsingGET: {
    parameters: {
      path: {
        /** id */
        id: boolean;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_1: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  searchApplicantUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencyApplicantsUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** shiftId */
        shiftId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  applyUsingPUT: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookUsingPUT: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_3: {
    parameters: {
      body: {
        /** assignmentCodeUpdateDto */
        assignmentCodeUpdateDto: definitions["AssignmentCodeUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_3: {
    parameters: {
      body: {
        /** assignmentCodeCreateDto */
        assignmentCodeCreateDto: definitions["AssignmentCodeCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByAgencyIdUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByClientIdUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_4: {
    parameters: {
      body: {
        /** assignmentCodeUpdateDto */
        assignmentCodeUpdateDto: definitions["AssignmentCodeRateUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeRateResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_5: {
    parameters: {
      body: {
        /** assignmentCodeCreateDto */
        assignmentCodeCreateDto: definitions["AssignmentCodeRateCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeRateResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_4: {
    parameters: {
      body: {
        /** assignmentCodeCreateDto */
        assignmentCodeCreateDto: definitions["AssignmentCodeRateCreateDto"][];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_5: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeRateResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_2: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_7: {
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeRateResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByByAgentIdUsingGET: {
    parameters: {
      query: {
        /** day */
        day?: string;
      };
      path: {
        /** payeeId */
        payeeId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeRateResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByByClientIdUsingGET: {
    parameters: {
      query: {
        /** day */
        day?: string;
      };
      path: {
        /** payerId */
        payerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeRateResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_6: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AssignmentCodeRateResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_2: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_1: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_4: {
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByCodeUsingGET: {
    parameters: {
      path: {
        /** code */
        code: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AssignmentCodeResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_3: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AssignmentCodeResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizeQueriedUsingPUT: {
    parameters: {
      path: {
        /** endTime */
        endTime: string;
        /** payerId */
        payerId: number;
        /** shiftId */
        shiftId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizeUsingPUT: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateAvailabilityUsingPOST: {
    parameters: {
      body: {
        /** availabilityCreateDto */
        availabilityCreateDto: definitions["AvailabilityCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Availability"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteAvailabiltyUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findWorkerAvailabilityUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«IAvailabilityResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_6: {
    parameters: {
      body: {
        /** bankCreateDto */
        bankCreateDto: definitions["BankCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Bank"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByWorkerIdUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BankResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_8: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BankResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_3: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  createDebitNoteUsingPOST_1: {
    parameters: {
      body: {
        /** shiftBillDto */
        shiftBillDto: definitions["ShiftBillDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  SetDebitNotePaidStatusUsingPUT_1: {
    parameters: {
      body: {
        /** agencyBillStatusDto */
        agencyBillStatusDto: definitions["AgencyBillStatusDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listAllBillsByStatusUsingPOST_1: {
    parameters: {
      query: {
        /** endDate */
        endDate?: string;
        /** payeeId */
        payeeId?: number;
        /** startDate */
        startDate?: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyBillDto"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_11: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyBillDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listAllBillsUsingGET_1: {
    parameters: {
      query: {
        /** endDate */
        endDate?: string;
        /** payeeId */
        payeeId?: number;
        /** startDate */
        startDate?: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyBillDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookUsingPUT_1: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_7: {
    parameters: {
      formData: {
        /** body */
        body: unknown;
        /** sendToAll */
        sendToAll: unknown;
        /** sendToAssCodes */
        sendToAssCodes: unknown;
        /** sendToIds */
        sendToIds: unknown;
        /** sendToType */
        sendToType: unknown;
        /** senderType */
        senderType: unknown;
        /** sendingAgencyId */
        sendingAgencyId: unknown;
        /** sendingClientId */
        sendingClientId: unknown;
        /** title */
        title: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Notification"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createWithAttachemntUsingPOST: {
    parameters: {
      formData: {
        /** body */
        body: unknown;
        /** file */
        file: unknown;
        /** sendToAll */
        sendToAll: unknown;
        /** sendToAssCodes */
        sendToAssCodes: unknown;
        /** sendToIds */
        sendToIds: unknown;
        /** sendToType */
        sendToType: unknown;
        /** senderType */
        senderType: unknown;
        /** sendingAgencyId */
        sendingAgencyId: unknown;
        /** sendingClientId */
        sendingClientId: unknown;
        /** title */
        title: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Notification"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  cancelWorkerShiftUsingPUT: {
    parameters: {
      path: {
        /** reason */
        reason: string;
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  cancelUsingPUT: {
    parameters: {
      path: {
        /** payerId */
        payerId: number;
        /** reason */
        reason: string;
        /** shiftId */
        shiftId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET_1: {
    responses: {
      /** OK */
      200: {
        schema: definitions["ChargeRate"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_5: {
    parameters: {
      body: {
        /** chargeRateUpdateDto */
        chargeRateUpdateDto: definitions["ChargeRateUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ChargeRate"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_8: {
    parameters: {
      body: {
        /** chargeRateDto */
        chargeRateDto: definitions["ChargeRateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_9: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ChargeRate"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_5: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_10: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ChargeRate»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findChatGroupMessagesByShiftIdAndWorkerIdUsingGET: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ChatGroupMessageResponseDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findChatGroupMessagesByChatGroupIdUsingGET: {
    parameters: {
      path: {
        /** groupId */
        groupId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ChatGroupMessageResponseDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findChatGroupByShiftIdUsingGET: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ChatGroup"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findChatGroupByShiftIdAndWorkerIdUsingGET: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ChatGroup"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_6: {
    parameters: {
      body: {
        /** clientUpdateDto */
        clientUpdateDto: definitions["ClientDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ClientDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_9: {
    parameters: {
      body: {
        /** clientDto */
        clientDto: definitions["ClientDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Client"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getClientAdminUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["UserResponse"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllAgenciesUsingGET: {
    parameters: {
      path: {
        /** clientId */
        clientId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«IAgency»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getClientAgenciesUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ClientStats"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findNumberOfClientsUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: number;
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  linkAgentToClientUsingPUT: {
    parameters: {
      path: {
        /** payeeId */
        payeeId: number;
        /** payerId */
        payerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchClientUsingPOST: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ClientDto»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByClientIdUsingPOST: {
    parameters: {
      body: {
        /** shiftRequest */
        shiftRequest: definitions["ShiftRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllClientShiftsByStatusUsingGET: {
    parameters: {
      path: {
        /** clientId */
        clientId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getClientStatsUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["IShiftReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getClientWorkersUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** payerId */
        payerId: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchAgencyClientsUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** searchCriteria */
        searchCriteria: string;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ClientDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  addClientDocsUsingPOST: {
    parameters: {
      body: {
        /** clientDocsRequestDto */
        clientDocsRequestDto: definitions["Client"];
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadProfileImageUsingPOST_1: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
      query: {
        /** payerId */
        payerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_12: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ClientDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_6: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAllClientsUsingGET_1: {
    responses: {
      /** OK */
      200: {
        schema: definitions["ClientDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_13: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ClientDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET_2: {
    responses: {
      /** OK */
      200: {
        schema: definitions["Compliance"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_7: {
    parameters: {
      body: {
        /** complianceUpdateDto */
        complianceUpdateDto: definitions["ComplianceUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_10: {
    parameters: {
      body: {
        /** complianceCreateDto */
        complianceCreateDto: definitions["ComplianceCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Compliance"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_14: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Compliance"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_7: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAllPagedUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«Compliance»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  chargeUsingPOST: {
    parameters: {
      body: {
        /** request */
        request: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["CreatePaymentResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_11: {
    parameters: {
      query: {
        content?: { [key: string]: unknown };
        type?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  registerWorkerDeviceUsingPOST: {
    parameters: {
      body: {
        /** deviceWorkerUpdateDto */
        deviceWorkerUpdateDto: definitions["DeviceWorkerUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookWorkerDirectlyUsingPUT: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET_3: {
    responses: {
      /** OK */
      200: {
        schema: definitions["ExpenseRate"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_8: {
    parameters: {
      body: {
        /** expenseRateUpdateDto */
        expenseRateUpdateDto: definitions["ExpenseRateUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_12: {
    parameters: {
      body: {
        /** expenseRateDto */
        expenseRateDto: definitions["ExpenseRateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ExpenseRate"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_15: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ExpenseRate"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_8: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findExpenseRatesUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ExpenseRate»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  downloadFileUsingGET: {
    parameters: {
      path: {
        /** fileName */
        fileName: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Resource"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadFileUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["FileDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencyInvoicesByAdminUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencyInvoiceReportsByAdminUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  generateAgencyAdminBillsUsingPOST: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAdminInvoicesUsingGET: {
    parameters: {
      query: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteAdminInvoiceUsingDELETE: {
    parameters: {
      path: {
        /** invoiceId */
        invoiceId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  acknowledgeUsingPUT: {
    parameters: {
      query: {
        /** amount */
        amount: number;
        /** invoiceId */
        invoiceId: number;
        /** paymentRef */
        paymentRef: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createAgencynvoiceUsingPOST: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
      };
      body: {
        /** invoiceCreateDto */
        invoiceCreateDto: definitions["InvoiceCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  setDiscountUsingPUT: {
    parameters: {
      query: {
        /** discount */
        discount: number;
        /** invoiceId */
        invoiceId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  publishUsingPUT: {
    parameters: {
      query: {
        /** invoiceId */
        invoiceId: number;
        /** sendEmail */
        sendEmail: boolean;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencyInvoicesUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencyInvoiceReportsUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createClientInvoiceUsingPOST: {
    parameters: {
      body: {
        /** invoiceCreateDto */
        invoiceCreateDto: definitions["InvoiceCreateDto"];
      };
      query: {
        /** payeeId */
        payeeId: number;
        /** payerId */
        payerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getClientInvoicesUsingGET: {
    parameters: {
      query: {
        /** clientId */
        clientId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getClientInvoiceUsingGET: {
    parameters: {
      query: {
        /** invoiceId */
        invoiceId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["InvoiceResult"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  downloadInvoiceUsingGET: {
    parameters: {
      query: {
        /** format */
        format: "PDF" | "XLSX" | "DOCX" | "CSV";
        /** invoiceId */
        invoiceId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Resource"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getTrainerAgencyInvoicesUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** agencyIdFilter */
        agencyIdFilter?: number;
        /** endDate */
        endDate?: string;
        /** onlyPaid */
        onlyPaid?: boolean;
        /** startDate */
        startDate?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createAgencyTrainingsInvoiceUsingPOST: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** trainerId */
        trainerId: number;
      };
      body: {
        /** invoiceCreateDto */
        invoiceCreateDto: definitions["InvoiceCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getTrainerWorkerInvoicesUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** endDate */
        endDate?: string;
        /** onlyPaid */
        onlyPaid?: boolean;
        /** startDate */
        startDate?: string;
        /** trainingId */
        trainingId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getWorkerInvoicesUsingGET: {
    parameters: {
      query: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
      path: {
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_16: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["InvoiceResult"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_9: {
    parameters: {
      path: {
        /** invoiceId */
        invoiceId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  getPaidInvoicesDueForSettlementUsingGET: {
    parameters: {
      query: {
        /** bookingId */
        bookingId?: number;
        /** endDate */
        endDate?: string;
        /** page */
        page?: number;
        /** rentalId */
        rentalId?: number;
        /** size */
        size?: number;
        /** startDate */
        startDate?: string;
        /** status */
        status?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«InvoiceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  settleInvoicesUsingPOST: {
    parameters: {
      body: {
        /** carRentalId */
        carRentalId?: number;
      };
      query: {
        /** invoices */
        invoices: number[];
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_9: {
    parameters: {
      body: {
        /** shiftLocationUpdateDto */
        shiftLocationUpdateDto: definitions["ShiftLocationUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftLocationResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_13: {
    parameters: {
      body: {
        /** shiftLocationCreateDto */
        shiftLocationCreateDto: definitions["ShiftLocationCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftLocationResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_17: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftLocationResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_10: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAllLocationsUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftLocationResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchUsingGET: {
    parameters: {
      path: {
        /** query */
        query: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftLocationResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_18: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ShiftLocationResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getMemoryStatisticsUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["MemoryStats"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createNoteUsingPOST: {
    parameters: {
      body: {
        /** noteCreateDto */
        noteCreateDto: definitions["NoteCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Note"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteNoteUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findWorkerNotesUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Note"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_4: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  getWorkerNotificationsUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["NotificationResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getCertificateUsingGET: {
    parameters: {
      path: {
        /** enrolmentId */
        enrolmentId: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  registerToIHascoUsingPOST: {
    parameters: {
      path: {
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_11: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  getWorkerPayAdvicesUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«PayAdviceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  acknowledgeUsingPUT_1: {
    parameters: {
      query: {
        /** payAdviceId */
        payAdviceId: number;
        /** paymentRef */
        paymentRef: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getAgencyPayAdvicesUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** endDate */
        endDate?: string;
        /** page */
        page: number;
        /** size */
        size: number;
        /** startDate */
        startDate?: string;
        /** workerId */
        workerId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«PayAdviceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  downloadPayAdviceUsingGET: {
    parameters: {
      query: {
        /** format */
        format: "PDF" | "XLSX" | "DOCX" | "CSV";
        /** payAdviceId */
        payAdviceId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Resource"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createWorkerPayAdviceUsingPOST: {
    parameters: {
      body: {
        /** payAdviceCreateDto */
        payAdviceCreateDto: definitions["PayAdviceCreateDto"];
      };
      query: {
        /** payDate */
        payDate: string;
        /** payeeId */
        payeeId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  popUsingPUT: {
    parameters: {
      query: {
        /** payAdviceId */
        payAdviceId: number;
        /** paymentRef */
        paymentRef: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getWorkerPayAdvicesUsingGET_1: {
    parameters: {
      query: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«PayAdviceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getWorkerPayAdviceUsingGET: {
    parameters: {
      query: {
        /** payAdviceId */
        payAdviceId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PayAdviceResult"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  downloadBacsPaymentCsvUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** date */
        date: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchAllAgencyWorkersforPaslipUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** query */
        query: string;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllAgencyWorkersforPaslipUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadPayslipUsingPOST: {
    parameters: {
      query: {
        /** agencyId */
        agencyId: number;
        /** workerId */
        workerId: number;
      };
      formData: {
        /** file */
        file: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Payslip"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET_4: {
    parameters: {
      query: {
        /** agencyId */
        agencyId?: number;
        /** direction */
        direction?: string;
        /** page */
        page?: number;
        /** query */
        query?: string;
        /** size */
        size?: number;
        /** sort */
        sort?: string;
        /** statuses */
        statuses?:
          | "ACTIVE"
          | "INACTIVE"
          | "APPROVED"
          | "WAITING"
          | "CANCELLED"
          | "COMPLETED"
          | "REJECTED"
          | "NEW";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«PromotionDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_10: {
    parameters: {
      body: {
        /** promotionDto */
        promotionDto: definitions["PromotionDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PromotionDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_14: {
    parameters: {
      body: {
        /** promotionDto */
        promotionDto: definitions["PromotionDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PromotionDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  activateUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_19: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PromotionDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_12: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  queryUsingPUT: {
    parameters: {
      path: {
        /** reason */
        reason: string;
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByWorkerIdAndStatusUsingGET: {
    parameters: {
      path: {
        /** payslip */
        payslip: string;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  releaseShiftUsingPUT: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  removeWorkerUsingPUT: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  runSchedulerUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["MemoryStats"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAdminSentUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["Notification"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAgencySentUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Notification"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findClientSentUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Notification"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_11: {
    parameters: {
      body: {
        /** serviceUpdateDto */
        serviceUpdateDto: definitions["ServiceUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_15: {
    parameters: {
      body: {
        /** serviceCreateDto */
        serviceCreateDto: definitions["ServiceCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_20: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_13: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_22: {
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_21: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ServiceResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_12: {
    parameters: {
      query: {
        /** message */
        message?: string;
      };
      body: {
        /** shiftUpdateDto */
        shiftUpdateDto: definitions["ShiftUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_16: {
    parameters: {
      body: {
        /** shiftCreateDto */
        shiftCreateDto: definitions["ShiftCreateDto"][];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getApplicantsUsingGET: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_13: {
    parameters: {
      body: {
        /** shiftDirectorateUpdateDto */
        shiftDirectorateUpdateDto: definitions["ShiftDirectorateUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftDirectorateResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_17: {
    parameters: {
      body: {
        /** shiftDirectorateCreateDto */
        shiftDirectorateCreateDto: definitions["ShiftDirectorateCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftDirectorateResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findDirectorateByLocationByIdUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftDirectorateResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_26: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftDirectorateResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_15: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByAgencyIdUsingGET_1: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ShiftDirectorateResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByClientIdUsingGET_1: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** payerId */
        payerId: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ShiftDirectorateResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByAllWithFilterUsingGET: {
    parameters: {
      query: {
        /** location */
        location?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftDirectorateResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET_6: {
    parameters: {
      query: {
        /** locationId */
        locationId?: number;
        /** searchCriteria */
        searchCriteria?: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ShiftDirectorateResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchShiftUsingPOST: {
    parameters: {
      query: {
        /** endDate */
        endDate: string;
        /** location */
        location?: string;
        /** payeeId */
        payeeId?: number;
        /** payerId */
        payerId?: number;
        /** startDate */
        startDate: string;
        /** status */
        status: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_15: {
    parameters: {
      body: {
        /** shiftTypeUpdateDto */
        shiftTypeUpdateDto: definitions["ShiftType"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftType"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_19: {
    parameters: {
      body: {
        /** shiftTypeCreateDto */
        shiftTypeCreateDto: definitions["ShiftType"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftType"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  activateShiftTypeUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftType"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deactivateShiftTypeUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftType"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_27: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftType"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_16: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_29: {
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftType"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_28: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ShiftType»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_14: {
    parameters: {
      body: {
        /** expenseRateUpdateDto */
        expenseRateUpdateDto: definitions["ShiftExpenseClaimDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_18: {
    parameters: {
      body: {
        /** expenseRateDto */
        expenseRateDto: definitions["ShiftExpenseClaimDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ExpenseRate"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftExpenseClaimsUsingGET: {
    parameters: {
      path: {
        /** shiftIds */
        shiftIds: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftExpenseClaim"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  carPoolingWorkerListUsingGET: {
    parameters: {
      path: {
        /** shiftId */
        shiftId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftCarPoolingDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorisationReminderUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_23: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_14: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAllUsingGET_5: {
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByShiftStatusByClientIdUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** payerId */
        payerId: number;
        /** size */
        size: number;
        /** status */
        status: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllShiftsByStatusUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_24: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftComplianceIssuesUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«IShiftCompliance»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findJobCountUsingGET: {
    parameters: {
      path: {
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_25: {
    parameters: {
      query: {
        /** endDate */
        endDate: string;
        /** startDate */
        startDate: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByShiftStatusByClientUsingGET: {
    parameters: {
      query: {
        /** endDate */
        endDate: string;
        /** startDate */
        startDate: string;
      };
      path: {
        /** page */
        page: number;
        /** payerId */
        payerId: number;
        /** size */
        size: number;
        /** status */
        status: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByShiftStatusUsingGET: {
    parameters: {
      query: {
        /** endDate */
        endDate: string;
        /** startDate */
        startDate: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  handleStripeWebhookUsingPOST: {
    parameters: {
      header: {
        /** Stripe-Signature */
        "Stripe-Signature": string;
      };
      body: {
        /** payload */
        payload: string;
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_30: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TaxCode»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_16: {
    parameters: {
      body: {
        /** taxCodeUpdateDto */
        taxCodeUpdateDto: definitions["TaxCodeUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TaxCode"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_20: {
    parameters: {
      body: {
        /** taxCodeDto */
        taxCodeDto: definitions["TaxCodeDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_31: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TaxCode"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_17: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_32: {
    responses: {
      /** OK */
      200: {
        schema: definitions["TaxCode"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findTrainersUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET_7: {
    responses: {
      /** OK */
      200: {
        schema: definitions["Training"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_17: {
    parameters: {
      body: {
        /** trainingUpdateDto */
        trainingUpdateDto: definitions["TrainingUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_21: {
    parameters: {
      body: {
        /** trainingCreateDto */
        trainingCreateDto: definitions["TrainingCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Training"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  applyUsingPUT_2: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** trainingSessionId */
        trainingSessionId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  applicationApprovalUsingPUT: {
    parameters: {
      path: {
        /** approveBooking */
        approveBooking: boolean;
        /** bookingId */
        bookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizationReminderUsingPOST: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: string;
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizeCompleteUsingPUT: {
    parameters: {
      body: {
        /** workerTrainingSessionAuthorizeDto */
        workerTrainingSessionAuthorizeDto: definitions["WorkerTrainingSessionAuthorizeDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerTrainingSessionResultsDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookUsingPUT_2: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** trainingSessionId */
        trainingSessionId: number;
        /** workerIds */
        workerIds: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findPendingFeedbackUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: definitions["Optional«WorkerTrainingSession»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  showCertificateUsingPUT: {
    parameters: {
      path: {
        /** bookingId */
        bookingId: number;
        /** show */
        show: boolean;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByTrainingSessionUsingGET: {
    parameters: {
      path: {
        /** trainingSessionId */
        trainingSessionId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerTrainingSessionResultsDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_44: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerTrainingSessionResultsDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  cancelUsingDELETE_1: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findForAgencyUsingGET_1: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status:
          | "NEW"
          | "AUTHORIZED"
          | "WAITING_AUTHORIZATION"
          | "CLOSED"
          | "BOOKED"
          | "CANCELLED"
          | "REJECTED";
      };
      query: {
        /** endDate */
        endDate?: string;
        /** startDate */
        startDate?: string;
        /** trainingId */
        trainingId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerTrainingSessionResultsDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findForTrainerUsingGET_1: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status:
          | "NEW"
          | "AUTHORIZED"
          | "WAITING_AUTHORIZATION"
          | "CLOSED"
          | "BOOKED"
          | "CANCELLED"
          | "REJECTED";
      };
      query: {
        /** endDate */
        endDate?: string;
        /** startDate */
        startDate?: string;
        /** trainingId */
        trainingId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerTrainingSessionResultsDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByBookingUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingFeedbacksRes"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_22: {
    parameters: {
      path: {
        /** bookingId */
        bookingId: number;
      };
      body: {
        /** trainingFeedback */
        trainingFeedback: definitions["TrainingFeedback"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingFeedback"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_34: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingFeedback"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_19: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  updateUsingPUT_18: {
    parameters: {
      body: {
        /** trainingSessionUpdateDto */
        trainingSessionUpdateDto: definitions["TrainingSessionUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_23: {
    parameters: {
      body: {
        /** trainingSessionCreateDto */
        trainingSessionCreateDto: definitions["TrainingSessionCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingSessionResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAgencySessionCountUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingSessionReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findTrainerSessionCountUsingGET: {
    parameters: {
      path: {
        /** trainerId */
        trainerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingSessionReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_35: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingSessionResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  cancelUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findForAgencyUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: "NEW" | "CLOSED" | "CANCELLED";
      };
      query: {
        /** endDate */
        endDate?: string;
        /** reports */
        reports?: boolean;
        /** startDate */
        startDate?: string;
        /** trainingId */
        trainingId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TrainingSessionResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findForTrainerBillingUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** trainerId */
        trainerId: number;
      };
      query: {
        /** endDate */
        endDate?: string;
        /** startDate */
        startDate?: string;
        /** trainingId */
        trainingId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TrainingSessionResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findForTrainerUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: "NEW" | "CLOSED" | "CANCELLED";
      };
      query: {
        /** endDate */
        endDate?: string;
        /** startDate */
        startDate?: string;
        /** trainingId */
        trainingId?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TrainingSessionResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  availableTrainingsForWorkerUsingGET: {
    parameters: {
      query: {
        /** trainingId */
        trainingId?: number;
      };
      path: {
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AvailableTraininingsResultsDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findForWorkerUsingGET: {
    parameters: {
      path: {
        /** trainingId */
        trainingId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TrainingSessionResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllPagedUsingGET_2: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: "NEW" | "CLOSED" | "CANCELLED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TrainingSessionResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  addTrainingUsingPUT: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** trainingId */
        trainingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ServiceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_33: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Training"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_18: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAllPagedUsingGET_1: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«Training»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAlwlPagedUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Training"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_19: {
    parameters: {
      body: {
        /** transportDto */
        transportDto: definitions["TransportDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_24: {
    parameters: {
      body: {
        /** transportDto */
        transportDto: definitions["TransportDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  approveBookingUsingPUT: {
    parameters: {
      path: {
        /** workerTransportBookingId */
        workerTransportBookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportApplicantsResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizationReminderUsingPUT: {
    parameters: {
      query: {
        /** bool */
        bool?: boolean;
      };
      path: {
        /** transportBookingId */
        transportBookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: string;
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizeTransportBookingUsingPUT: {
    parameters: {
      path: {
        /** transportBookingId */
        transportBookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: string;
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookWorkersDirectlyUsingPOST: {
    parameters: {
      path: {
        /** specId */
        specId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  cancelTransportBookingUsingDELETE: {
    parameters: {
      query: {
        /** reason */
        reason: string;
      };
      path: {
        /** transportBookingId */
        transportBookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportApplicantsResultDto"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  rejectBookingUsingPUT: {
    parameters: {
      path: {
        /** transportBookingId */
        transportBookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  agencyTransportBookingsUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** transportId */
        transportId?: number;
        /** transportStatus */
        transportStatus?:
          | "NEW"
          | "WAITING"
          | "CLOSED"
          | "BOOKED"
          | "AUTHORIZED"
          | "PENDING"
          | "CANCELLED"
          | "EXPIRED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TransportApplicantsResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  applyUsingPUT_1: {
    parameters: {
      path: {
        /** transportId */
        transportId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  acceptUsingPUT: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllAgencyShiftsByStatusUsingGET_2: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByAgencyIdUsingGET_2: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status:
          | "NEW"
          | "WAITING"
          | "CLOSED"
          | "BOOKED"
          | "AUTHORIZED"
          | "PENDING"
          | "CANCELLED"
          | "EXPIRED";
      };
      query: {
        /** fullyBookedOrNOt */
        fullyBookedOrNOt?: boolean;
        /** searchCriteria */
        searchCriteria: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TransportDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizeTransportUsingPUT: {
    parameters: {
      body: {
        /** authorizeTransportDto */
        authorizeTransportDto: definitions["AuthorizeTransportDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateCleaningCheckUsingPUT: {
    parameters: {
      body: {
        /** transportDto */
        transportDto: definitions["VehicleLogDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleLogDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllClientShiftsByStatusUsingGET_1: {
    parameters: {
      path: {
        /** clientId */
        clientId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByClientIdUsingGET_2: {
    parameters: {
      path: {
        /** clientId */
        clientId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status:
          | "NEW"
          | "WAITING"
          | "CLOSED"
          | "BOOKED"
          | "AUTHORIZED"
          | "PENDING"
          | "CANCELLED"
          | "EXPIRED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TransportDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  commitJobUsingPUT: {
    parameters: {
      path: {
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getTransportLegibleWorkersUsingGET: {
    parameters: {
      path: {
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["LegibleWorkersDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllAgencyLogsShiftsByStatusUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ShiftReportStatus"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateMileageUsingPUT: {
    parameters: {
      body: {
        /** transportDto */
        transportDto: definitions["VehicleLogDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleLogDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  pickDriverUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  pickTeamLeaderUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  rateProviderUsingPUT: {
    parameters: {
      path: {
        /** rating */
        rating: number;
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  teamLeaderUpdateUsingPUT: {
    parameters: {
      body: {
        /** transportDto */
        transportDto: definitions["TransportTeamLeaderUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadPatientDocumentOneUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
      path: {
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadPatientDocumentThreeUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
      path: {
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadPatientDocumentTwoUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
      path: {
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadRiskDocumentUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
      path: {
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateVehicleCheckUsingPUT: {
    parameters: {
      body: {
        /** transportDto */
        transportDto: definitions["VehicleLogDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleLogDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  transportVehicleUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
        /** transportId */
        transportId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  teamLeaderUpdateUsingPUT_1: {
    parameters: {
      body: {
        /** transportDto */
        transportDto: definitions["TransportWorkerTimesDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByDriverUsingGET: {
    parameters: {
      path: {
        /** driverId */
        driverId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status:
          | "NEW"
          | "WAITING"
          | "CLOSED"
          | "BOOKED"
          | "AUTHORIZED"
          | "PENDING"
          | "CANCELLED"
          | "EXPIRED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TransportDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByTeamLeaderUsingGET: {
    parameters: {
      path: {
        /** driverId */
        driverId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status:
          | "NEW"
          | "WAITING"
          | "CLOSED"
          | "BOOKED"
          | "AUTHORIZED"
          | "PENDING"
          | "CANCELLED"
          | "EXPIRED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TransportDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_36: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["TransportDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  cancelJobUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAllUsingGET_8: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status:
          | "NEW"
          | "WAITING"
          | "CLOSED"
          | "BOOKED"
          | "AUTHORIZED"
          | "PENDING"
          | "CANCELLED"
          | "EXPIRED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«TransportDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  downloadBacsPaymentCsvUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["GeneralResponse"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllUsingGET_9: {
    responses: {
      /** OK */
      200: {
        schema: definitions["VatRate"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_20: {
    parameters: {
      body: {
        /** vatRateDto */
        vatRateDto: definitions["VatRateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_25: {
    parameters: {
      body: {
        /** vatRateDto */
        vatRateDto: definitions["VatRateCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_37: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VatRate"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_20: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_38: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VatRate»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_22: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_27: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_21: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleBookingDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_26: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleBookingDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createAsAgencyUsingPOST: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleBookingDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  cancelBookingUsingPOST: {
    parameters: {
      path: {
        /** bookingId */
        bookingId: number;
      };
      query: {
        /** byAgency */
        byAgency: boolean;
        /** reason */
        reason?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  rateClientForBookingUsingPOST: {
    parameters: {
      body: {
        /** rating */
        rating: definitions["Rating"];
      };
      path: {
        /** vehicleBookingId */
        vehicleBookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Rating"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findForClientUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId?: number;
        /** searchCriteria */
        searchCriteria?: string;
        /** statuses */
        statuses?:
          | "RESERVED"
          | "BOOKED"
          | "AUTHORIZED"
          | "WAITINGAUTH"
          | "COMPLETE"
          | "CANCELLED";
      };
      path: {
        /** clientId */
        clientId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleBookingDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createDepositUsingPOST: {
    parameters: {
      body: {
        /** depositDto */
        depositDto: definitions["VehicleBookingDepositDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDepositDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdAndEmailUsingGET: {
    parameters: {
      path: {
        /** email */
        email: string;
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  handOverVehicleUsingPUT: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleBookingDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookingPaidPaynowUsingGET: {
    parameters: {
      path: {
        /** quoteId */
        quoteId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookingPaidPaynowPutUsingPUT: {
    parameters: {
      path: {
        /** quoteId */
        quoteId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bookingPaidPaynowPostUsingPOST: {
    parameters: {
      path: {
        /** quoteId */
        quoteId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getQuoteUsingPOST: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleBookingDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  rateVehicleBookingUsingPOST: {
    parameters: {
      body: {
        /** rating */
        rating: definitions["Rating"];
      };
      path: {
        /** vehicleBookingId */
        vehicleBookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Rating"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  resendBookingEmailUsingPOST: {
    parameters: {
      path: {
        /** bookingId */
        bookingId: number;
        /** emailType */
        emailType:
          | "BOOKING_PAID"
          | "BOOKING_RESERVED"
          | "AGENCY_BOOKING_RECEIVED"
          | "BOOKING_PAYMENT_FAILED"
          | "BOOKING_CANCELLED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  returnVehicleUsingPUT: {
    parameters: {
      body: {
        /** vehicle */
        vehicle: definitions["VehicleBookingDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findVehicleLogsUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: "NEW" | "WAITING_APPROVAL" | "APPROVED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleLogDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getDepositsByBookingIdUsingGET: {
    parameters: {
      path: {
        /** bookingId */
        bookingId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDepositDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_39: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleBookingDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByAgencyIdUsingGET_3: {
    parameters: {
      query: {
        /** agencyId */
        agencyId?: number;
        /** bookingId */
        bookingId?: number;
        /** end */
        end?: string;
        /** searchCriteria */
        searchCriteria?: string;
        /** sortBy */
        sortBy?: string;
        /** sortOrder */
        sortOrder?: string;
        /** start */
        start?: string;
        /** statuses */
        statuses?:
          | "RESERVED"
          | "BOOKED"
          | "AUTHORIZED"
          | "WAITINGAUTH"
          | "COMPLETE"
          | "CANCELLED";
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleBookingDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  authorizeVehicleLogUsingPUT: {
    parameters: {
      path: {
        /** authorizer */
        authorizer: string;
        /** vehicleLogId */
        vehicleLogId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleLogDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  commentVehicleLogUsingPUT: {
    parameters: {
      path: {
        /** comment */
        comment: string;
        /** vehicleLogId */
        vehicleLogId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleLogDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadRiskDamageReportUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
      path: {
        /** logId */
        logId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByAgencyAndStatusUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: "NEW" | "WAITING_APPROVAL" | "APPROVED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleLogDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByWorkerAndStatusUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: "NEW" | "WAITING_APPROVAL" | "APPROVED";
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleLogDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  addDocumentUsingPOST: {
    parameters: {
      formData: {
        /** docName */
        docName: unknown;
        /** documentId */
        documentId?: unknown;
        /** expiryDate */
        expiryDate: unknown;
        /** expiryMileage */
        expiryMileage?: unknown;
        /** file */
        file?: unknown;
        /** lastDate */
        lastDate?: unknown;
        /** lastMileage */
        lastMileage?: unknown;
        /** vehicleId */
        vehicleId: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteDocumentsUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  addInventoryUsingPOST: {
    parameters: {
      body: {
        /** vehicleInventory */
        vehicleInventory: definitions["VehicleInventory"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteInventoryUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  deletePhotoUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  addPhotoUsingPOST: {
    parameters: {
      body: {
        /** file */
        file: definitions["VehiclePhotoDto"];
      };
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  setMainPhotoUsingPUT: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByAdminIdUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId?: number;
        /** direction */
        direction?: string;
        /** searchQuery */
        searchQuery?: string;
        /** sort */
        sort?: string;
        /** status */
        status?: "AWAITING" | "EDITED" | "DISABLED" | "AVAILABLE" | "REJECTED";
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAgencyAvailableVehiclesUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** end */
        end: string;
        /** hasPromotion */
        hasPromotion?: boolean;
        /** location */
        location?: number;
        /** promotionType */
        promotionType?:
          | "DISCOUNT"
          | "EXTRA_MILEAGE"
          | "EXTRA_DAYS"
          | "OTHER_AWARD";
        /** searchCriteria */
        searchCriteria?: string;
        /** start */
        start: string;
        /** vehicleType */
        vehicleType?:
          | "SUV"
          | "PICKUP"
          | "DEFAULT"
          | "SEDAN"
          | "HATCHBACK"
          | "COUPE"
          | "SPECIAL"
          | "CONVERTIBLE"
          | "MPV"
          | "MINIVAN"
          | "STATION_WAGON"
          | "CROSSOVER"
          | "SPORTS_CAR";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByAgencyIdUsingGET_4: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** direction */
        direction?: string;
        /** searchQuery */
        searchQuery?: string;
        /** sort */
        sort?: string;
        /** status */
        status?: "AWAITING" | "EDITED" | "DISABLED" | "AVAILABLE" | "REJECTED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  approveVehicleUsingPUT: {
    parameters: {
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  disableVehicleUsingPUT: {
    parameters: {
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  enableVehicleUsingPUT: {
    parameters: {
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findPublicVehiclesUsingGET: {
    parameters: {
      query: {
        /** agencyId */
        agencyId?: number;
        /** end */
        end: string;
        /** hasPromotion */
        hasPromotion?: boolean;
        /** location */
        location: number;
        /** lowMileageLimit */
        lowMileageLimit?: number;
        /** maxDeposit */
        maxDeposit?: number;
        /** maxPrice */
        maxPrice?: number;
        /** minPrice */
        minPrice?: number;
        /** promotionType */
        promotionType?:
          | "DISCOUNT"
          | "EXTRA_MILEAGE"
          | "EXTRA_DAYS"
          | "OTHER_AWARD";
        /** searchCriteria */
        searchCriteria?: string;
        /** start */
        start: string;
        /** transmission */
        transmission?: "AUTO" | "MANUAL";
        /** vehicleType */
        vehicleType?:
          | "SUV"
          | "PICKUP"
          | "DEFAULT"
          | "SEDAN"
          | "HATCHBACK"
          | "COUPE"
          | "SPECIAL"
          | "CONVERTIBLE"
          | "MPV"
          | "MINIVAN"
          | "STATION_WAGON"
          | "CROSSOVER"
          | "SPORTS_CAR";
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateRatesUsingPUT: {
    parameters: {
      body: {
        /** asset */
        asset: definitions["VehicleDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  rejectVehicleUsingPUT: {
    parameters: {
      body: {
        /** comment */
        comment: definitions["CommentDto"];
      };
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getFilteredVehiclesUsingPOST: {
    parameters: {
      body: {
        /** filter */
        filter: definitions["VehicleFilterDto"];
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleDto»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_40: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findVehicleLogsUsingGET_1: {
    parameters: {
      path: {
        /** id */
        id: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: "NEW" | "WAITING_APPROVAL" | "APPROVED";
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«VehicleLogDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getVehicleAvailabilityUsingGET: {
    parameters: {
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleAvailabilityDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateVehicleAvailabilityUsingPOST: {
    parameters: {
      query: {
        /** isAvailable */
        isAvailable: boolean;
      };
      body: {
        /** updateDto */
        updateDto: definitions["VehicleAvailabilityUpdateDto"];
      };
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  clearAllVehicleAvailabilityUsingDELETE: {
    parameters: {
      path: {
        /** vehicleId */
        vehicleId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["VehicleAvailabilityDto"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  updateUsingPUT_23: {
    parameters: {
      body: {
        /** workerUpdateDto */
        workerUpdateDto: definitions["WorkerUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_28: {
    parameters: {
      body: {
        /** workerCreateDto */
        workerCreateDto: definitions["WorkerCreateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: unknown };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllAgenciesUsingGET_1: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AgencyResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByWorkerIdAndStatusUsingGET_1: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** status */
        status: string;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByWorkerIdforBillingUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findShiftsByClientIdUsingGET_1: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«BookingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getMyAgenciesUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«AgencyResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAppliedShiftsForWorkerIdUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["BookingResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadGeneralSignatureUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
        /** workerId */
        workerId: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllClientsUsingGET_2: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«ClientDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  responseWorkerComplianceUsingPUT: {
    parameters: {
      body: {
        /** workerComplianceUpdateDto */
        workerComplianceUpdateDto: definitions["WorkerComplianceUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Availability"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createWorkerComplianceUsingPOST: {
    parameters: {
      formData: {
        agencyId?: unknown;
        complianceDate?: unknown;
        complianceExpiry?: unknown;
        complianceId?: unknown;
        description?: unknown;
        file?: unknown;
        workerId?: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerComplianceResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteWorkerComplianceUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAgencyWorkerCompliancesUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerComplianceResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findWorkerCompliancesUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«IWorkerComplianceResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findNumberOfWorkersUsingGET: {
    responses: {
      /** OK */
      200: {
        schema: number;
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  linkAgencyToWorkerUsingPUT: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getWorkerPayAdvicesUsingGET_2: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«PayAdviceResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getWorkerPayslipsUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«PayslipResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchWorkerUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchClientWorkerUsingGET: {
    parameters: {
      path: {
        /** clientId */
        clientId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  searchAllWorkersUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
      };
      query: {
        /** searchCriteria */
        searchCriteria?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAllWorkerShiftsByStatusUsingGET: {
    parameters: {
      path: {
        /** workerID */
        workerID: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["IShiftReportStatus"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getWorkerStatsUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerStats"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  responseWorkerTrainingUsingPUT: {
    parameters: {
      body: {
        /** workerTrainingUpdateDto */
        workerTrainingUpdateDto: definitions["WorkerTrainingUpdateDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Availability"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createWorkerTrainingUsingPOST: {
    parameters: {
      formData: {
        agencyId?: unknown;
        description?: unknown;
        file?: unknown;
        trainingDate?: unknown;
        trainingExpiry?: unknown;
        trainingId?: unknown;
        workerId?: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerTrainingResultDto"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findWorkerTrainingUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerTrainingResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteWorkerTrainingUsingDELETE: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findAgencyWorkerTrainingsUsingGET: {
    parameters: {
      path: {
        /** agencyId */
        agencyId: number;
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerTrainingResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findWorkerTrainingsUsingGET: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerTrainingResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadProfileImageUsingPOST_2: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
        /** workerId */
        workerId: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadComplianceDocUsingPOST: {
    parameters: {
      query: {
        /** compliance */
        compliance: number;
        /** workerId */
        workerId: number;
      };
      formData: {
        /** file */
        file: unknown;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  uploadTrainingDocUsingPOST: {
    parameters: {
      formData: {
        /** file */
        file: unknown;
      };
      query: {
        /** training */
        training: number;
        /** workerId */
        workerId: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_41: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerResultDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_21: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  findByIdUsingGET_43: {
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerResultDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findClientWorkersUsingGET: {
    parameters: {
      query: {
        /** assignmentCodeId */
        assignmentCodeId: number;
        /** clientId */
        clientId: number;
        /** gender */
        gender: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findAgencyWorkersUsingPOST: {
    parameters: {
      body: {
        /** agencyList */
        agencyList: definitions["AgencyList"];
      };
      path: {
        /** assignmentCodeName */
        assignmentCodeName: number;
        /** gender */
        gender: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["WorkerResultDto"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET_42: {
    parameters: {
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["Page«WorkerResultDto»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createDebitNoteUsingPOST: {
    parameters: {
      body: {
        /** shiftBillDto */
        shiftBillDto: definitions["ShiftBillDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  SetDebitNotePaidStatusUsingPUT: {
    parameters: {
      body: {
        /** agencyBillStatusDto */
        agencyBillStatusDto: definitions["AgencyBillStatusDto"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listAllBillsByStatusUsingPOST: {
    parameters: {
      query: {
        /** endDate */
        endDate?: string;
        /** payeeId */
        payeeId?: number;
        /** startDate */
        startDate?: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyBillDto"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  findByIdUsingGET: {
    parameters: {
      path: {
        /** id */
        id: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyBillDto"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listAllBillsUsingGET: {
    parameters: {
      query: {
        /** endDate */
        endDate?: string;
        /** payeeId */
        payeeId?: number;
        /** startDate */
        startDate?: string;
      };
      path: {
        /** page */
        page: number;
        /** size */
        size: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["AgencyBillDto"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
}

export interface external {}
