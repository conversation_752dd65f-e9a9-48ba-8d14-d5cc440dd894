import { useMutation } from "@tanstack/react-query";
import { vehicleBookingService } from "@/common/services/vehicle-booking.service";

export function useCancelBooking() {
    return useMutation({
        mutationFn: ({
            id,
            byAgency,
            reason,
        }: {
            id: number;
            byAgency: boolean;
            reason: string | null | undefined;
        }) => vehicleBookingService.cancelBooking(id, byAgency, reason),
    });
}
