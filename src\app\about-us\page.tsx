import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import { Card, CardContent } from "@/common/components/ui/card";
import { Building2, Clock, Shield, Car } from "lucide-react";
import aboutUsImg from "@/common/assets/images/about-us.jpg";
import { TextLogo } from "@/common/components/text-logo";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "About MyKarLink - Leading Car Rental Platform in Zimbabwe",
  description:
    "Learn about MyKarLink's mission to transform car rental in Zimbabwe. We connect car owners with renters, offering affordable, reliable vehicle sharing services with 24/7 support and secure booking.",
  keywords: [
    "about MyKarLink",
    "car rental company Zimbabwe",
    "vehicle sharing platform",
    "car rental Zimbabwe history",
    "MyKarLink story",
    "peer-to-peer car sharing",
    "car rental platform Zimbabwe",
    "vehicle rental company",
    "Zimbabwe transport solutions",
    "car sharing mission",
    "trusted car rental",
    "affordable car hire Zimbabwe",
  ],

  alternates: {
    canonical: "https://mykarlink.com/about-us",
  },

  openGraph: {
    title: "About MyKarLink - Leading Car Rental Platform in Zimbabwe",
    description:
      "Discover MyKarLink's mission to transform car rental in Zimbabwe through peer-to-peer vehicle sharing. Connecting car owners with renters since 2024.",
    url: "https://mykarlink.com/about-us",
    type: "website",
    images: [
      {
        url: "https://mykarlink.com/brands/karlink/images/og-image.png",
        width: 1200,
        height: 630,
        alt: "MyKarLink About Us - Car Rental Platform Team and Mission",
      },
    ],
  },

  twitter: {
    card: "summary_large_image",
    title: "About MyKarLink - Leading Car Rental Platform in Zimbabwe",
    description:
      "Learn about MyKarLink's mission to transform car rental through peer-to-peer vehicle sharing.",
    images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
  },
};

export default function AboutPage() {
  // Structured data for About Us page
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "MyKarLink",
    legalName: "MyKarLink PVT LTD",
    url: "https://mykarlink.com",
    logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
    description:
      "Leading car rental and vehicle sharing platform in Zimbabwe, connecting car owners with renters through a trusted, affordable, and convenient peer-to-peer marketplace.",
    foundingDate: "2024",
    founder: {
      "@type": "Organization",
      name: "MyKarLink Founders",
    },
    numberOfEmployees: {
      "@type": "QuantitativeValue",
      value: "10-50",
    },
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+263779144386",
      contactType: "customer service",
      email: "<EMAIL>",
      availableLanguage: ["English"],
      areaServed: "ZW",
      hoursAvailable: {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        opens: "09:00",
        closes: "17:00",
      },
    },
    address: {
      "@type": "PostalAddress",
      streetAddress: "7 St Antony, Cnr Five Av and Fifth St",
      addressLocality: "Harare",
      addressCountry: "ZW",
    },
    sameAs: [
      "https://www.facebook.com/people/My-Karlink/61572568015730/",
      "https://www.instagram.com/my_karlink/",
      "https://x.com/My_KarLink",
      "https://www.youtube.com/@mykarlink",
      "https://www.tiktok.com/@mykarlink",
    ],
    serviceType: "Car Rental and Vehicle Sharing",
    areaServed: {
      "@type": "Country",
      name: "Zimbabwe",
    },
    knowsAbout: [
      "Car Rental",
      "Vehicle Sharing",
      "Peer-to-peer Marketplace",
      "Transportation Services",
      "Zimbabwe Tourism",
    ],
    slogan: "Linking the global village through simplified car rentals",
  };

  const aboutPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "AboutPage",
    name: "About MyKarLink",
    description:
      "Learn about MyKarLink's mission, values, and commitment to transforming car rental in Zimbabwe",
    url: "https://mykarlink.com/about-us",
    mainEntity: {
      "@type": "Organization",
      name: "MyKarLink",
      description: "Car rental and vehicle sharing platform in Zimbabwe",
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: "https://mykarlink.com",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "About Us",
          item: "https://mykarlink.com/about-us",
        },
      ],
    },
  };

  const statsJsonLd = {
    "@context": "https://schema.org",
    "@type": "Dataset",
    name: "MyKarLink Statistics",
    description: "Key performance statistics for MyKarLink platform",
    variableMeasured: [
      {
        "@type": "PropertyValue",
        name: "Happy Customers",
        value: "10000+",
        description: "Number of satisfied customers served",
      },
      {
        "@type": "PropertyValue",
        name: "Available Vehicles",
        value: "500+",
        description: "Number of vehicles available for rental",
      },
      {
        "@type": "PropertyValue",
        name: "Service Locations",
        value: "15+",
        description: "Number of locations where services are available",
      },
      {
        "@type": "PropertyValue",
        name: "Customer Satisfaction Rate",
        value: "98%",
        description: "Percentage of satisfied customers",
      },
    ],
  };
  return (
    <main className="min-h-screen">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(aboutPageJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(statsJsonLd).replace(/</g, "\\u003c"),
        }}
      />

      {/* Hero Section */}
      <section className="bg-primary relative pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <TextLogo
              className="text-3xl font-bold tracking-tight text-white sm:text-5xl"
              inverted
            />
            <p className="mt-6 text-lg leading-8 text-gray-300">
              Linking the global village through simplified car rentals
            </p>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="bg-white py-24">
        <div className="container mx-auto px-4">
          <div className="grid items-center gap-12 lg:grid-cols-2 lg:gap-16">
            <div className="relative h-[400px] overflow-hidden rounded-xl">
              <Image
                src={aboutUsImg}
                fill
                alt="MyKarLink office and fleet"
                className="object-cover"
                priority
              />
            </div>
            <div className="space-y-6">
              <h2 className="text-primary text-3xl font-bold tracking-tight sm:text-4xl">
                About Us
              </h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  MyKarLink was founded with a simple mission:{" "}
                  <b>
                    To link the global village and transform the car rental
                    experience.
                  </b>{" "}
                  We recognized the challenges faced by both locals and visitors
                  in finding reliable, affordable and efficient car rental
                  services.
                </p>
                <p>
                  MyKarLink literally &apos;Links&apos; car owners/businesses
                  with hirers from anywhere in the simplest and most flexible
                  and affordable way.
                </p>
                <p>
                  Via MyKarLink, you can hire just about any vehicle anywhere by
                  linking with car owners from the MyKarLink community.
                </p>
                <p>
                  MyKarLink offers a win-win for everyone, where car owners can
                  make extra income from their personal/business vehicles in a
                  hassle free way while hirers get the convenience,
                  affordability and wide choice of vehicles to rent from.
                </p>
                <p>
                  We are growing to become the biggest and most trusted car
                  rental platform, serving thousands of satisfied customers
                  across multiple cities and unlocking potential for individual
                  car owners to make extra income.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="bg-gray-50 py-24">
        <div className="container mx-auto px-4">
          <div className="mb-16 text-center">
            <h2 className="text-primary text-3xl font-bold tracking-tight sm:text-4xl">
              Why Book Via <TextLogo />
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Experience the difference of a modern, customer-focused car rental
              service
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                icon: Car,
                title: "Just About Any Car",
                description:
                  "Modern and specialized vehicles for every need and budget. No credit cards required.",
              },
              {
                icon: Shield,
                title: "Secure Booking",
                description:
                  "Safe, transparent booking process with no hidden fees",
              },
              {
                icon: Clock,
                title: "24/7 Support",
                description:
                  "Round-the-clock customer service for peace of mind",
              },
              {
                icon: Building2,
                title: "Multiple Locations",
                description:
                  "Convenient pickup and drop-off locations – finding cars nearest to you or your destination has never been simpler",
              },
            ].map((feature, index) => (
              <Card key={index}>
                <CardContent>
                  <div className="bg-secondary/10 mb-4 inline-block rounded-lg">
                    <feature.icon className="text-secondary size-9 p-1" />
                  </div>
                  <h3 className="text-primary mb-2 text-lg font-semibold">
                    {feature.title}
                  </h3>
                  <p className="text-card-foreground/70">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-white py-24">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            {[
              { number: "10k+", label: "Happy Customers" },
              { number: "500+", label: "Vehicles" },
              { number: "15+", label: "Locations" },
              { number: "98%", label: "Satisfaction Rate" },
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-orange-500">
                  {stat.number}
                </div>
                <div className="mt-2 text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary/95 py-24">
        <div className="container mx-auto px-4 text-center">
          <h2 className="mb-6 text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Ready to Experience Better Car Rental?
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-300">
            Join thousands of satisfied customers who trust MyKarLink for their
            transportation needs.
          </p>
          <div className="flex justify-center gap-4">
            <Button asChild variant="secondary" size="lg">
              <Link href="/">Browse Cars</Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="border-white bg-transparent text-white hover:bg-white/10 hover:text-white"
              size="lg"
            >
              <Link href="/contact-us">Contact Us</Link>
            </Button>
          </div>
        </div>
      </section>
    </main>
  );
}
