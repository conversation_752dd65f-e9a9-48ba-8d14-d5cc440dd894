import { VehicleBooking } from "@/common/models";
import { Nullish } from "@/common/types/utils";

const HIRER_PERCENTAGE = 0.03;
const PROVIDER_PERCENTAGE = 0.04;
const TOTAL_PERCENTAGE = HIRER_PERCENTAGE + PROVIDER_PERCENTAGE;

export type BookingItem = {
    description: string;
    total: number;
    units: string
}

export type BookingTotals = {
    subTotal: Nullish<number>;
    total: Nullish<number>;
    discount: Nullish<number>;
    amountDue: Nullish<number>;
    payableOnline: Nullish<number>;
    payableAtCollection: Nullish<number>;
    reservationFee: Nullish<number>
}

export function isHirerBooking(booking: Nullish<VehicleBooking>): boolean {
    // Check if byAgency is false OR if there's a service charge
    return !booking?.byAgency || !!booking.invoices[0]?.serviceCharge;
}

export function getBookingItems(booking: Nullish<VehicleBooking>) {
    const invoice = booking?.invoices?.[0]
    const vehicle = booking?.vehicle
    const daysHired = getBookingDaysHired(booking)

    const vehicleBookingItems = invoice?.invoiceItemResult.filter((item) => item.description.includes("Car rental for"))
    const addonItems = invoice?.invoiceItemResult.filter((item) => item.rate !== null && !item.description.includes("Car rental for"))

    const bookingItems: BookingItem[] = []

    bookingItems.push({
        description: `${vehicle?.color} ${vehicle?.name} ${vehicle?.model}`,
        total: vehicleBookingItems?.reduce((sum, item) => sum + item.total, 0) ?? 0,
        units: `x${daysHired} ${daysHired === 1 ? 'day' : 'days'}`
    })

    addonItems?.forEach((item) => {
        bookingItems.push({
            description: item.description,
            total: item.total,
            units: `x${daysHired} ${daysHired === 1 ? 'day' : 'days'}`
        })
    })

    return bookingItems
}

export function getBookingDaysHired(booking: Nullish<VehicleBooking>) {
    return booking?.invoices[0]?.invoiceItemResult.filter((item) => item.description.includes("Car rental for")).length ?? 0
}

export function getBookingTotals(booking: Nullish<VehicleBooking>) {
    const invoice = booking?.invoices?.[0]

    const amountDue = (invoice?.totalAmount ?? 0) - (invoice?.payments?.reduce((sum, payment) => sum + payment.total, 0) ?? 0);
    const totals: BookingTotals = {
        subTotal: invoice?.subTotalAmount,
        total: (invoice?.subTotalAmount ?? 0) - (invoice?.discount ?? 0),
        discount: invoice?.discount,
        amountDue,
        payableOnline: 0,
        payableAtCollection: 0,
        reservationFee: 0
    }

    if (isHirerBooking(booking)) {
        totals.reservationFee = (totals?.subTotal ?? 0) * HIRER_PERCENTAGE;
    }

    totals.total = (totals?.subTotal ?? 0) - (totals?.discount ?? 0) + (totals?.reservationFee ?? 0);
    totals.amountDue = totals.total - (invoice?.payments?.reduce((sum, payment) => sum + payment.total, 0) ?? 0) - (totals?.reservationFee ?? 0);
    totals.payableOnline = (totals.subTotal ?? 0) * TOTAL_PERCENTAGE
    totals.payableAtCollection = totals.total! - ((totals.subTotal ?? 0) * PROVIDER_PERCENTAGE) - (totals?.reservationFee ?? 0)

    return totals
}

export function getAddonIds(booking: Nullish<VehicleBooking>) {
    const addonItems = booking?.invoices?.[0]?.invoiceItemResult.filter((item) => item.rate !== null && !item.description.includes("Car rental for"))
    const vehicleAddons = booking?.vehicle.inventory.filter((inv) => inv.price != null)

    const addOnIds = addonItems?.map((item) => {
        const addon = vehicleAddons?.find((addon) => addon.name.toLowerCase().trim() === item.description.toLowerCase().trim())
        return addon?.id
    }).filter((id) => id !== undefined) ?? []

    return addOnIds
}

export function getPaymentMethod(paymentRef: string | null): string {
    if (!paymentRef) return "-";
    if (paymentRef.toLowerCase().includes("stripe")) return "Card";
    if (paymentRef.toLowerCase().includes("paynow")) return "Local Payment Options";
    return "-";
}

export function getBookingInvoicePayments(booking: VehicleBooking) {
    const invoice = booking?.invoices[0]
    const totals = getBookingTotals(booking)

    const payments: { id: number, date: string, paymentMethod: string, total: number }[] = []

    invoice?.payments.map((payment) => {
        payments.push({
            id: payment.id,
            date: payment.paymentDate,
            paymentMethod: getPaymentMethod(payment.ref),
            total: payment.total
        })
    })


    if (isHirerBooking(booking)) {
        const providerChargePayment = invoice.payments.find(payment =>
            Math.floor(payment.total) === Math.floor(PROVIDER_PERCENTAGE * totals.subTotal!))

        if (providerChargePayment) {

            const lowestIdPayment = payments.reduce((prev, curr) => prev.id < curr.id ? prev : curr, payments[0]);
            if (lowestIdPayment) {
                lowestIdPayment.total = lowestIdPayment.total + (HIRER_PERCENTAGE * totals.subTotal!);
            }
            // payments.push({
            //     date: providerChargePayment.paymentDate,
            //     paymentMethod: getPaymentMethod(providerChargePayment.ref),
            //     total: (HIRER_PERCENTAGE * totals.subTotal!)
            // })
        }
    }



    return payments
}
