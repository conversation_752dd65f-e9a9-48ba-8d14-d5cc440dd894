import { definitions } from "@/common/types/api/schema";

// Main Agency Types
export type Agency = definitions["Agency"];
export type AgencyResultDto = definitions["AgencyResultDto"];
export type AgencyUpdateDto = definitions["AgencyUpdateDto"];

// Agency Status and Type
export type AgencyStatus = NonNullable<Agency["status"]>;
export type AgencyType = NonNullable<Agency["agencyType"]>;

// Agency Settings
export type AgencySettings = definitions["AgencySettings"];
export type AgencySettingsCreateDto = definitions["AgencySettingsCreateDto"];

// Paginated Agency Types
export type PageAgencyResultDto = definitions["Page«AgencyResultDto»"];
