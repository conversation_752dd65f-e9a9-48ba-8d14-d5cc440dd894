export default function Omari({ className }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      className={className}
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 107 68"
      enableBackground="new 0 0 107 68"
      xmlSpace="preserve"
    >
      <g>
        <path
          fill="#FFFFFF"
          stroke="#9AB4D8"
          strokeWidth="0.5"
          strokeMiterlimit="10"
          d="M12.743,4.603h81.513
       c4.494,0,8.238,3.744,8.238,8.24v42.314c0,4.493-3.744,8.239-8.238,8.239H12.743c-4.495,0-8.237-3.746-8.237-8.239V12.843
       C4.505,8.347,8.248,4.603,12.743,4.603"
        />
        <linearGradient
          id="SVGID_1_"
          gradientUnits="userSpaceOnUse"
          x1="42.1279"
          y1="37.6973"
          x2="64.8715"
          y2="14.9538"
        >
          <stop offset="0" style={{ stopColor: "#87C542" }} />
          <stop offset="1" style={{ stopColor: "#0E9B70" }} />
        </linearGradient>
        <path
          fill="url(#SVGID_1_)"
          d="M53.5,10.243c8.882,0,16.082,7.2,16.082,16.082c0,8.882-7.2,16.083-16.082,16.083
       s-16.082-7.201-16.082-16.083C37.418,17.443,44.618,10.243,53.5,10.243L53.5,10.243z"
        />
        <path
          fill="#FFFFFF"
          d="M53.5,13.521c7.07,0,12.805,5.732,12.805,12.804c0,7.072-5.732,12.805-12.805,12.805
       c-7.072,0-12.804-5.733-12.804-12.805C40.696,19.254,46.429,13.521,53.5,13.521L53.5,13.521z"
        />
        <path
          fill="#002385"
          d="M34.2,57.757c3.426,0,5.987-2.464,5.987-5.809s-2.562-5.808-5.987-5.808c-3.46,0-6.005,2.479-6.005,5.808
       S30.74,57.757,34.2,57.757L34.2,57.757z M34.2,56.682c-2.758,0-4.813-2.008-4.813-4.732s2.056-4.731,4.813-4.731
       c2.74,0,4.78,2.009,4.78,4.731C38.98,54.674,36.94,56.682,34.2,56.682L34.2,56.682z M42.168,45.471
       c-0.49,0-0.865,0.359-0.865,0.883c0,0.424,0.245,0.717,0.604,0.832l-0.571,2.349h0.783l0.701-2.251
       c0.146-0.456,0.212-0.652,0.212-0.93C43.032,45.861,42.674,45.471,42.168,45.471L42.168,45.471z M55.83,49.012
       c-1.551,0-2.773,0.701-3.394,1.812c-0.521-1.207-1.632-1.812-3.052-1.812c-1.435,0-2.545,0.604-3.148,1.631v-1.565h-1.109v8.583
       h1.158v-4.503c0-1.959,1.126-3.101,2.872-3.101c1.566,0,2.479,0.93,2.479,2.74v4.862h1.158v-4.503c0-1.959,1.11-3.1,2.872-3.1
       c1.55,0,2.464,0.93,2.464,2.739v4.862h1.158v-4.977C59.289,50.219,57.919,49.012,55.83,49.012L55.83,49.012z M65.07,49.012
       c-1.354,0-2.627,0.424-3.49,1.158l0.521,0.865c0.701-0.62,1.746-1.012,2.854-1.012c1.599,0,2.431,0.799,2.431,2.268v0.521h-2.725
       c-2.464,0-3.313,1.109-3.313,2.432c0,1.484,1.19,2.496,3.133,2.496c1.42,0,2.432-0.538,2.953-1.42v1.339h1.108V52.34
       C68.546,50.121,67.29,49.012,65.07,49.012L65.07,49.012z M64.663,56.827c-1.37,0-2.17-0.62-2.17-1.615
       c0-0.882,0.538-1.534,2.202-1.534h2.691v1.403C66.932,56.207,65.969,56.827,64.663,56.827L64.663,56.827z M72.436,50.758v-1.682
       h-1.108v8.583h1.157v-4.372c0-2.023,1.095-3.166,2.889-3.166c0.082,0,0.18,0.018,0.277,0.018v-1.127
       C74.066,49.012,72.975,49.615,72.436,50.758L72.436,50.758z M77.956,47.201c0.489,0,0.849-0.375,0.849-0.85
       c0-0.439-0.375-0.8-0.849-0.8s-0.849,0.375-0.849,0.816C77.107,46.826,77.482,47.201,77.956,47.201L77.956,47.201z M77.369,57.659
       h1.158v-8.583h-1.158V57.659L77.369,57.659z"
        />
      </g>
    </svg>
  );
}
