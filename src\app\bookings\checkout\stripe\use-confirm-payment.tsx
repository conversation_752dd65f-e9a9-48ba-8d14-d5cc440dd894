import { useMutation } from "@tanstack/react-query";
import { useStripe, useElements } from "@stripe/react-stripe-js";
import { StripeError } from "@stripe/stripe-js";
import { toast } from "sonner";

interface ConfirmPaymentParams {
  bookingId: number;
}

export const useConfirmPayment = () => {
  const stripe = useStripe();
  const elements = useElements();
  const url = process.env.NEXT_PUBLIC_URL || "http://localhost:3000";

  return useMutation({
    mutationFn: async ({ bookingId }: ConfirmPaymentParams) => {
      if (!stripe || !elements) {
        throw new Error("Stripe not initialized");
      }

      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${url}/bookings/checkout/success?bookingId=${bookingId}`,
        },
      });

      if (error) {
        throw error;
      }
    },
    onError: (error: StripeError) => {
      console.log(error);
      const knownErrors = [
        "card_error",
        "validation_error",
        "invalid_request_error",
      ];
      if (knownErrors.includes(error.type)) {
        toast.error(error.message);
      } else {
        toast.error("An unexpected error occurred.");
      }
    },
  });
};
