"use client";

import { <PERSON><PERSON> } from "@/common/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/common/components/ui/dialog";
import { useProviderRedirect } from "@/common/hooks/use-provider-redirect";
import { CheckCircle } from "lucide-react";
import { useSession } from "next-auth/react";
import React from "react";

interface ProviderRedirectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ProviderRedirectModal({
  open,
  onOpenChange,
}: ProviderRedirectModalProps) {
  const { update } = useSession();
  const { redirectToProviderDashboard } = useProviderRedirect();

  const handleGoToDashboard = async () => {
    const session = await update();
    if (session) redirectToProviderDashboard(session);
  };

  const handleStayHere = async () => {
    await update();
    onOpenChange(false);
  };
  return (
    <Dialog open={open} onOpenChange={handleStayHere}>
      <DialogContent>
        <DialogHeader>
          <div className="mx-auto flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <DialogTitle className="text-center">
            Car Rental Profile Created!
          </DialogTitle>
          <DialogDescription className="text-center">
            Your car rental profile has been successfully created. Would you
            like to go to your provider dashboard now?
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <Button variant="outline" onClick={handleStayHere}>
            Stay on Profile Page
          </Button>
          <Button onClick={handleGoToDashboard}>
            Go to Provider Dashboard
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
