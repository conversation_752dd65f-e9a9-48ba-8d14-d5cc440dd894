"use client";

import Link from "next/link";
import { But<PERSON> } from "@/common/components/ui/button";
import { ArrowLeft, Home } from "lucide-react";
import { useRouter } from "next/navigation";

export default function Error() {
  const router = useRouter();

  return (
    <main className="flex min-h-[75vh] flex-col items-center justify-center">
      <div className="container flex max-w-[64rem] flex-col items-center gap-4 text-center">
        <p className="text-primary text-sm font-medium">500</p>
        <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
          Server error
        </h1>
        <p className="text-muted-foreground max-w-[42rem] leading-normal sm:text-xl sm:leading-8">
          Sorry, something went wrong on our end. Please try again later.
        </p>
        <div className="flex gap-4">
          <Button
            onClick={() => router.back()}
            variant="default"
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Go back
          </Button>
          <Button variant="ghost" asChild className="gap-2">
            <Link href="/">
              <Home size={16} />
              Go home
            </Link>
          </Button>
        </div>
      </div>
    </main>
  );
}
