FROM node:20

WORKDIR /app

# Accept build arguments for all NEXT_PUBLIC_* variables you need at build time
ARG NEXT_PUBLIC_BRAND_NAME
ARG NEXT_PUBLIC_AGENCY_ID 

# Set as environment variables for build
ENV NEXT_PUBLIC_BRAND_NAME=$NEXT_PUBLIC_BRAND_NAME
ENV NEXT_PUBLIC_AGENCY_ID=$NEXT_PUBLIC_AGENCY_ID

COPY package*.json ./
RUN npm install --force
COPY . .
RUN npm run build
EXPOSE 3000
CMD npm run start
# CMD npm run dev
