"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useRouter } from "next/navigation";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/common/components/ui/table";
import { VehicleBooking } from "@/common/models";
import useBookings from "@/common/hooks/use-bookings";
import useBookingsQuery from "@/common/hooks/use-bookings-query";
import { bookingsColumns } from "@/app/bookings/components/bookings-columns";
import { DataTablePagination } from "@/app/bookings/components/data-table-pagination";
import { DataTableFilters } from "@/app/bookings/components/data-table-filters";
import { Skeleton } from "@/common/components/ui/skeleton";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  searchCriteria: string | null;
  statuses: string[];
  onSearchChange: (search: string | null) => void;
  onStatusesChange: (statuses: string[]) => void;
}

function DataTable<TData, TValue>({
  columns,
  data,
  totalItems,
  searchCriteria,
  statuses,
  onSearchChange,
  onStatusesChange,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const { isLoading } = useBookings();
  const { state } = useBookingsQuery();
  const router = useRouter();

  const handleRowClick = (rowData: TData) => {
    const booking = rowData as VehicleBooking;
    if (booking.id) {
      router.push(`/bookings/${booking.id}`);
    }
  };

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  return (
    <div className="w-full space-y-4 overflow-hidden p-4">
      <DataTableFilters
        table={table}
        searchCriteria={searchCriteria}
        statuses={statuses}
        onSearchChange={onSearchChange}
        onStatusesChange={onStatusesChange}
      />
      <div className="w-full overflow-hidden rounded-md border p-1">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Show skeleton rows based on current page size
              Array.from({ length: state.size }).map((_, i) => (
                <TableRow key={`skeleton-${i}`}>
                  <TableCell>
                    <Skeleton className="h-4 w-12" />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-16 rounded-full" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8 rounded" />
                  </TableCell>
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className="cursor-pointer"
                  onClick={() => handleRowClick(row.original)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No bookings found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} totalItems={totalItems} />
    </div>
  );
}

export function BookingsTable() {
  const { data: bookingsData, isError } = useBookings();
  const {
    state,
    updatePage,
    updateSize,
    updateSearchCriteria,
    updateStatuses,
  } = useBookingsQuery();

  if (isError) {
    return (
      <div className="flex h-24 items-center justify-center rounded-md border px-4">
        <div className="text-center">
          <p className="text-muted-foreground text-sm">
            Error loading bookings. Please try again.
          </p>
        </div>
      </div>
    );
  }

  // Always render the DataTable, let it handle loading state internally
  return (
    <DataTable
      columns={bookingsColumns}
      data={bookingsData?.content || []}
      totalItems={bookingsData?.totalElements || 0}
      onPageChange={updatePage}
      onPageSizeChange={updateSize}
      searchCriteria={state.searchCriteria}
      statuses={state.statuses}
      onSearchChange={updateSearchCriteria}
      onStatusesChange={updateStatuses}
    />
  );
}
