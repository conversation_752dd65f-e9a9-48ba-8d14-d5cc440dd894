import { remark } from "remark";
import remarkGfm from "remark-gfm";
import remarkRehype from "remark-rehype";
import rehypeStringify from "rehype-stringify";
import matter from "gray-matter";

export async function markdownToHtml(markdown: string) {
  const { content, data } = matter(markdown);
  const processedContent = await remark()
    .use(remarkGfm)
    .use(remarkRehype)
    .use(rehypeStringify)
    .process(content);
  const contentHtml = processedContent.toString();

  return {
    contentHtml,
    frontmatter: data,
  };
}
