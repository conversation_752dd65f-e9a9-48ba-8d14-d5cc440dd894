import type { MetadataRoute } from 'next'
import { getCurrentBrandConfig } from '@/common/config/brands/utils'

export default function sitemap(): MetadataRoute.Sitemap {
    const brandConfig = getCurrentBrandConfig()

    // Only generate sitemap for Karlink brand
    if (brandConfig.name.toLowerCase() !== 'karlink') {
        return []
    }

    const baseUrl = 'https://mykarlink.com'

    // Static pages
    const staticPages = [
        {
            url: baseUrl,
            lastModified: new Date(),
            changeFrequency: 'daily' as const,
            priority: 1,
        },
        {
            url: `${baseUrl}/about-us`,
            lastModified: new Date(),
            changeFrequency: 'monthly' as const,
            priority: 0.8,
        },
        {
            url: `${baseUrl}/contact-us`,
            lastModified: new Date(),
            changeFrequency: 'monthly' as const,
            priority: 0.7,
        },
        {
            url: `${baseUrl}/advice-for-hirers`,
            lastModified: new Date(),
            changeFrequency: 'monthly' as const,
            priority: 0.6,
        },
        {
            url: `${baseUrl}/community-guidelines`,
            lastModified: new Date(),
            changeFrequency: 'yearly' as const,
            priority: 0.5,
        },
        {
            url: `${baseUrl}/cancellation-and-refund-policy`,
            lastModified: new Date(),
            changeFrequency: 'yearly' as const,
            priority: 0.4,
        },
        {
            url: `${baseUrl}/terms-and-conditions`,
            lastModified: new Date(),
            changeFrequency: 'yearly' as const,
            priority: 0.4,
        },
        {
            url: `${baseUrl}/privacy-policy`,
            lastModified: new Date(),
            changeFrequency: 'yearly' as const,
            priority: 0.4,
        },
        {
            url: `${baseUrl}/vehicles`,
            lastModified: new Date(),
            changeFrequency: 'daily' as const,
            priority: 0.9,
        },
    ]

    return staticPages
}
