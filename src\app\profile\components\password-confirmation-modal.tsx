"use client";

import React, { useState, useActionState, startTransition } from "react";
import { But<PERSON> } from "@/common/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/common/components/ui/dialog";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { Eye, EyeOff } from "lucide-react";
import Loader from "@/common/components/loader";
import {
  reauthenticateUserAction,
  ReauthenticateUserState,
  createProviderProfileAction,
  CreateProviderProfileState,
} from "@/app/profile/actions";
import { ProviderFormData } from "./create-provider-profile-modal";

interface PasswordConfirmationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  onBack: () => void;
  userEmail: string;
  profileData: ProviderFormData;
}

export function PasswordConfirmationModal({
  open,
  onOpenChange,
  onSuccess,
  onBack,
  userEmail,
  profileData,
}: PasswordConfirmationModalProps) {
  // Local state for password input
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  // Authentication state
  const [authState, authAction, isAuthPending] = useActionState<
    ReauthenticateUserState,
    FormData
  >(reauthenticateUserAction, undefined);

  // Profile creation state
  const [profileState, profileAction, isProfilePending] = useActionState<
    CreateProviderProfileState,
    FormData
  >(createProviderProfileAction, undefined);

  // Handle successful authentication
  React.useEffect(() => {
    if (authState && "success" in authState && authState.success === true) {
      const formData = new FormData();
      formData.append("firstName", profileData.firstName);
      formData.append("lastName", profileData.lastName);
      formData.append("email", profileData.email);
      formData.append("telephone", profileData.telephone);
      formData.append("companyName", profileData.companyName);

      startTransition(() => profileAction(formData));
    }
  }, [authState, profileAction]);

  // Handle successful profile creation
  React.useEffect(() => {
    if (
      profileState &&
      "success" in profileState &&
      profileState.success === true
    ) {
      if (!password.trim()) return;

      const formData = new FormData();
      formData.append("password", password);
      formData.append("username", userEmail);
      formData.append("refresh", "true");

      reauthenticateUserAction(undefined, formData).then(() => {
        onSuccess();
      });
    }
  }, [profileState]);

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!password.trim()) return;

    const formData = new FormData();
    formData.append("password", password);
    formData.append("username", userEmail);

    startTransition(() => authAction(formData));
  };

  const handleBack = () => {
    setPassword("");
    onBack();
  };

  const handleClose = () => {
    setPassword("");
    onOpenChange(false);
  };

  const isPending = isAuthPending || isProfilePending;
  const hasAuthError =
    authState && "success" in authState && !authState.success;
  const hasProfileError =
    profileState && "success" in profileState && !profileState.success;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-center">
            Confirm Your Password
          </DialogTitle>
          <DialogDescription className="text-center">
            Please enter your password to confirm your identity before creating
            your car rental profile.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handlePasswordSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pr-10"
                placeholder="Enter your password"
                required
                disabled={isPending}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isPending}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500" />
                )}
              </Button>
            </div>
          </div>

          {/* Error Messages */}
          {hasAuthError && (
            <div className="text-sm text-red-600">
              {authState.message || "Invalid password. Please try again."}
            </div>
          )}

          {hasProfileError && (
            <div className="text-sm text-red-600">
              {profileState.message ||
                "Failed to create profile. Please try again."}
            </div>
          )}
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={isPending}
            >
              Back
            </Button>
            <Button type="submit" disabled={isPending || !password.trim()}>
              {isPending && <Loader />}
              {isProfilePending
                ? "Creating Profile..."
                : isAuthPending
                  ? "Verifying..."
                  : "Confirm"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
