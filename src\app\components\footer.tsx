import { Mail } from "lucide-react";
import Link from "next/link";
import { WhatsAppIcon } from "@/common/components/whatsapp-icon";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";

function Footer() {
  const brandConfig = getCurrentBrandConfig();

  // Contact data for the footer
  const contactData = [
    {
      value: brandConfig.contactInfo.phone,
      icon: WhatsAppIcon,
      href: `https://wa.me/${brandConfig.contactInfo.phone.replace(/\+|\s/g, "")}`,
    },
    {
      value: brandConfig.contactInfo.email,
      icon: Mail,
      href: `mailto:${brandConfig.contactInfo.email}`,
    },
  ];

  return (
    <footer className="bg-primary">
      <div className="container-footer text-background container grid items-start gap-16 pt-16 pb-8 text-lg md:grid-cols-2 lg:grid-cols-4 lg:pt-20">
        {/* Primary links */}
        <ul className="grid gap-3 text-sm lg:text-[14px]">
          <li className="-mb-1 text-lg font-bold text-white lg:text-xl">
            {brandConfig.brandName}
          </li>

          {brandConfig.links.primary.map((link) => (
            <li key={link.name}>
              <Link
                id={link.name}
                href={link.href}
                className="hover:text-secondary transition-all duration-300 hover:underline"
              >
                {link.name}
              </Link>
            </li>
          ))}
        </ul>

        {/* Important links */}
        <ul className="grid gap-3 text-sm lg:text-[14px]">
          <li className="-mb-1 text-lg font-bold text-white lg:text-xl">
            Important Links
          </li>
          {brandConfig.links.important.map((link) => (
            <li key={link.name}>
              <Link
                id={link.name}
                href={link.href}
                className="hover:text-secondary transition-all duration-300 hover:underline"
              >
                {link.name}
              </Link>
            </li>
          ))}
        </ul>

        {/* Contact links */}
        <ul className="grid gap-3 text-sm lg:text-[14px]">
          <li className="-mb-1 text-lg font-bold text-white lg:text-xl">
            Talk to us
          </li>
          {contactData.map((contact) => (
            <li key={contact.value}>
              <a
                href={contact.href}
                target="_blank"
                className="hover:text-secondary flex items-center gap-x-2 hover:underline"
              >
                <contact.icon className="size-5" />
                <span>{contact.value}</span>
              </a>
            </li>
          ))}
        </ul>

        {/* Logo and copyright */}
        <div className="grid gap-4">
          {/* Logo */}
          <div className="w-52">
            <Link href="/" className="hover:text-secondary">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={brandConfig.logo.footer}
                alt={`${brandConfig.brandName} logo`}
                className="block w-full"
                width={128}
                height={40}
              />
            </Link>
          </div>
          {brandConfig.links.social && brandConfig.links.social.length > 0 && (
            <div className="mb-2 flex items-center gap-3">
              {brandConfig.links.social.map((social) => {
                return (
                  <Link
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={social.name}
                    className="text-secondary hover:text-secondary/80 transition-all duration-200"
                  >
                    <social.icon className="size-6" />
                  </Link>
                );
              })}
            </div>
          )}
          {/* Copyright */}
          <p className="text-sm">
            {brandConfig.copyright.replace(
              "{year}",
              new Date().getFullYear().toString(),
            )}
          </p>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
