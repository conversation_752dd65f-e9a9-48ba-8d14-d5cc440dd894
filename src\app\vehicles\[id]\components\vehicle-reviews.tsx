import { User } from "lucide-react";
import { Ava<PERSON>, AvatarFallback } from "@/common/components/ui/avatar";
import { Card, CardContent, CardHeader } from "@/common/components/ui/card";
import { Rating as Review } from "@/common/models";

const calculateAverageRating = (review: Review): number => {
  if (!review.ratingItems || review.ratingItems.length === 0) {
    return 0;
  }

  const sum = review.ratingItems.reduce((total, item) => total + item.rate, 0);
  const average = sum / review.ratingItems.length;
  return Math.round(average * 10) / 10; // Round to 1 decimal place
};

interface ReviewsProps {
  reviews: Review[];
}

const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <span key={star} className="relative">
          {/* Empty star (background) */}
          <svg
            className="fill-muted/80 stroke-muted-foreground h-4 w-4"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          {/* Filled star (overlay) */}
          <svg
            className="fill-primary absolute top-0 left-0 h-4 w-4"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              clipPath: `inset(0 ${100 - (rating - star + 1) * 100}% 0 0)`,
            }}
          >
            <path
              d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </span>
      ))}
      <span className="text-muted-foreground ml-2 text-sm">
        {rating.toFixed(1)}
      </span>
    </div>
  );
};

export default function Reviews({ reviews }: ReviewsProps) {
  return (
    <Card className="gap-1">
      <CardHeader>
        <h2 className="m-0 text-2xl font-semibold tracking-tight">Reviews</h2>
      </CardHeader>
      <CardContent>
        <div className="grid gap-2">
          {reviews.length === 0 && (
            <p className="text-black/50">No reviews yet</p>
          )}
          {reviews.map((review, index) => (
            <div
              key={index}
              className="border-b border-b-gray-100 bg-white last:border-none"
            >
              <div className="flex items-start gap-4 py-6">
                <Avatar className="h-10 w-10 shrink-0">
                  <AvatarFallback className="bg-primary/15">
                    <User className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
                <div className="grid gap-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold">{review.name}</h3>
                    <StarRating rating={calculateAverageRating(review)} />
                  </div>
                  <p className="text-muted-foreground text-sm">
                    {review.comment}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
