import { <PERSON><PERSON> } from "@/common/components/ui/button";
import { Card, CardContent } from "@/common/components/ui/card";
import { UserX } from "lucide-react";
import Link from "next/link";

export default function ClientNotFound() {
  return (
    <div className="container pt-28 pb-20">
      <div className="mx-auto max-w-4xl px-4 py-12 sm:px-6">
        <Card className="text-center">
          <CardContent>
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-amber-100">
              <UserX className="h-8 w-8 text-amber-600" />
            </div>
            <h2 className="mb-2 text-xl font-semibold text-gray-800">
              Client Not Found
            </h2>
            <p className="mb-6 text-gray-600">
              We couldn&apos;t find the client you&apos;re looking for. They may
              have been removed or you might have followed an invalid link.
            </p>
            <Button asChild>
              <Link href="/">Go Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
