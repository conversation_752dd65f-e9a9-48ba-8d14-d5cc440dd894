import { Client } from "@/common/models";
import AuthService from "@/common/services/auth.service";
import ClientService from "@/common/services/client.service";
import NextAuth, { CredentialsSignin, DefaultSession, User } from "next-auth";
import Credentials from "next-auth/providers/credentials";

declare module "next-auth" {
    /**
     * Returned by `auth`, `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
     */
    interface User {
        userType: "ADMIN" | "AGENCY" | "CLIENT"
        agentId: number;
        id?: string | undefined;
        clientId: null | number;
        client: Omit<Client, "clientDocs">;
        workerId: number;
        firstName: string;
        lastName: string;
        access_token: string;
        token_type: string;
        refresh_token: string;
        scope: string;
        expires_in: string;
    }

    interface Session {
        user: User & DefaultSession["user"];
    }
}

export class InvalidLoginError extends CredentialsSignin {
    code = "Invalid username or password";
}
const userService = new AuthService();
const clientService = new ClientService();

export const { handlers, signIn, signOut, auth } = NextAuth({
    providers: [
        Credentials({
            credentials: {
                username: {
                    type: "text",
                },
                password: {
                    type: "password",
                },
            },
            authorize: async (credentials) => {
                const username = credentials?.username as string;
                const password = credentials?.password as string;

                try {
                    const data = (await userService.login({
                        username,
                        password,
                    })) as unknown as User;

                    if (data.userType === "CLIENT") {
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        const { clientDocs, ...client } = await clientService.findById(data.clientId!);
                        data.client = client;
                    }

                    return data;
                } catch {
                    throw new InvalidLoginError();
                }
            },
        }),
    ],
    session: {
        strategy: "jwt",
        maxAge: 60 * 60 * 24, // 1 day in seconds
    },
    callbacks: {
        async session({ session, token }) {
            // TODO: Fix this type error with AuthJS.
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            session.user = token.user;
            return session;
        },
        async jwt({ token, user }) {
            if (user) {
                token.user = user;
            }
            return token;
        },
    },
});
