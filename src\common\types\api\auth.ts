import { components } from "@/common/types/api/auth-schema";

// Main Authentication Types
export type AuthenticationResult = components["schemas"]["ResultDTO"];
export type LoginRequest = components["schemas"]["UserLogin"];
export type ClientLoginRequest = components["schemas"]["UserLoginRequest"];
export type UserType = components["schemas"]["ResultDTO"]["userType"];

// Role and Permission Types
export type Role = components["schemas"]["Role"];
export type Permission = components["schemas"]["UserPermission"];

// Alias for service compatibility
export type User = AuthenticationResult;
