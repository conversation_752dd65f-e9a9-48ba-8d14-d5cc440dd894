"use client";

import type React from "react";
import { useState, useRef } from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/common/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import { Input } from "@/common/components/ui/input";
import {
  PlusIcon,
  XIcon,
  FileIcon,
  AlertCircleIcon,
  CameraIcon,
  SwitchCameraIcon,
} from "lucide-react";
import { Camera } from "react-camera-pro";
import type { NewDocument } from "./document-upload";
import { cn } from "@/common/lib/shadcn-utils";
import {
  documentTypeSchema,
  fileSchema,
  MAX_FILE_SIZE,
  ACCEPTED_IMAGE_TYPES,
} from "../validations";
import { DOCUMENT_TYPE_MAP } from "@/common/lib/document-utils";
import { ZodError } from "zod";

interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddDocument: (document: NewDocument) => void;
  addedDocumentTypes: string[];
}

// Document type mapping (display name to API value)
const documentTypeOptions = Object.entries(DOCUMENT_TYPE_MAP).map(
  ([value, display]) => ({
    value,
    display,
  }),
);

export default function DocumentUploadModal({
  isOpen,
  onClose,
  onAddDocument,
  addedDocumentTypes,
}: DocumentUploadModalProps) {
  const [documentType, setDocumentType] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isCameraMode, setIsCameraMode] = useState(false);
  const [numberOfCameras, setNumberOfCameras] = useState(0);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [errors, setErrors] = useState<{
    documentType?: string;
    file?: string;
  }>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraRef = useRef<{
    takePhoto: () => string;
    switchCamera: () => void;
  } | null>(null);

  const resetForm = () => {
    setDocumentType("");
    setFile(null);
    setIsCameraMode(false);
    setCapturedImage(null);
    setErrors({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Camera functions
  const takePhoto = () => {
    if (cameraRef.current) {
      const photo = cameraRef.current.takePhoto();
      setCapturedImage(photo);

      // Convert base64 to File object
      fetch(photo)
        .then((res) => res.blob())
        .then((blob) => {
          const timestamp = new Date().getTime();
          const file = new File([blob], `document-${timestamp}.jpg`, {
            type: "image/jpeg",
          });
          validateAndSetFile(file);
        });
    }
  };

  const switchCamera = () => {
    if (cameraRef.current && numberOfCameras > 1) {
      cameraRef.current.switchCamera();
    }
  };

  const retakePhoto = () => {
    setCapturedImage(null);
    setFile(null);
    setErrors((prev) => ({ ...prev, file: undefined }));
  };

  const toggleCameraMode = () => {
    setIsCameraMode(!isCameraMode);
    setFile(null);
    setCapturedImage(null);
    setErrors((prev) => ({ ...prev, file: undefined }));
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    setErrors({});

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      validateAndSetFile(droppedFile);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setErrors({});
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      validateAndSetFile(selectedFile);
    }
  };

  const validateAndSetFile = (file: File) => {
    try {
      // Validate file type
      if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
        setErrors((prev) => ({
          ...prev,
          file: "Only JPEG, JPG, PNG, and WEBP images are allowed",
        }));
        return;
      }

      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        setErrors((prev) => ({
          ...prev,
          file: "File size must be less than 1MB",
        }));
        return;
      }

      // Validate with Zod
      fileSchema.parse(file);
      setFile(file);
    } catch (error: unknown) {
      if (error instanceof ZodError) {
        setErrors((prev) => ({
          ...prev,
          file: error.errors[0]?.message || "Invalid file",
        }));
      }
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + " bytes";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  };

  const handleSubmit = () => {
    setErrors({});
    let hasErrors = false;

    // Validate document type
    if (!documentType) {
      setErrors((prev) => ({
        ...prev,
        documentType: "Please select a document type",
      }));
      hasErrors = true;
    } else {
      try {
        documentTypeSchema.parse(documentType);
      } catch {
        setErrors((prev) => ({
          ...prev,
          documentType: "Invalid document type",
        }));
        hasErrors = true;
      }
    }

    // Validate file
    if (!file) {
      setErrors((prev) => ({ ...prev, file: "Please upload a document" }));
      hasErrors = true;
    }

    if (hasErrors) return;

    const newDocument: NewDocument = {
      name: documentType as NewDocument["name"],
      file: file!,
    };

    onAddDocument(newDocument);
    resetForm();
  };

  // Check if there are any document types left to add
  const allDocumentTypesAdded = documentTypeOptions.every((type) =>
    addedDocumentTypes.includes(type.value),
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="mx-auto w-[95vw] max-w-md rounded-lg p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold sm:text-xl">
            Add new document
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 sm:space-y-6">
          <div className="space-y-1 sm:space-y-2">
            <label className="text-sm font-medium sm:text-base">
              Document Type
            </label>
            <Select
              value={documentType}
              onValueChange={(value) => {
                setDocumentType(value);
                setErrors((prev) => ({ ...prev, documentType: undefined }));
              }}
              disabled={allDocumentTypesAdded}
            >
              <SelectTrigger
                className={cn(
                  "w-full text-sm sm:text-base",
                  errors.documentType && "border-red-500",
                )}
              >
                <SelectValue
                  placeholder={
                    allDocumentTypesAdded
                      ? "All document types added"
                      : "Select document type"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {documentTypeOptions.map((type) => {
                  const isDisabled = addedDocumentTypes.includes(type.value);
                  return (
                    <SelectItem
                      key={type.value}
                      value={type.value}
                      disabled={isDisabled}
                      className={cn(
                        "text-sm sm:text-base",
                        isDisabled && "text-gray-400 line-through",
                      )}
                    >
                      {type.display} {isDisabled && "(Already added)"}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {errors.documentType && (
              <div className="mt-1 flex items-center gap-1 text-xs text-red-500">
                <AlertCircleIcon className="h-3 w-3" />
                <span>{errors.documentType}</span>
              </div>
            )}
            {allDocumentTypesAdded && (
              <p className="mt-1 text-xs text-amber-600">
                All document types have been added. Remove an existing document
                to add a different type.
              </p>
            )}
          </div>

          <div className="space-y-1 sm:space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium sm:text-base">
                Select Document (Images only)
              </label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={toggleCameraMode}
                  disabled={allDocumentTypesAdded}
                  className="text-xs sm:text-sm"
                >
                  <CameraIcon className="mr-1 h-3 w-3" />
                  {isCameraMode ? "Upload File" : "Use Camera"}
                </Button>
              </div>
            </div>

            {isCameraMode ? (
              // Camera Mode
              <div className="space-y-4">
                {!capturedImage ? (
                  <div className="relative">
                    <div className="relative z-0 aspect-video w-full overflow-hidden rounded-md border-2 border-gray-300">
                      <Camera
                        ref={cameraRef}
                        aspectRatio="cover"
                        numberOfCamerasCallback={setNumberOfCameras}
                        errorMessages={{
                          noCameraAccessible:
                            "No camera accessible. Please check camera permissions or use file upload.",
                          permissionDenied:
                            "Camera permission denied. Please allow camera access and try again.",
                          switchCamera:
                            "Cannot switch camera - only one camera available.",
                          canvas: "Canvas not supported by your browser.",
                        }}
                      />
                    </div>
                    <div className="relative z-10 mt-3 flex justify-center gap-2">
                      <Button
                        type="button"
                        onClick={takePhoto}
                        className="flex items-center gap-2"
                      >
                        <CameraIcon className="h-4 w-4" />
                        Take Photo
                      </Button>
                      {numberOfCameras > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={switchCamera}
                          className="flex items-center gap-2"
                        >
                          <SwitchCameraIcon className="h-4 w-4" />
                          Switch Camera
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  // Preview captured photo
                  <div className="space-y-3">
                    <div className="relative">
                      <Image
                        src={capturedImage}
                        alt="Captured document"
                        width={400}
                        height={256}
                        className="max-h-64 w-full rounded-md border-2 border-gray-300 object-contain"
                      />
                    </div>
                    <div className="flex justify-center gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={retakePhoto}
                        className="flex items-center gap-2"
                      >
                        <CameraIcon className="h-4 w-4" />
                        Retake Photo
                      </Button>
                    </div>
                    {file && (
                      <div className="text-center">
                        <p className="text-sm font-medium">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              // File Upload Mode
              <div
                className={cn(
                  "rounded-md border-2 border-dashed p-4 text-center sm:p-6",
                  isDragging
                    ? "border-blue-500 bg-blue-50"
                    : errors.file
                      ? "border-red-300"
                      : "border-gray-300",
                  !allDocumentTypesAdded && "cursor-pointer",
                  allDocumentTypesAdded && "opacity-50",
                )}
                onDragOver={!allDocumentTypesAdded ? handleDragOver : undefined}
                onDragLeave={
                  !allDocumentTypesAdded ? handleDragLeave : undefined
                }
                onDrop={!allDocumentTypesAdded ? handleDrop : undefined}
                onClick={
                  !allDocumentTypesAdded
                    ? () => fileInputRef.current?.click()
                    : undefined
                }
              >
                <Input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept=".jpg,.jpeg,.png,.webp"
                  onChange={handleFileChange}
                  disabled={allDocumentTypesAdded}
                />

                {file ? (
                  <div className="flex flex-col items-center">
                    <div className="relative">
                      <div className="flex items-center justify-center rounded-md bg-blue-50 p-2">
                        <FileIcon className="h-8 w-8 text-blue-500 sm:h-10 sm:w-10" />
                      </div>
                      <button
                        className="absolute -top-2 -right-2 rounded-full bg-white p-1 shadow-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          setFile(null);
                          setErrors((prev) => ({ ...prev, file: undefined }));
                        }}
                        disabled={allDocumentTypesAdded}
                      >
                        <XIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                      </button>
                    </div>
                    <p className="mt-2 text-sm font-medium sm:text-base">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500 sm:text-sm">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                ) : (
                  <>
                    <PlusIcon className="mx-auto h-10 w-10 text-gray-400 sm:h-12 sm:w-12" />
                    <p className="mt-2 text-sm sm:text-base">
                      {allDocumentTypesAdded
                        ? "No more document types available"
                        : "Choose a file or drag it here"}
                    </p>
                    <p className="mt-1 text-xs text-gray-500 sm:text-sm">
                      {!allDocumentTypesAdded &&
                        "Supported formats: JPEG, PNG, WEBP (max 1MB)"}
                    </p>
                  </>
                )}
              </div>
            )}
            {errors.file && (
              <div className="mt-1 flex items-center gap-1 text-xs text-red-500">
                <AlertCircleIcon className="h-3 w-3" />
                <span>{errors.file}</span>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={allDocumentTypesAdded}
          >
            Add
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
