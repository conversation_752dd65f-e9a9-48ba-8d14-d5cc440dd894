import { type CountryCode } from "libphonenumber-js";
import {
  detectCountryFromTimezone,
  detectCountryFromLanguage,
  detectCountryFromIP,
} from "./country_utils";

/**
 * Mapping of country codes to their primary currencies
 * Based on ISO 4217 currency codes
 */
export const COUNTRY_TO_CURRENCY: Record<CountryCode, string> = {
  // Major economies
  US: "USD", // United States
  GB: "GBP", // United Kingdom
  DE: "EUR", // Germany
  FR: "EUR", // France
  IT: "EUR", // Italy
  ES: "EUR", // Spain
  NL: "EUR", // Netherlands
  BE: "EUR", // Belgium
  AT: "EUR", // Austria
  PT: "EUR", // Portugal
  IE: "EUR", // Ireland
  FI: "EUR", // Finland
  GR: "EUR", // Greece
  LU: "EUR", // Luxembourg
  MT: "EUR", // Malta
  CY: "EUR", // Cyprus
  SK: "EUR", // Slovakia
  SI: "EUR", // Slovenia
  EE: "EUR", // Estonia
  LV: "EUR", // Latvia
  LT: "EUR", // Lithuania
  
  // Other major currencies
  CA: "CAD", // Canada
  AU: "AUD", // Australia
  NZ: "NZD", // New Zealand
  JP: "JPY", // Japan
  CH: "CHF", // Switzerland
  SE: "SEK", // Sweden
  NO: "NOK", // Norway
  DK: "DKK", // Denmark
  PL: "PLN", // Poland
  CZ: "CZK", // Czech Republic
  HU: "HUF", // Hungary
  RO: "RON", // Romania
  BG: "BGN", // Bulgaria
  HR: "EUR", // Croatia (adopted EUR in 2023)
  
  // Asia Pacific
  CN: "CNY", // China
  HK: "HKD", // Hong Kong
  SG: "SGD", // Singapore
  KR: "KRW", // South Korea
  TW: "TWD", // Taiwan
  MY: "MYR", // Malaysia
  TH: "THB", // Thailand
  ID: "IDR", // Indonesia
  PH: "PHP", // Philippines
  VN: "VND", // Vietnam
  IN: "INR", // India
  
  // Americas
  MX: "MXN", // Mexico
  BR: "BRL", // Brazil
  AR: "ARS", // Argentina
  CL: "CLP", // Chile
  CO: "COP", // Colombia
  PE: "PEN", // Peru
  
  // Middle East & Africa
  AE: "AED", // UAE
  SA: "SAR", // Saudi Arabia
  IL: "ILS", // Israel
  TR: "TRY", // Turkey
  ZA: "ZAR", // South Africa
  EG: "EGP", // Egypt
  
  // Others - fallback to USD for countries not explicitly mapped
  // This is a simplified mapping - in reality, you'd want to include more countries
} as const;

/**
 * Detect currency based on user's location using multiple methods
 */
export interface CurrencyDetectionResult {
  currency: string;
  method: "timezone" | "language" | "ip" | "fallback";
  confidence: number; // 0-100
  country?: CountryCode;
}

/**
 * Detect user's preferred currency based on their location
 * Uses multiple detection methods with fallback
 */
export async function detectUserCurrency(): Promise<CurrencyDetectionResult> {
  // Method 1: Try timezone-based detection (fast, moderate accuracy)
  const timezoneCountry = detectCountryFromTimezone();
  if (timezoneCountry && COUNTRY_TO_CURRENCY[timezoneCountry]) {
    return {
      currency: COUNTRY_TO_CURRENCY[timezoneCountry],
      method: "timezone",
      confidence: 70,
      country: timezoneCountry,
    };
  }

  // Method 2: Try language-based detection (fast, lower accuracy)
  const languageCountry = detectCountryFromLanguage();
  if (languageCountry && COUNTRY_TO_CURRENCY[languageCountry]) {
    return {
      currency: COUNTRY_TO_CURRENCY[languageCountry],
      method: "language",
      confidence: 60,
      country: languageCountry,
    };
  }

  // Method 3: Try IP-based detection (slower, higher accuracy)
  try {
    const ipCountry = await detectCountryFromIP();
    if (ipCountry && COUNTRY_TO_CURRENCY[ipCountry]) {
      return {
        currency: COUNTRY_TO_CURRENCY[ipCountry],
        method: "ip",
        confidence: 85,
        country: ipCountry,
      };
    }
  } catch (error) {
    console.warn("IP-based currency detection failed:", error);
  }

  // Fallback to USD
  return {
    currency: "USD",
    method: "fallback",
    confidence: 100, // We're certain about the fallback
  };
}

/**
 * Get currency for a specific country
 */
export function getCurrencyForCountry(countryCode: CountryCode): string {
  return COUNTRY_TO_CURRENCY[countryCode] || "USD";
}

/**
 * Check if a currency is supported by the system
 * This is a fallback list - the actual validation should use the API
 */
export function isCurrencySupportedFallback(currencyCode: string): boolean {
  // Common supported currencies - fallback when API is not available
  const supportedCurrencies = [
    "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "CNY", "SEK", "NOK",
    "DKK", "PLN", "CZK", "HUF", "RON", "BGN", "HKD", "SGD", "KRW", "TWD",
    "MYR", "THB", "IDR", "PHP", "VND", "INR", "MXN", "BRL", "ARS", "CLP",
    "COP", "PEN", "AED", "SAR", "ILS", "TRY", "ZAR", "EGP", "NZD"
  ];

  return supportedCurrencies.includes(currencyCode);
}

/**
 * Detect currency with validation against supported currencies
 * Uses fallback validation - for better validation, use the hook-based approach
 */
export async function detectSupportedUserCurrency(): Promise<CurrencyDetectionResult> {
  const result = await detectUserCurrency();

  // If detected currency is not supported, fallback to USD
  if (!isCurrencySupportedFallback(result.currency)) {
    return {
      currency: "USD",
      method: "fallback",
      confidence: 100,
    };
  }

  return result;
}

/**
 * Detect currency with API-based validation
 * This version checks against actual supported currencies from the API
 */
export async function detectSupportedUserCurrencyWithAPI(
  supportedCurrencies: string[]
): Promise<CurrencyDetectionResult> {
  const result = await detectUserCurrency();

  // If detected currency is not supported, fallback to USD
  if (!supportedCurrencies.includes(result.currency)) {
    return {
      currency: "USD",
      method: "fallback",
      confidence: 100,
    };
  }

  return result;
}

/**
 * Get a list of currencies for common countries
 * Useful for currency selector components
 */
export function getCommonCurrencies(): Array<{ code: string; country: CountryCode; name: string }> {
  return [
    { code: "USD", country: "US", name: "US Dollar" },
    { code: "EUR", country: "DE", name: "Euro" },
    { code: "GBP", country: "GB", name: "British Pound" },
    { code: "CAD", country: "CA", name: "Canadian Dollar" },
    { code: "AUD", country: "AU", name: "Australian Dollar" },
    { code: "JPY", country: "JP", name: "Japanese Yen" },
    { code: "CHF", country: "CH", name: "Swiss Franc" },
    { code: "CNY", country: "CN", name: "Chinese Yuan" },
    { code: "SEK", country: "SE", name: "Swedish Krona" },
    { code: "NOK", country: "NO", name: "Norwegian Krone" },
  ];
}
