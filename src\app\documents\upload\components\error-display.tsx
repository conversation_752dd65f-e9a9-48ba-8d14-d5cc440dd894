"use client";

import { But<PERSON> } from "@/common/components/ui/button";
import { Card, CardContent } from "@/common/components/ui/card";
import { AlertTriangleIcon } from "lucide-react";
import Link from "next/link";

interface ErrorDisplayProps {
  error: Error;
}

export default function ErrorDisplay({ error }: ErrorDisplayProps) {
  return (
    <div className="container pt-28 pb-20">
      <div className="mx-auto max-w-4xl px-4 py-12 sm:px-6">
        <Card className="text-center">
          <CardContent>
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <AlertTriangleIcon className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="mb-2 text-xl font-semibold text-gray-800">
              Something went wrong
            </h2>
            <p className="mb-6 text-gray-600">
              We encountered an error while loading your information. Please try
              again later.
            </p>
            <div className="mx-auto mb-6 max-w-md rounded-md bg-gray-50 p-3 text-sm text-gray-500">
              Error: {error.message}
            </div>
            <Button asChild>
              <Link href="/">Go Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
