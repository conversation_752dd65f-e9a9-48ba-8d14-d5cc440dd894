"use client";
import { createContext, useContext, useState, ReactNode } from "react";
import { useVehicleQuery } from "@/common/hooks/use-vehicle-query";
import { PromotionType } from "@/common/models";

type FilterState = {
  maxDeposit: number | null;
  minPrice: number | null;
  maxPrice: number | null;
  mileageAllowance: number | null;
  transmission: "AUTO" | "MANUAL" | null;
  hasPromotion: boolean | null;
  promotionType: PromotionType | null;
};

type FilterContextType = {
  filters: FilterState;
  appliedFilters: FilterState;
  setFilters: (filters: Partial<FilterState>) => void;
  clearFilter: (key: keyof FilterState) => void;
  applyFilters: () => void;
  resetFilters: () => void;
};

const FilterContext = createContext<FilterContextType | undefined>(undefined);

export function FilterProvider({ children }: { children: ReactNode }) {
  const query = useVehicleQuery();
  const [filters, setFiltersState] = useState<FilterState>({
    maxDeposit: query.state.maxDeposit,
    minPrice: query.state.minPrice,
    maxPrice: query.state.maxPrice,
    mileageAllowance: query.state.lowMileageLimit,
    transmission: query.state.transmission,
    hasPromotion: query.state.hasPromotion,
    promotionType: query.state.promotionType,
  });

  // Track applied filters separately
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    maxDeposit: query.state.maxDeposit,
    minPrice: query.state.minPrice,
    maxPrice: query.state.maxPrice,
    mileageAllowance: query.state.lowMileageLimit,
    transmission: query.state.transmission,
    hasPromotion: query.state.hasPromotion,
    promotionType: query.state.promotionType,
  });

  const setFilters = (newFilters: Partial<FilterState>) => {
    setFiltersState((prev) => ({ ...prev, ...newFilters }));
  };

  const applyFilters = () => {
    query.updateMaxDeposit(filters.maxDeposit);
    query.updateMinPrice(filters.minPrice);
    query.updateMaxPrice(filters.maxPrice);
    query.updateLowMileageLimit(filters.mileageAllowance);
    query.updateTransmission(filters.transmission);
    query.updateHasPromotion(filters.hasPromotion);
    query.updatePromotionType(filters.promotionType);
    setAppliedFilters(filters);
  };

  const resetFilters = () => {
    const resetState = {
      maxDeposit: null,
      minPrice: null,
      maxPrice: null,
      mileageAllowance: null,
      transmission: null,
      hasPromotion: null,
      promotionType: null,
    };
    setFiltersState(resetState);
    setAppliedFilters(resetState);
    query.updateMaxDeposit(null);
    query.updateMinPrice(null);
    query.updateMaxPrice(null);
    query.updateLowMileageLimit(null);
    query.updateTransmission(null);
    query.updateHasPromotion(null);
    query.updatePromotionType(null);
  };

  const clearFilter = (key: keyof FilterState) => {
    setFiltersState((prev) => ({ ...prev, [key]: null }));
    setAppliedFilters((prev) => ({ ...prev, [key]: null }));

    // Update URL params
    switch (key) {
      case "maxDeposit":
        query.updateMaxDeposit(null);
        break;
      case "minPrice":
      case "maxPrice":
        query.updateMinPrice(null);
        query.updateMaxPrice(null);
        break;
      case "mileageAllowance":
        query.updateLowMileageLimit(null);
        break;
      case "transmission":
        query.updateTransmission(null);
        break;
      case "hasPromotion":
        query.updateHasPromotion(null);
        break;
      case "promotionType":
        query.updatePromotionType(null);
        break;
    }
  };

  return (
    <FilterContext.Provider
      value={{
        filters,
        appliedFilters,
        setFilters,
        clearFilter,
        applyFilters,
        resetFilters,
      }}
    >
      {children}
    </FilterContext.Provider>
  );
}

export const useFilters = () => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error("useFilters must be used within a FilterProvider");
  }
  return context;
};
