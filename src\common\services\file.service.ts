import { apiClient } from "@/common/lib/api-client";

export interface FileUploadResponse {
    fileName: string;
    fileUrl: string;
}

export interface BatchUploadResponse {
    successful: FileUploadResponse[];
    failed: {
        file: string;
        error: string;
    }[];
}

export default class FileService {
    async upload(file: File) {
        const formData = new FormData();
        formData.append("file", file);
        return apiClient.post<FileUploadResponse>("/files/upload", formData);
    }

    async uploadMultiple(files: File[]): Promise<BatchUploadResponse> {
        const results: BatchUploadResponse = {
            successful: [],
            failed: []
        };

        // Process files sequentially
        for (const file of files) {
            try {
                const response = await this.upload(file);
                results.successful.push(response.data);
            } catch (error) {
                results.failed.push({
                    file: file.name,
                    error: error instanceof Error ? error.message : 'Upload failed'
                });
            }
        }

        return results;
    }

    async compress(file: File, maxWidth = 1920, maxHeight = 1080, quality = 0.8): Promise<File> {
        // Only compress if it's an image
        if (!file.type.startsWith('image/')) {
            return file;
        }

        return new Promise((resolve, reject) => {
            const img = new Image();
            const reader = new FileReader();

            // Handle errors
            reader.onerror = () => reject(new Error('Failed to read file'));
            img.onerror = () => reject(new Error('Failed to load image'));

            reader.onload = (event: ProgressEvent<FileReader>) => {
                if (!event.target?.result) {
                    reject(new Error('Failed to read file'));
                    return;
                }
                img.src = event.target.result as string;
            };

            img.onload = () => {
                let { width, height } = img;

                // Calculate new dimensions while maintaining aspect ratio
                if (width > maxWidth || height > maxHeight) {
                    const aspectRatio = width / height;

                    if (width > height) {
                        width = maxWidth;
                        height = Math.round(width / aspectRatio);
                    } else {
                        height = maxHeight;
                        width = Math.round(height * aspectRatio);
                    }
                }

                // Create canvas and context
                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');

                if (!ctx) {
                    reject(new Error('Failed to get canvas context'));
                    return;
                }

                // Enable image smoothing for better quality
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                // Draw image to canvas
                ctx.drawImage(img, 0, 0, width, height);

                // Convert to blob
                canvas.toBlob(
                    (blob) => {
                        if (!blob) {
                            reject(new Error('Failed to create blob'));
                            return;
                        }

                        // Preserve original file name but ensure jpeg extension
                        const fileName = file.name.replace(/\.[^/.]+$/, '') + '.jpg';
                        resolve(new File([blob], fileName, {
                            type: 'image/jpeg',
                            lastModified: Date.now()
                        }));
                    },
                    'image/jpeg',
                    quality
                );
            };

            reader.readAsDataURL(file);
        });
    }

    async compressAndUpload(
        file: File,
        maxWidth = 1920,
        maxHeight = 1080,
        quality = 0.8
    ): Promise<FileUploadResponse> {
        try {
            const compressedFile = await this.compress(file, maxWidth, maxHeight, quality);
            const response = await this.upload(compressedFile);
            return response.data;
        } catch (error) {
            throw new Error(error instanceof Error ? error.message : 'Failed to process file');
        }
    }

    async compressAndUploadMultiple(
        files: File[],
        maxWidth = 1920,
        maxHeight = 1080,
        quality = 0.8
    ): Promise<BatchUploadResponse> {
        const results: BatchUploadResponse = {
            successful: [],
            failed: []
        };

        // Process files sequentially
        for (const file of files) {
            try {
                // Compress first
                const compressedFile = await this.compress(file, maxWidth, maxHeight, quality);
                // Then upload
                const response = await this.upload(compressedFile);
                results.successful.push(response.data);
            } catch (error) {
                results.failed.push({
                    file: file.name,
                    error: error instanceof Error ? error.message : 'Processing failed'
                });
            }
        }

        return results;
    }
}

export const fileService = new FileService();
