import brands, { BrandConfig } from '@/common/config/brands';

/**
 * Get the current brand configuration based on environment variable
 * This function can be used in both client and server contexts
 */
export function getCurrentBrandConfig(): BrandConfig {
    const currentBrand = process.env.NEXT_PUBLIC_BRAND_NAME || 'karlink';
    return brands[currentBrand] || brands.karlink;
}

/**
 * Check if the current brand is Karlink
 * This checks both the environment variable and hostname (client-side only)
 */
export function isKarlinkBrand(): boolean {
    const brandConfig = getCurrentBrandConfig();
    const isKarlinkFromConfig = brandConfig.brandName === 'karlink';

    // Additional hostname check for client-side
    if (typeof window !== 'undefined') {
        return isKarlinkFromConfig || window.location.hostname.includes('karlink');
    }

    return isKarlinkFromConfig;
}

/**
 * Get a specific brand configuration by name
 */
export function getBrandConfigByName(brandName: string): BrandConfig | undefined {
    return brands[brandName];
}

/**
 * Format the copyright text by replacing the {year} placeholder
 */
export function formatCopyright(copyright: string): string {
    return copyright.replace('{year}', new Date().getFullYear().toString());
}

/**
 * Get the WhatsApp link for a phone number with optional pre-filled message
 */
export function getWhatsAppLink(phone: string, message?: string): string {
    const cleanPhone = phone.replace(/\+|\s/g, '');
    const baseUrl = `https://wa.me/${cleanPhone}`;
    
    if (message) {
        const encodedMessage = encodeURIComponent(message);
        return `${baseUrl}?text=${encodedMessage}`;
    }
    
    return baseUrl;
}

/**
 * Get the email link for an email address
 */
export function getEmailLink(email: string): string {
    return `mailto:${email}`;
}
