import { vehicleBookingService } from "@/common/services/vehicle-booking.service";
import BookingInvoice from "@/app/bookings/[id]/components/booking-invoice";
import { Vehicle, VehicleBooking } from "@/common/models";
import { Card, CardContent } from "@/common/components/ui/card";
import Image from "next/image";
import Rating from "@/app/components/rating";
import { getVehicleFeatures } from "@/common/lib/vehicle-utils";
import { Separator } from "@/common/components/ui/separator";
import { formatDateWithTime } from "@/common/lib/date-utils";
import VehicleService from "@/common/services/vehicle.service";
import CancelBookingDialog from "@/app/bookings/components/cancel-booking-dialog";
import { isBefore } from "date-fns";

interface Props {
  params: Promise<{
    id: string;
  }>;
}

async function ViewBookingPage({ params }: Props) {
  const { id } = await params;
  const booking = await vehicleBookingService.getBookingById(id);
  const vehicle = await new VehicleService().getVehicleById(booking.vehicle.id);
  const canCancel =
    booking.status === "BOOKED" &&
    isBefore(new Date(), new Date(booking.start));
  return (
    <div className="container grid gap-10 pt-28 pb-20">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Booking Details</h2>
        {canCancel && <CancelBookingDialog booking={booking} />}
      </div>
      <VehicleDetailsCard vehicle={vehicle} booking={booking} />
      <BookingInvoice booking={booking} />
    </div>
  );
}

export default ViewBookingPage;

function VehicleDetailsCard({
  vehicle,
  booking,
}: {
  vehicle: Vehicle;
  booking: VehicleBooking;
}) {
  const rate = vehicle.vehicleRates.find((rate) => rate.weekDay === "MTF");
  const vehicleFeatures = getVehicleFeatures(vehicle);
  return (
    <Card className="p-5 text-sm">
      <CardContent className="flex h-full w-full flex-col gap-8 p-0 lg:flex-row lg:gap-12">
        <div className="flex h-full flex-col gap-6 lg:flex-row">
          <Image
            className="h-full w-full rounded-lg object-cover lg:w-52"
            width={100}
            height={100}
            src={vehicle.mainPhoto}
            alt={`image of ${vehicle.color} ${vehicle.name} ${vehicle.model}`}
          />
          <div className="flex flex-col gap-1">
            <span className="text-xl font-bold lg:text-base">
              {vehicle.name} {vehicle.model}
            </span>
            <span>${rate?.rate}/Day</span>
            <Rating rating={vehicle.rating} />
          </div>
        </div>

        <Separator
          orientation="vertical"
          className="bg-primary/5 hidden h-full lg:block"
        />
        <Separator
          orientation="horizontal"
          className="bg-primary/5 block lg:hidden"
        />

        <div className="grid grid-cols-2 gap-3 lg:grid-cols-3">
          {vehicleFeatures.map((feature) => (
            <div key={feature.title} className="flex gap-2">
              <feature.icon className="bg-secondary/15 text-secondary size-9 rounded-md p-1" />
              <div className="flex flex-col">
                <span className="font-medium">{feature.title}</span>
                <span className="text-xs">{feature.description}</span>
              </div>
            </div>
          ))}
        </div>

        <Separator
          orientation="vertical"
          className="bg-primary/5 hidden h-full lg:block"
        />
        <Separator
          orientation="horizontal"
          className="bg-primary/5 block lg:hidden"
        />

        <div className="grid grid-cols-[auto_1fr] gap-x-2">
          <div className="z-10 col-[1/2] row-[1/2] -mt-1 h-2 w-2 self-center justify-self-center rounded-full bg-orange-400 md:mt-1"></div>
          <div className="col-[2/-1] row-[1/2] text-base font-bold lg:-mb-8">
            Pick Up
          </div>
          <div className="col-[2/-1] row-[2/3] mb-5">
            {formatDateWithTime(booking.start)} - {vehicle.location.city}
          </div>

          <div className="bg-primary/10 after:bg-primary/10 relative col-[1/2] row-[2/3] w-[1px] justify-self-center after:absolute after:-top-2 after:-z-10 after:block after:h-[140%] after:w-full"></div>

          <div className="col-[1/2] row-[3/4] -mt-1 h-2 w-2 self-center justify-self-center rounded-full bg-blue-400 md:mt-1"></div>
          <div className="col-[2/-1] row-[3/4] text-base font-bold lg:-mb-8">
            Drop Off
          </div>
          <div className="col-[2/-1] row-[4/5]">
            {formatDateWithTime(booking.end)} - {vehicle.location.city}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
