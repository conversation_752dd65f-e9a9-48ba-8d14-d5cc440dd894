"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Eye } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/common/components/ui/button";
import { Badge } from "@/common/components/ui/badge";
import { VehicleBooking } from "@/common/models";
import Link from "next/link";

const statusOptions = {
  RESERVED: {
    label: "Reserved",
    color: "bg-blue-100 text-blue-800 hover:bg-blue-200",
  },
  BOOKED: {
    label: "Booked",
    color: "bg-green-100 text-green-800 hover:bg-green-200",
  },
  WAITINGAUTH: {
    label: "In Progress",
    color: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
  },
  COMPLETE: {
    label: "Complete",
    color: "bg-gray-100 text-gray-800 hover:bg-gray-200",
  },
  CANCELLED: {
    label: "Cancelled",
    color: "bg-red-100 text-red-800 hover:bg-red-200",
  },
} as const;

export const bookingsColumns: ColumnDef<VehicleBooking>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2"
        >
          ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="font-medium">#{row.getValue("id")}</div>;
    },
  },
  {
    accessorKey: "start",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2"
        >
          Start Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const startDate = row.getValue("start") as string;
      try {
        return (
          <div>
            <div className="font-medium">
              {format(new Date(startDate), "MMM dd, yyyy")}
            </div>
            <div className="text-muted-foreground text-sm">
              {format(new Date(startDate), "HH:mm")}
            </div>
          </div>
        );
      } catch {
        return (
          <div className="text-muted-foreground text-sm">Invalid date</div>
        );
      }
    },
  },
  {
    accessorKey: "end",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2"
        >
          End Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const endDate = row.getValue("end") as string;
      try {
        return (
          <div>
            <div className="font-medium">
              {format(new Date(endDate), "MMM dd, yyyy")}
            </div>
            <div className="text-muted-foreground text-sm">
              {format(new Date(endDate), "HH:mm")}
            </div>
          </div>
        );
      } catch {
        return (
          <div className="text-muted-foreground text-sm">Invalid date</div>
        );
      }
    },
  },
  {
    accessorKey: "vehicle",
    header: "Vehicle",
    cell: ({ row }) => {
      const vehicle = row.getValue("vehicle") as VehicleBooking["vehicle"];
      return (
        <div>
          <div className="font-medium">{vehicle.name}</div>
          <div className="text-muted-foreground text-sm">{vehicle.model}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 px-2"
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const status = row.getValue("status") as keyof typeof statusOptions;
      return (
        <Badge
          variant="secondary"
          className={
            statusOptions[status].color ||
            "bg-gray-100 text-gray-800 hover:bg-gray-200"
          }
        >
          {statusOptions[status].label}
        </Badge>
      );
    },
  },
  {
    id: "actions",
    enableHiding: false,
    header: "Actions",
    cell: ({ row }) => {
      const booking = row.original;

      return (
        <Button asChild variant="ghost">
          <Link href={`/bookings/${booking.id}`}>
            <Eye className="h-4 w-4" />
          </Link>
        </Button>
      );
    },
  },
];
