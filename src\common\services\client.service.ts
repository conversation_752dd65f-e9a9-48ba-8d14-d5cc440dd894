import { apiClient } from "@/common/lib/api-client";
import { Client, ClientDocument } from "@/common/models";

export default class ClientService {
    async findById(id: number | null | undefined) {
        const response = await apiClient.get<Client>(`/client/${id}`);
        return response.data;
    }

    async addDocuments(id: number, documents: Partial<ClientDocument>[]) {
        return apiClient.post<void>("/client/docs", {
            id,
            clientDocs: documents
        })
    }

    async update(data: Partial<Client>) {
        return apiClient.put<void>(`/client`, data);
    }
}

export const clientService = new ClientService();
