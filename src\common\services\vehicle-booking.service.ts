// TODO: Better error handling in axios services

import {
    VehicleBooking,
    PaginatedResponse,
    RatingItem,
    Rating,
} from "@/common/models";
import { apiClient } from "@/common/lib/api-client";
import { Nullish, PaymentMethod } from "@/common/types/utils";

export interface CreateBookingInput {
    firstname: string;
    surname: string;
    email: string;
    phone: string;
    start: string;
    end: string;
    vehicleId: number;
    clientId?: number;
    gatewayType: PaymentMethod;
    vehicleAddonsIds: number[];
    promoCode: string | null;
    hirerCurrency?: string; // Hirer's preferred currency for payment display
}

export interface CreateRatingInput {
    comment: string;
    ratingItems: RatingItem[];
}

export default class VehicleBookingService {
    private endPoint = "/vehicle-booking";

    private makeUrl(path: string) {
        return `${this.endPoint}/${path}`;
    }
    async getBookingsForClient(clientId: string, page = 0, size = 10, agencyId?: string | null, statuses?: string[], searchCriteria?: string | null) {
        const url = this.makeUrl(`client/${clientId}/${page}/${size}?agencyId=${agencyId ?? ""}&statuses=${statuses?.join(",") ?? ""}&searchCriteria=${searchCriteria ?? ""}`);
        const response =
            await apiClient.get<PaginatedResponse<VehicleBooking>>(url);

        return response.data;
    }

    async getBookingById(id: string) {
        const url = this.makeUrl(`/${id}`);
        const response = await apiClient.get<VehicleBooking>(url);

        return response.data;
    }

    async createBooking(booking: CreateBookingInput) {
        const url = this.makeUrl("");
        const response = await apiClient.post<VehicleBooking>(url, booking);

        return response.data;
    }

    async createBookingQuote(data: Partial<VehicleBooking>) {
        const url = this.makeUrl("/quote");
        const response = await apiClient.post<VehicleBooking>(url, data);
        return response.data;
    }

    async rate(id: number, data: CreateRatingInput) {
        const url = this.makeUrl(`/rating/${id}`);
        const response = await apiClient.post<Rating>(url, data);
        return response.data;
    }

    async cancelBooking(id: number, byAgency: boolean, reason: Nullish<string>) {
        const url = this.makeUrl(`/cancel/${id}`);
        const response = await apiClient.post<VehicleBooking>(url, {}, {
            params: {
                byAgency,
                reason
            }
        });
        return response.data;
    }
}

export const vehicleBookingService = new VehicleBookingService();
