"use client";

import Link from "next/link";
import { Button } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { forgotPasswordAction } from "@/app/(auth)/actions";
import { useActionState } from "react";
import Loader from "@/common/components/loader";

export default function ForgotPasswordPage() {
  const [state, action, isPending] = useActionState(
    forgotPasswordAction,
    undefined,
  );

  return (
    <div className="mx-auto w-full max-w-sm space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Reset Password</h1>
        <p className="text-gray-500">Enter your email to reset your password</p>
      </div>
      <form action={action} className="grid gap-4">
        {!state?.error && (
          <p className="text-center text-sm text-green-500">{state?.message}</p>
        )}
        <div className="grid gap-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" name="email" required />
          {state?.error && (
            <p className="text-destructive text-sm">{state?.message}</p>
          )}
        </div>
        <div className="flex justify-between">
          <Button type="button" variant="outline">
            <Link href="/login">Cancel</Link>
          </Button>
          <Button type="submit" disabled={isPending}>
            {isPending && <Loader />}
            {isPending ? "Reseting..." : "Reset"}
          </Button>
        </div>
      </form>
    </div>
  );
}
