import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/common/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/common/components/ui/popover";
import { Check, ChevronsUpDown, MapPin } from "lucide-react";
import { useState } from "react";
import { Button } from "@/common/components/ui/button";
import { cn } from "@/common/lib/shadcn-utils";
import { Location } from "@/common/models";
import { useLocations } from "@/common/hooks/use-locations";
import { useDebounce } from "@uidotdev/usehooks";

interface LocationComboboxProps {
  value: Location | null;
  onChange: (location: Location) => void;
  placeholder: string;
}

export function LocationCombobox({
  value,
  onChange,
  placeholder = "Search locations...",
}: LocationComboboxProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 400);
  const { data, isLoading } = useLocations(debouncedSearch);
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          role="combobox"
          className="bg-background border-input hover:bg-accent text-foreground relative h-12 w-full justify-between border py-3 text-left"
        >
          <MapPin
            className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-400"
            size={18}
          />
          <span className="pl-6">
            {value ? `${value.name?(value.name+', '):''}${value.city}, ${value.country}` : placeholder}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="max-h-(--radix-popover-content-available-height) w-(--radix-popover-trigger-width) p-0">
        <Command>
          <CommandInput
            placeholder="Search locations..."
            value={search}
            onValueChange={setSearch}
          />
          {isLoading ? (
            <CommandEmpty className="py-6 text-center text-sm">
              Searching...
            </CommandEmpty>
          ) : data?.length === 0 ? (
            <CommandEmpty className="text-muted-foreground py-6 text-center text-sm">
              No locations found
            </CommandEmpty>
          ) : null}
          <CommandGroup>
            {data?.map((result) => (
              <CommandItem
                key={result.id}
                value={`${result.name?(result.name+', '):''}${result.city}, ${result.country}`}
                onSelect={() => {
                  onChange(result);
                  setOpen(false);
                  setSearch("");
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value?.id === result.id ? "opacity-100" : "opacity-0",
                  )}
                />
                <div className="flex flex-col"> 
                  <span>{result.name?(result.name+', '):''}{result.city}</span>
                  <span className="text-muted-foreground text-xs">
                    {result.country}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
