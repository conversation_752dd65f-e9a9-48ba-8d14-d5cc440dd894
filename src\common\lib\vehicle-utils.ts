import { Vehicle, VehicleRate } from "@/common//models";
import {
    Armchair,
    BadgeDollarSign,
    Clock,
    Cog,
    DoorClosed,
    Fan,
    Fuel,
    Gauge,
    Vault,
} from "lucide-react";

export function getVehicleFeatures(vehicle: Vehicle | null | undefined) {
    return [
        {
            title: "Engine Size",
            description: vehicle?.engineSize ?? "Not set",
            icon: Vault,
        },
        {
            title: "Air Conditioning",
            description: vehicle?.airConditioning ? "Yes" : "No",
            icon: Fan,
        },
        {
            title: "Doors",
            description: vehicle?.doors ?? 0,
            icon: DoorClosed,
        },
        {
            title: "Seats",
            description: vehicle?.seats ?? 0,
            icon: Armchair,
        },
        {
            title: "Fuel Type",
            description: vehicle?.fuelType ?? "Not set",
            icon: Fuel,
        },
        {
            title: "Transmission Type",
            description: vehicle?.transmissionType ?? "Not set",
            icon: Cog,
        },
        {
            title: "Mileage Allowance",
            description: `${vehicle?.maxDailyMileage === 0 ? "Unlimited" : vehicle?.maxDailyMileage + "KM"}/day`,
            icon: Gauge,
        },
        {
            title: "Excess Mileage Rate",
            description: "$" + vehicle?.excessMileageRate.toFixed(2) + "/KM",
            icon: BadgeDollarSign,
        },
        {
            title: "Minimum Hire Days",
            description: vehicle?.minHireDays ? `${vehicle.minHireDays} ${vehicle.minHireDays === 1 ? 'day' : 'days'}` : "Not set",
            icon: Clock,
        },
    ];
}

export function getDescription(vehicle: Vehicle) {
    return vehicle.description;
}


export function removeRateDuplicates(rates: VehicleRate[]): VehicleRate[] {
    const uniqueRates: VehicleRate[] = [];

    rates.forEach((rate) => {
        if (
            !uniqueRates.some(
                (uniqueRate) =>
                    uniqueRate.weekDay === rate.weekDay && uniqueRate.rate === rate.rate,
            )
        ) {
            uniqueRates.push(rate);
        }
    });

    return uniqueRates;
}
