import { Card, CardContent } from "@/common/components/ui/card";

export default function DocumentUploadSkeleton() {
  return (
    <div className="container pt-28 pb-20">
      <div className="mx-auto max-w-4xl px-4 sm:px-6">
        {/* Header skeleton */}
        <div className="mb-6 text-center sm:mb-8">
          <div className="mx-auto mb-2 h-8 w-48 animate-pulse rounded-md bg-gray-200"></div>
          <div className="mx-auto h-4 w-64 animate-pulse rounded-md bg-gray-200"></div>
        </div>
        {/* Main content skeleton */}
        <Card>
          <CardContent>
            <div className="h-[400px] animate-pulse rounded-md bg-gray-100"></div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
