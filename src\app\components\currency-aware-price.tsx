"use client";

import React, { useState, useEffect } from "react";
import { formatCurrency, formatUserCurrency } from "@/common/lib/currency-utils";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import { useCurrencyConversion } from "@/common/hooks/use-currency-conversion";
import { Loader2, Info } from "lucide-react";
import { Button } from "@/common/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/common/components/ui/tooltip";

interface CurrencyAwarePriceProps {
  amount: number;
  originalCurrency: string;
  className?: string;
  showOriginal?: boolean;
  showConversionInfo?: boolean;
  size?: "sm" | "md" | "lg";
}

export function CurrencyAwarePrice({
  amount,
  originalCurrency,
  className = "",
  showOriginal = true,
  showConversionInfo = true,
  size = "md",
}: CurrencyAwarePriceProps) {
  const { preferredCurrency } = useUserCurrency();
  const { convertAmount, isLoading, error } = useCurrencyConversion();
  const [convertedAmount, setConvertedAmount] = useState<number | null>(null);
  const [exchangeRate, setExchangeRate] = useState<number | null>(null);

  // If currencies are the same, no conversion needed
  const needsConversion = originalCurrency !== preferredCurrency;

  useEffect(() => {
    if (needsConversion) {
      convertAmount(amount, originalCurrency)
        .then((result) => {
          setConvertedAmount(result.convertedAmount);
          setExchangeRate(result.exchangeRate);
        })
        .catch((err) => {
          console.error("Currency conversion failed:", err);
          // Fallback to original amount
          setConvertedAmount(amount);
        });
    }
  }, [amount, originalCurrency, preferredCurrency, needsConversion, convertAmount]);

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "text-sm";
      case "lg":
        return "text-lg font-semibold";
      default:
        return "text-base";
    }
  };

  // If no conversion needed, show original price
  if (!needsConversion) {
    return (
      <span className={`${getSizeClasses()} ${className}`}>
        {formatCurrency(amount, originalCurrency)}
      </span>
    );
  }

  // If loading conversion
  if (isLoading || convertedAmount === null) {
    return (
      <span className={`flex items-center gap-1 ${getSizeClasses()} ${className}`}>
        <Loader2 className="h-3 w-3 animate-spin" />
        {formatCurrency(amount, originalCurrency)}
      </span>
    );
  }

  // If conversion failed, show original with error indicator
  if (error) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className={`flex items-center gap-1 ${getSizeClasses()} ${className}`}>
              {formatCurrency(amount, originalCurrency)}
              <Info className="h-3 w-3 text-amber-500" />
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>Currency conversion unavailable</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Show converted price with optional original
  return (
    <div className={`${className}`}>
      <span className={`${getSizeClasses()}`}>
        {formatCurrency(convertedAmount, preferredCurrency)}
      </span>
      
      {showOriginal && (
        <div className="text-xs text-muted-foreground">
          {formatCurrency(amount, originalCurrency)}
        </div>
      )}
      
      {showConversionInfo && exchangeRate && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="inline h-3 w-3 ml-1 text-muted-foreground cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Exchange rate: 1 {originalCurrency} = {exchangeRate.toFixed(4)} {preferredCurrency}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}

interface CurrencyAwareTotalProps {
  items: Array<{
    label: string;
    amount: number;
    originalCurrency: string;
  }>;
  className?: string;
}

export function CurrencyAwareTotal({ items, className = "" }: CurrencyAwareTotalProps) {
  const { preferredCurrency } = useUserCurrency();
  const { convertAmount, isLoading } = useCurrencyConversion();
  const [convertedItems, setConvertedItems] = useState<Array<{
    label: string;
    originalAmount: number;
    convertedAmount: number;
    originalCurrency: string;
  }> | null>(null);

  useEffect(() => {
    const convertAllItems = async () => {
      try {
        const converted = await Promise.all(
          items.map(async (item) => {
            if (item.originalCurrency === preferredCurrency) {
              return {
                label: item.label,
                originalAmount: item.amount,
                convertedAmount: item.amount,
                originalCurrency: item.originalCurrency,
              };
            }

            const result = await convertAmount(item.amount, item.originalCurrency);
            return {
              label: item.label,
              originalAmount: item.amount,
              convertedAmount: result.convertedAmount,
              originalCurrency: item.originalCurrency,
            };
          })
        );
        setConvertedItems(converted);
      } catch (error) {
        console.error("Failed to convert currency totals:", error);
        // Fallback to original amounts
        setConvertedItems(
          items.map((item) => ({
            label: item.label,
            originalAmount: item.amount,
            convertedAmount: item.amount,
            originalCurrency: item.originalCurrency,
          }))
        );
      }
    };

    convertAllItems();
  }, [items, preferredCurrency, convertAmount]);

  if (isLoading || !convertedItems) {
    return (
      <div className={`space-y-2 ${className}`}>
        {items.map((item, index) => (
          <div key={index} className="flex justify-between items-center">
            <span>{item.label}</span>
            <span className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              {formatCurrency(item.amount, item.originalCurrency)}
            </span>
          </div>
        ))}
      </div>
    );
  }

  const total = convertedItems.reduce((sum, item) => sum + item.convertedAmount, 0);

  return (
    <div className={`space-y-2 ${className}`}>
      {convertedItems.map((item, index) => (
        <div key={index} className="flex justify-between items-center">
          <span>{item.label}</span>
          <div className="text-right">
            <div>{formatCurrency(item.convertedAmount, preferredCurrency)}</div>
            {item.originalCurrency !== preferredCurrency && (
              <div className="text-xs text-muted-foreground">
                {formatCurrency(item.originalAmount, item.originalCurrency)}
              </div>
            )}
          </div>
        </div>
      ))}
      
      <div className="border-t pt-2 flex justify-between items-center font-semibold">
        <span>Total</span>
        <span>{formatCurrency(total, preferredCurrency)}</span>
      </div>
    </div>
  );
}

interface CurrencyToggleProps {
  amount: number;
  originalCurrency: string;
  className?: string;
}

export function CurrencyToggle({ amount, originalCurrency, className = "" }: CurrencyToggleProps) {
  const { preferredCurrency } = useUserCurrency();
  const [showOriginal, setShowOriginal] = useState(false);

  // If currencies are the same, no toggle needed
  if (originalCurrency === preferredCurrency) {
    return (
      <span className={className}>
        {formatCurrency(amount, originalCurrency)}
      </span>
    );
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      className={`h-auto p-0 font-normal ${className}`}
      onClick={() => setShowOriginal(!showOriginal)}
    >
      {showOriginal ? (
        <span>{formatCurrency(amount, originalCurrency)}</span>
      ) : (
        <CurrencyAwarePrice
          amount={amount}
          originalCurrency={originalCurrency}
          showOriginal={false}
          showConversionInfo={false}
        />
      )}
    </Button>
  );
}
