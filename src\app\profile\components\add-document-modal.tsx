"use client";

import type React from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/common/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import {
  Upload,
  FileIcon,
  X,
  AlertCircle,
  CameraIcon,
  SwitchCameraIcon,
} from "lucide-react";
import { useState, useRef, type ChangeEvent, useEffect } from "react";
import { Camera } from "react-camera-pro";
import { cn } from "@/common/lib/shadcn-utils";
import { Client, ClientDocument } from "@/common/models";
import { useAddClientDocuments } from "@/common/hooks/use-add-client-documents";
import { useUploadDocument } from "@/common/hooks/use-upload-document";
import { useQueryClient } from "@tanstack/react-query";
import { DOCUMENT_TYPE_MAP } from "@/common/lib/document-utils";
import { toast } from "sonner";
import Loader from "@/common/components/loader";

interface AddDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  client: Client;
}

type DocumentFormData = {
  documentType: string;
  file: File | null;
};

type FormErrors = {
  documentType?: string;
  file?: string;
};

// All available document types
const ALL_DOCUMENT_TYPES = Object.entries(DOCUMENT_TYPE_MAP).map(
  ([value, label]) => ({
    value,
    label,
  }),
);

export function AddDocumentModal({
  open,
  onOpenChange,
  client,
}: AddDocumentDialogProps) {
  const [formData, setFormData] = useState<DocumentFormData>({
    documentType: "",
    file: null,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isDragging, setIsDragging] = useState(false);
  const [isCameraMode, setIsCameraMode] = useState(false);
  const [numberOfCameras, setNumberOfCameras] = useState(0);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const { mutateAsync: uploadDocument, isPending: isUploading } =
    useUploadDocument();
  const { mutate: addClientDocuments, isPending: isSubmitting } =
    useAddClientDocuments();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraRef = useRef<{
    takePhoto: () => string;
    switchCamera: () => void;
  } | null>(null);

  // Check if a document type is already uploaded
  const isDocumentTypeUploaded = (documentType: string) => {
    if (!client || !client.clientDocs) return false;
    return client.clientDocs.some((doc) => doc.name === documentType);
  };

  // Reset form when modal is closed
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const resetForm = () => {
    setFormData({
      documentType: "",
      file: null,
    });
    setErrors({});
    setIsDragging(false);
    setIsCameraMode(false);
    setCapturedImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Camera functions
  const takePhoto = () => {
    if (cameraRef.current) {
      const photo = cameraRef.current.takePhoto();
      setCapturedImage(photo);

      // Convert base64 to File object
      fetch(photo)
        .then((res) => res.blob())
        .then((blob) => {
          const timestamp = new Date().getTime();
          const file = new File([blob], `document-${timestamp}.jpg`, {
            type: "image/jpeg",
          });
          setFormData((prev) => ({ ...prev, file }));
          // Clear file error if it exists
          if (errors.file) {
            setErrors((prev) => ({ ...prev, file: undefined }));
          }
        });
    }
  };

  const switchCamera = () => {
    if (cameraRef.current && numberOfCameras > 1) {
      cameraRef.current.switchCamera();
    }
  };

  const retakePhoto = () => {
    setCapturedImage(null);
    setFormData((prev) => ({ ...prev, file: null }));
    setErrors((prev) => ({ ...prev, file: undefined }));
  };

  const toggleCameraMode = () => {
    setIsCameraMode(!isCameraMode);
    setFormData((prev) => ({ ...prev, file: null }));
    setCapturedImage(null);
    setErrors((prev) => ({ ...prev, file: undefined }));
  };

  // Fix the validateForm function to properly validate document type
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Check document type
    if (!formData.documentType || formData.documentType.trim() === "") {
      newErrors.documentType = "Please select a document type";
      isValid = false;
    }

    // Check file
    if (!formData.file) {
      newErrors.file = "Please upload a document";
      isValid = false;
    } else {
      // Validate file type
      if (
        !["image/jpeg", "image/jpg", "image/png"].includes(formData.file.type)
      ) {
        newErrors.file = "Only JPEG, JPG, and PNG formats are allowed";
        isValid = false;
      }

      // Validate file size
      if (formData.file.size > 1024 * 1024) {
        newErrors.file = "File size must be less than 1MB";
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Validate file type
      if (
        !["image/jpeg", "image/jpg", "image/png"].includes(selectedFile.type)
      ) {
        setErrors({
          ...errors,
          file: "Only JPEG, JPG, and PNG formats are allowed",
        });
        return;
      }

      // Validate file size
      if (selectedFile.size > 1024 * 1024) {
        setErrors({
          ...errors,
          file: "File size must be less than 1MB",
        });
        return;
      }

      setFormData({
        ...formData,
        file: selectedFile,
      });

      // Clear file error if it exists
      if (errors.file) {
        setErrors({
          ...errors,
          file: undefined,
        });
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];

      // Validate file type
      if (
        !["image/jpeg", "image/jpg", "image/png"].includes(droppedFile.type)
      ) {
        setErrors({
          ...errors,
          file: "Only JPEG, JPG, and PNG formats are allowed",
        });
        return;
      }

      // Validate file size
      if (droppedFile.size > 1024 * 1024) {
        setErrors({
          ...errors,
          file: "File size must be less than 1MB",
        });
        return;
      }

      setFormData({
        ...formData,
        file: droppedFile,
      });

      // Clear file error if it exists
      if (errors.file) {
        setErrors({
          ...errors,
          file: undefined,
        });
      }
    }
  };

  const openFileSelector = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removeFile = () => {
    setFormData({
      ...formData,
      file: null,
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDocumentTypeChange = (value: string) => {
    setFormData({
      ...formData,
      documentType: value,
    });

    // Clear document type error if it exists
    if (errors.documentType) {
      setErrors({
        ...errors,
        documentType: undefined,
      });
    }
  };

  // Update the handleSubmit function to call validateForm
  const handleSubmit = async () => {
    if (validateForm()) {
      const { documentType, file } = formData;
      const { fileUrl } = await uploadDocument(file!);

      addClientDocuments(
        {
          clientId: client.id,
          documents: [
            {
              name: documentType as ClientDocument["name"],
              url: fileUrl,
            },
          ],
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["client", client?.id] });
            toast("Document added successfully", {
              duration: 3000,
            });
            onOpenChange(false);
          },
          onError: (error) => {
            toast("Failed to add document", {
              description:
                error instanceof Error ? error.message : "Unknown error",
              duration: 3000,
            });
          },
        },
      );
    }
  };

  // Check if all document types are already uploaded
  const allDocumentsUploaded = ALL_DOCUMENT_TYPES.every((type) =>
    isDocumentTypeUploaded(type.value),
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            Add new document
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <label htmlFor="documentType" className="text-sm font-medium">
              Document Type
            </label>
            <Select
              value={formData.documentType}
              onValueChange={handleDocumentTypeChange}
            >
              <SelectTrigger
                className={cn(
                  "w-full",
                  errors.documentType && "border-red-500",
                )}
              >
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {allDocumentsUploaded ? (
                  <div className="p-2 text-center text-sm text-gray-500">
                    All document types already uploaded
                  </div>
                ) : (
                  ALL_DOCUMENT_TYPES.map((type) => {
                    const isUploaded = isDocumentTypeUploaded(type.value);
                    return (
                      <SelectItem
                        key={type.value}
                        value={type.value}
                        disabled={isUploaded}
                        className={
                          isUploaded ? "text-gray-400 line-through" : ""
                        }
                      >
                        {type.label}{" "}
                        {isUploaded && (
                          <span className="font-normal text-gray-400">
                            (already uploaded)
                          </span>
                        )}
                      </SelectItem>
                    );
                  })
                )}
              </SelectContent>
            </Select>
            {errors.documentType && (
              <p className="mt-1 flex items-center text-sm text-red-500">
                <AlertCircle className="mr-1 h-3 w-3" />
                {errors.documentType}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">
                Select Document (Images only)
              </label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={toggleCameraMode}
                  className="text-xs sm:text-sm"
                >
                  <CameraIcon className="mr-1 h-3 w-3" />
                  {isCameraMode ? "Upload File" : "Use Camera"}
                </Button>
              </div>
            </div>

            {isCameraMode ? (
              // Camera Mode
              <div className="space-y-4">
                {!capturedImage ? (
                  <div className="relative">
                    <div className="relative z-0 aspect-video w-full overflow-hidden rounded-md border-2 border-gray-300">
                      <Camera
                        ref={cameraRef}
                        aspectRatio="cover"
                        numberOfCamerasCallback={setNumberOfCameras}
                        errorMessages={{
                          noCameraAccessible:
                            "No camera accessible. Please check camera permissions or use file upload.",
                          permissionDenied:
                            "Camera permission denied. Please allow camera access and try again.",
                          switchCamera:
                            "Cannot switch camera - only one camera available.",
                          canvas: "Canvas not supported by your browser.",
                        }}
                      />
                    </div>
                    <div className="relative z-10 mt-3 flex justify-center gap-2">
                      <Button
                        type="button"
                        onClick={takePhoto}
                        className="flex items-center gap-2"
                      >
                        <CameraIcon className="h-4 w-4" />
                        Take Photo
                      </Button>
                      {numberOfCameras > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={switchCamera}
                          className="flex items-center gap-2"
                        >
                          <SwitchCameraIcon className="h-4 w-4" />
                          Switch Camera
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  // Preview captured photo
                  <div className="space-y-3">
                    <div className="relative">
                      <Image
                        src={capturedImage}
                        alt="Captured document"
                        width={400}
                        height={256}
                        className="max-h-64 w-full rounded-md border-2 border-gray-300 object-contain"
                      />
                    </div>
                    <div className="flex justify-center gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={retakePhoto}
                        className="flex items-center gap-2"
                      >
                        <CameraIcon className="h-4 w-4" />
                        Retake Photo
                      </Button>
                    </div>
                    {formData.file && (
                      <div className="text-center">
                        <p className="text-sm font-medium">
                          {formData.file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(formData.file.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              // File Upload Mode
              <div
                className={cn(
                  "cursor-pointer rounded-md border-2 border-dashed p-8 text-center transition-colors",
                  isDragging
                    ? "border-blue-500 bg-blue-50"
                    : errors.file
                      ? "border-red-500"
                      : "focus-within:border-blue-500 hover:border-blue-400",
                  formData.file ? "border-blue-500" : "",
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={openFileSelector}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".jpg,.jpeg,.png"
                />

                {!formData.file ? (
                  <div className="flex flex-col items-center">
                    <Upload className="mb-2 h-10 w-10 text-gray-400" />
                    <p className="text-sm font-medium">
                      Choose a file or drag it here
                    </p>
                    <p className="mt-1 text-xs text-gray-500">
                      Supported formats: JPEG, PNG (max 1MB)
                    </p>
                  </div>
                ) : (
                  <div className="relative flex flex-col items-center">
                    <button
                      className="absolute -top-4 -right-4 rounded-full bg-white p-1 shadow-xs hover:bg-gray-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile();
                      }}
                    >
                      <X className="h-4 w-4 text-gray-500" />
                    </button>
                    <FileIcon className="mb-2 h-10 w-10 text-blue-500" />
                    <p className="text-sm font-medium break-all">
                      {formData.file.name}
                    </p>
                    <p className="mt-1 text-xs text-gray-500">
                      {(formData.file.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                )}
              </div>
            )}
            {errors.file && (
              <p className="mt-1 flex items-center text-sm text-red-500">
                <AlertCircle className="mr-1 h-3 w-3" />
                {errors.file}
              </p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting || isUploading}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting || isUploading}>
            {isSubmitting || isUploading ? <Loader /> : null}
            {isSubmitting || isUploading ? "Uploading..." : "Add"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
