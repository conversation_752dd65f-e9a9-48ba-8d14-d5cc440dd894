"use client";
import React, { useEffect, useRef } from 'react';
import NProgress from 'nprogress';
import { usePathname, useSearchParams } from 'next/navigation';
import { useIsFetching, useIsMutating } from '@tanstack/react-query';

export interface LoadingIndicatorProps {
  /**
   * The color of the progress bar.
   * @default "#0A2FFF"
   */
  color?: string;
  /**
   * The height of the progress bar.
   * @default "2px"
   */
  height?: string;
  /**
   * Whether to show the spinner.
   * @default true
   */
  showSpinner?: boolean;
  /**
   * The delay before starting the progress bar.
   * @default 0
   */
  delay?: number;
  /**
   * Whether to show the bar on shallow routes.
   * @default false
   */
  showOnShallow?: boolean;
  /**
   * Custom NProgress options.
   */
  options?: Partial<NProgress.NProgressOptions>;
}

export const LoadingIndicator = React.memo<LoadingIndicatorProps>(
  ({ 
    color = '#0A2FFF', 
    height = '2px', 
    showSpinner = true, 
    delay = 0, 
    showOnShallow = false,
    options 
  }) => {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const isFetching = useIsFetching();
    const isMutating = useIsMutating();
    const navigationTimerRef = useRef<NodeJS.Timeout>();
    const reactQueryActiveRef = useRef(false);
    const navigatingRef = useRef(false);

    // Combine fetching and mutating states
    const isReactQueryActive = isFetching > 0 || isMutating > 0;

    // Configure NProgress
    useEffect(() => {
      NProgress.configure({
        showSpinner,
        ...options
      });
    }, [showSpinner, options]);

    // Handle navigation state changes
    useEffect(() => {
      // Clear any existing navigation timer
      if (navigationTimerRef.current) {
        clearTimeout(navigationTimerRef.current);
      }

      // Start navigation progress
      navigatingRef.current = true;
      if (!reactQueryActiveRef.current) {
        navigationTimerRef.current = setTimeout(() => {
          NProgress.start();
        }, delay);
      }

      // Clean up navigation after a delay
      const cleanupTimer = setTimeout(() => {
        navigatingRef.current = false;
        if (!reactQueryActiveRef.current) {
          NProgress.done();
        }
      }, 500);

      return () => {
        clearTimeout(cleanupTimer);
        if (navigationTimerRef.current) {
          clearTimeout(navigationTimerRef.current);
        }
      };
    }, [pathname, searchParams, delay]);

    // Handle React Query state changes (both fetching and mutating)
    useEffect(() => {
      const wasActive = reactQueryActiveRef.current;
      reactQueryActiveRef.current = isReactQueryActive;

      if (isReactQueryActive && !wasActive) {
        // Start React Query progress
        if (!navigatingRef.current) {
          setTimeout(() => {
            NProgress.start();
          }, delay);
        }
      } else if (!isReactQueryActive && wasActive) {
        // Stop React Query progress
        if (!navigatingRef.current) {
          NProgress.done();
        }
      }
    }, [isReactQueryActive, delay]);

    // Handle anchor clicks for navigation
    useEffect(() => {
      const handleAnchorClick = (event: MouseEvent) => {
        const anchorElement = event.currentTarget as HTMLAnchorElement;

        // Skip anchors with target attribute but different than _self
        if (anchorElement.target !== '_self' && anchorElement.target?.trim() !== '')
          return;

        // Skip anchors with download attribute
        if (anchorElement.hasAttribute('download')) return;

        // target url without hash removed
        const targetUrl = new URL(anchorElement.href);
        const currentUrl = new URL(location.href);

        // check if search params changed
        const hasSearchParams = targetUrl?.searchParams?.toString() !== currentUrl?.searchParams?.toString();
        const paramsChanged = hasSearchParams && targetUrl?.search !== currentUrl?.search;
        const isSameUrl = targetUrl?.pathname === currentUrl?.pathname && !paramsChanged;

        // detect ctrl/cmd option/alt shift click
        if (event.metaKey || event.ctrlKey || event.shiftKey || event.altKey) return;

        if (showOnShallow && isSameUrl) return;
        if (isSameUrl) return;

        // Start progress for navigation
        navigatingRef.current = true;
        navigationTimerRef.current = setTimeout(() => {
          NProgress.start();
        }, delay);
      };

      const handleMutation: MutationCallback = () => {
        const anchorElements = document.querySelectorAll('a');
        const validAnchorELes = Array.from(anchorElements).filter((anchor) => {
          if (anchor.href.startsWith('tel:+') || anchor.href.startsWith('mailto:')) return false;
          if (anchor.target !== '_self' && anchor.target?.trim() !== '') return false;
          return anchor.href;
        });
        validAnchorELes.forEach((anchor) => anchor.addEventListener('click', handleAnchorClick));
      };

      const mutationObserver = new MutationObserver(handleMutation);
      mutationObserver.observe(document, { childList: true, subtree: true });

      return () => {
        mutationObserver.disconnect();
      };
    }, [delay, showOnShallow]);

    const styles = (
      <style>
        {`
          #nprogress {
            pointer-events: none;
          }
          
          #nprogress .bar {
            background: ${color};
          
            position: fixed;
            z-index: 1031;
            top: 0;
            left: 0;
          
            width: 100%;
            height: ${height};
          }
          
          /* Fancy blur effect */
          #nprogress .peg {
            display: block;
            position: absolute;
            right: 0px;
            width: 100px;
            height: 100%;
            box-shadow: 0 0 10px ${color}, 0 0 5px ${color};
            opacity: 1.0;
          
            -webkit-transform: rotate(3deg) translate(0px, -4px);
                -ms-transform: rotate(3deg) translate(0px, -4px);
                    transform: rotate(3deg) translate(0px, -4px);
          }
          
          /* Spinner */
          #nprogress .spinner {
            display: ${showSpinner ? 'block' : 'none'};
            position: fixed;
            z-index: 1031;
            top: 15px;
            right: 15px;
          }
          
          #nprogress .spinner-icon {
            width: 18px;
            height: 18px;
            box-sizing: border-box;
          
            border: solid 2px transparent;
            border-top-color: ${color};
            border-left-color: ${color};
            border-radius: 50%;
          
            -webkit-animation: nprogress-spinner 400ms linear infinite;
                    animation: nprogress-spinner 400ms linear infinite;
          }
          
          .nprogress-custom-parent {
            overflow: hidden;
            position: relative;
          }
          
          .nprogress-custom-parent #nprogress .spinner,
          .nprogress-custom-parent #nprogress .bar {
            position: absolute;
          }
          
          @-webkit-keyframes nprogress-spinner {
            0%   { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
          }
          @keyframes nprogress-spinner {
            0%   { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    );

    return styles;
  }
);

LoadingIndicator.displayName = 'LoadingIndicator';