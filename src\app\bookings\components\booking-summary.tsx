import Image from "next/image";
import dayjs from "dayjs";
import { VehicleBooking } from "@/common/models";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";

interface Props {
  booking: VehicleBooking;
}

// TODO: total amount and subtotal amount need to be separated once we start including add-ons
// TODO: centralize date functions either use dayjs or date-fns
// TODO: find a better way to structure this using all the values provided (e.g. invoiceItemResult)
export default function BookingSummary({ booking }: Props) {
  const brandConfig = getCurrentBrandConfig();
  const invoice = booking.invoices[0]!;
  const { vehicle } = booking;
  let dailyRate = 0;
  invoice.invoiceItemResult.forEach((item) => {
    if (item) {
      dailyRate = item.rate;
      return;
    }
  });
  const daysHired = calculateDaysHired(booking.start, booking.end);
  const totalAmount = daysHired * dailyRate;

  return (
    <div className="bg-white p-8">
      <div className="mb-8 flex flex-col items-start justify-between space-y-4 sm:flex-row sm:space-y-0">
        <div className="flex flex-col gap-4">
          <Image
            src={brandConfig.logo.nav}
            alt={`${brandConfig.brandName} Logo`}
            className="h-12 w-auto"
            width={120}
            height={48}
          />
          <div className="text-sm">
            <p className="font-semibold">MyKarLink PVT LTD</p>
            <p>7 San Fernando</p>
            <p>Corner 5th & Fife Ave</p>
            <p>Harare, Zimbabwe</p>
            <p>+263 856 562 555</p>
            <p><EMAIL></p>
          </div>
        </div>
      </div>

      <div className="mb-8 flex flex-col items-start gap-8 sm:flex-row sm:justify-between">
        <div className="grid gap-0.5 text-sm">
          <h3 className="font-semibold">Bill to:</h3>
          <div>
            <p>
              {booking.firstname} {booking.surname}
            </p>
            <p>{booking.phone}</p>
            <p>{booking.email}</p>
          </div>
        </div>
        <div className="grid gap-0.5 text-sm">
          <h3 className="font-semibold">Vehicle Hired</h3>
          <div>
            <p>
              {vehicle.name} {vehicle.model}
            </p>
            <p>{vehicle.regno}</p>
          </div>
        </div>
        <div className="grid gap-0.5 text-sm">
          <div className="grid grid-cols-2 gap-8">
            <span className="font-semibold">Date issued:</span>
            <span>{formatCustomDateTime(booking.start)}</span>
          </div>
          <div className="grid grid-cols-2 gap-8">
            <span className="font-semibold">Due date:</span>
            <span>{formatCustomDateTime(booking.end)}</span>
          </div>
          <div className="grid grid-cols-2 gap-8">
            <span className="font-semibold">Terms:</span>
            <span>Due on Receipt</span>
          </div>
        </div>
      </div>

      <div className="mb-8 overflow-x-auto">
        <table className="w-full bg-[#f0f5ff]">
          <thead>
            <tr className="bg-primary hover:bg-primary text-white">
              <th className="font-semi p-3 text-left text-white">
                Vehicle Description
              </th>
              <th className="font-semi p-3 text-right text-white">
                Daily Rate
              </th>
              <th className="font-semi p-3 text-right text-white">
                Days Hired
              </th>
              <th className="font-semi p-3 text-right text-white">
                Total Charge
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="">
              <td className="border-b-primary/30 border-b p-3 font-medium">
                {`${booking.vehicle.color.charAt(0).toUpperCase() + booking.vehicle.color.slice(1)} ${booking.vehicle.model} ${booking.vehicle.regno.toUpperCase()}`}
              </td>
              <td className="border-b-primary/30 border-b p-3 text-right">
                ${dailyRate.toFixed(2)}
              </td>
              <td className="border-b-primary/30 border-b p-3 text-right">
                x{daysHired}
              </td>
              <td className="border-b-primary/30 border-b p-3 text-right">
                ${(totalAmount || 0).toFixed(2)}
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr className="bg-white hover:bg-white hover:*:bg-white">
              <td colSpan={2} className="p-3" />
              <td className="p-3 text-right font-medium">Sub Total</td>
              <td className="p-3 text-right">
                ${(totalAmount || 0).toFixed(2)}
              </td>
            </tr>
            <tr className="bg-white hover:bg-white hover:*:bg-white">
              <td colSpan={2} className="p-3" />
              <td className="p-3 text-right font-medium">Discount</td>
              <td className="p-3 text-right">
                ${(invoice.discount || 0).toFixed(2)}
              </td>
            </tr>
            <tr className="bg-white hover:bg-white">
              <td colSpan={2} className="p-3" />
              <td className="bg-primary p-3 text-right font-medium text-white">
                Balance Due
              </td>
              <td className="bg-primary p-3 text-right font-bold text-white">
                ${(totalAmount || 0).toFixed(2)}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div className="mb-8 text-sm">
        <span className="font-medium">Deposit: </span>
        <span className="font-medium text-emerald-600">
          ${(vehicle.depositAmt ?? 0).toFixed(2)}
        </span>
        <span>
          {" "}
          cash is required for deposit at vehicle pick up/collection, the amount
          to be returned at vehicle drop off after inspection.
        </span>
      </div>
    </div>
  );
}

function formatCustomDateTime(date: string) {
  return dayjs(date).format("DD MMMM YYYY");
}

// TODO: Fix this calculation. It's wrong.
function calculateDaysHired(start: string, end: string) {
  const startDate = dayjs(start);
  const endDate = dayjs(end);

  return endDate.diff(startDate, "days");
}
