import React from "react";

import VehicleInfoCard from "@/app/vehicles/[id]/components/vehicle-info-card";
import VehicleBookingForm from "@/app/vehicles/[id]/components/book-vehicle-form";
import VehicleReviewsCard from "@/app/vehicles/[id]/components/vehicle-reviews";

import VehicleService from "@/common/services/vehicle.service";
import VehicleSearchForm from "@/app/components/vehicle-search-form";
import VehicleRatesTable from "@/app/vehicles/[id]/components/vehicle-rates-table";

interface Props {
  params: Promise<{
    id: string;
  }>;
}

const service = new VehicleService();

async function ViewVehicle({ params }: Props) {
  const { id } = await params;

  const vehicle = await service.getVehicleById(Number(id));

  return (
    <>
      <section className="container grid gap-8 py-24 lg:py-28">
        <VehicleSearchForm variant="compact" />
        <div className="space-y-8 lg:grid lg:grid-cols-2 lg:gap-10 lg:space-y-0">
          <div className="lg:row-span-2 lg:*:h-full">
            <VehicleInfoCard vehicle={vehicle} />
          </div>
          <div className="space-y-8 lg:col-[2/-1] lg:row-span-2 lg:h-full">
            <VehicleBookingForm vehicle={vehicle} />
            <VehicleRatesTable rates={vehicle.vehicleRates} />
          </div>
          <div className="lg:col-span-full">
            <VehicleReviewsCard reviews={vehicle.ratings} />
          </div>
        </div>
      </section>
    </>
  );
}

export default ViewVehicle;
