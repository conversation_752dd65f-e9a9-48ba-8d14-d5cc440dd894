import type { MetadataRoute } from 'next'
import { getCurrentBrandConfig } from '@/common/config/brands/utils'

export default function robots(): MetadataRoute.Robots {
    const brandConfig = getCurrentBrandConfig()

    // Only generate robots.txt for Karlink brand
    if (brandConfig.name.toLowerCase() !== 'karlink') {
        return {
            rules: {
                userAgent: '*',
                disallow: '/',
            },
        }
    }

    return {
        rules: [
            {
                userAgent: '*',
                allow: '/',
                disallow: [
                    '/api/',
                    '/admin/',
                    '/dashboard/',
                    '/bookings/',
                    '/profile/',
                    '/documents/upload/',
                    '/vehicles/create/',
                ],
            },
            {
                userAgent: 'Googlebot',
                allow: '/',
                disallow: [
                    '/api/',
                    '/admin/',
                    '/dashboard/',
                    '/bookings/',
                    '/profile/',
                    '/documents/upload/',
                    '/vehicles/create/',
                ],
                crawlDelay: 1,
            },
        ],
        sitemap: 'https://mykarlink.com/sitemap.xml',
        host: 'https://mykarlink.com',
    }
}
