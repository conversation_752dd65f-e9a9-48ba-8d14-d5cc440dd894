import { Card, CardContent } from "@/common/components/ui/card";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/common/components/ui/table";
import { usdFormatter } from "@/common/lib/currency-utils";
import { VehicleRate } from "@/common/models";

export default function VehicleRatesTable({ rates }: { rates: VehicleRate[] }) {
  return (
    <Card>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow className="bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground">
              <TableHead className="text-primary-foreground">Day</TableHead>
              <TableHead className="text-primary-foreground">Rate</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rates.map((rate) => (
              <TableRow key={rate.id}>
                <TableHead>{humanizeWeekDay(rate.weekDay)}</TableHead>
                <TableHead>{usdFormatter.format(rate.rate)}</TableHead>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

function humanizeWeekDay(weekDay: string) {
  switch (weekDay) {
    case "SAT":
      return "Saturday";
    case "SUN":
      return "Sunday";
    case "MTF":
      return "Monday to Friday";
    default:
      return weekDay;
  }
}
