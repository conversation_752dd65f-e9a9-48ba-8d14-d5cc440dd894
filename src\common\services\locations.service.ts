import { Location } from "@/common/models";
import { apiClient } from "@/common/lib/api-client";

export default class LocationsService {
  async search(query: string): Promise<Location[]> {
    if (!query || query.length < 2) return [];

    const response = await apiClient.get<Location[]>(
      `/locations/search/${query}`,
    );
    return response.data;
  }

  async getById(id: number) {
    const response = await apiClient.get<Location>(`/location/${id}`);
    return response.data;
  }
}
