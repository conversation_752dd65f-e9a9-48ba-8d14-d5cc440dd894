import { definitions, operations } from "@/common/types/api/schema";

/**
 * Extracts the paginated response type for an entity
 * @template T - The entity type name (must be a key in definitions)
 * @example
 * // Gets the paginated response type for Vehicle entity
 * type VehiclePage = PageOf<"Vehicle">; // Page«Vehicle» with content: Vehicle[], etc.
 *
 * // Access paginated properties
 * function renderPagination(page: PageOf<"Vehicle">) {
 *   return {
 *     items: page.content,           // Vehicle[]
 *     totalItems: page.totalElements, // number
 *     currentPage: page.number,      // number
 *     totalPages: page.totalPages    // number
 *   };
 * }
 */
export type PageOf<T extends keyof definitions> =
    T extends keyof definitions ?
    T extends `${infer U}` ?
    `Page«${U}»` extends keyof definitions ?
    definitions[`Page«${U}»`] :
    never :
    never :
    never;

/**
 * Extracts the status enum type from an entity
 * @template T - The entity type name (must be a key in definitions)
 * @example
 * // Gets the status type for Client entity
 * type ClientStatus = StatusOf<"Client">; // "ACTIVE" | "INACTIVE" | etc.
 */
export type StatusOf<T extends keyof definitions> =
    definitions[T] extends { status?: infer S } ? S : never;

/**
 * Finds all entity types that contain a specific property
 * @template P - The property name to search for
 * @example
 * // Gets all entities that have a "status" property
 * type EntitiesWithStatus = EntitiesWithProperty<"status">; // "Client" | "Vehicle" | etc.
 */
export type EntitiesWithProperty<P extends string> = {
    [K in keyof definitions]: P extends keyof definitions[K] ? K : never
}[keyof definitions];

/**
 * Extracts the return type of an API operation
 * @template T - The operation name (must be a key in operations)
 * @example
 * // Gets the return type of the getClientById operation
 * type ClientResponse = OperationReturnType<"getClientById">; // Client
 */
export type OperationReturnType<T extends keyof operations> =
    operations[T] extends { responses: { 200: { content: { "application/json": infer R } } } }
    ? R
    : operations[T] extends { responses: { 200: { schema: infer S } } }
    ? S
    : operations[T] extends { responses: { 200: infer O } }
    ? O
    : never;

/**
 * Extracts all DTO types from the schema (entities ending with "Dto")
 * @example
 * // Gets all DTO types
 * type AllDTOs = DTOTypes; // "ClientDto" | "VehicleDto" | etc.
 */
export type DTOTypes = {
    [K in keyof definitions]: K extends `${string}Dto` ? K : never
}[keyof definitions];

/**
 * Filters entity types to only those with a numeric ID field
 * @template T - The entity type name (must be a key in definitions)
 * @example
 * // Ensures Client has an ID field and returns its type
 * type ClientWithId = EntityWithId<"Client">; // Client with id: number
 */
export type EntityWithId<T extends keyof definitions> =
    definitions[T] extends { id?: number } ? definitions[T] : never;

/**
 * Creates a type suitable for entity mutations by removing server-generated fields
 * @template T - The entity type name (must be a key in definitions)
 * @example
 * // Creates a type for creating or updating a Client
 * type MutableClient = MutableEntity<"Client">; // Omits id, timestamps, etc.
 */
export type MutableEntity<T extends keyof definitions> = Omit<
    definitions[T],
    'id' | 'createdDate' | 'lastModifiedDate' | 'deletedAt' | 'version'
>;

/**
 * Extracts all properties that are string enums from an entity
 * @template T - The entity type name (must be a key in definitions)
 * @template P - Optional specific property to extract (if omitted, returns all enum properties)
 * @example
 * // Gets all enum properties from Vehicle
 * type VehicleEnums = EnumPropertiesOf<"Vehicle">; // "status" | "fuelType" | "transmissionType" | "type"
 *
 * // Gets just the status enum from Vehicle
 * type VehicleStatus = EnumPropertiesOf<"Vehicle", "status">; // "AWAITING" | "EDITED" | "DISABLED" | "AVAILABLE" | "REJECTED"
 */
export type EnumPropertiesOf<
    T extends keyof definitions,
    P extends keyof definitions[T] = never
> = P extends never
    ? {
        [K in keyof definitions[T]]: definitions[T][K] extends string ?
        string extends definitions[T][K] ? never : K : never
    }[keyof definitions[T]]
    : definitions[T][P];
