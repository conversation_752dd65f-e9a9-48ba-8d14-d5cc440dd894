import { auth } from "@/app/(auth)/auth";
import { cookies } from "next/headers";

import { NextRequest, NextResponse } from "next/server";

const protectedRoutes: { [key: string]: string } = {
    "/bookings": "My Bookings",
    "/profile": "Profile",
};

export default async function middleware(req: NextRequest) {
    const path = req.nextUrl.pathname;
    const isProtectedRoute = Object.keys(protectedRoutes).includes(path);

    // Decrypt the session from the cookie
    const session = await auth();

    if (isProtectedRoute && !session?.user) {
        const flashMessage = {
            title: "Unauthorized",
            description: `You need to sign in to access ${protectedRoutes[path]} page.`,
            variant: "warning",
        };
        const cookieStore = await cookies();
        cookieStore.set("flash-message", JSON.stringify(flashMessage), {
            path: "/",
            httpOnly: false,
            sameSite: "strict",
            maxAge: 60 * 5,
        });
        return NextResponse.redirect(new URL("/login", req.nextUrl));
    }

    return NextResponse.next();
}

// Routes Middleware should not run on
export const config = {
    matcher: ["/((?!api|_next/static|_next/image|.*\\.png$).*)"],
};
