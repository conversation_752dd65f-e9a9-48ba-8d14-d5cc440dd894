import { definitions } from "@/common/types/api/schema";

// Core File Types
export type File = definitions["File"];
export type FileDto = definitions["FileDto"];

// File Upload/Download Operations
export type FileUploadResponse = definitions["FileDto"];
export type FileDownloadResponse = definitions["Resource"];

// Document Types
export type VehicleDocument = definitions["VehicleDocument"];
export type ClientDocs = definitions["ClientDocs"];

// Document Status Types
export type VehicleDocumentStatus = NonNullable<VehicleDocument["status"]>;
export type ClientDocsName = NonNullable<ClientDocs["name"]>;
export type ClientDocsStatus = NonNullable<ClientDocs["status"]>;

// Photo Types
export type VehiclePhoto = definitions["VehiclePhoto"];
export type VehiclePhotoDto = definitions["VehiclePhotoDto"];
export type VehicleBookingPhoto = definitions["VehicleBookingPhoto"];

// Resource Types
export type Resource = definitions["Resource"];
export type InputStream = definitions["InputStream"];
export type URI = definitions["URI"];
export type URL = definitions["URL"];