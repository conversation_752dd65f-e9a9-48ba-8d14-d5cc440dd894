import Image from "next/image";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>eader,
} from "@/common/components/ui/card";
import { Button } from "@/common/components/ui/button";
import {
  ArrowRight,
  Armchair,
  Cog,
  Fuel,
  Tag,
  Gauge,
  Calendar,
  Gift,
} from "lucide-react";
import { Promotion, Vehicle } from "@/common/models";
import Link from "next/link";
import { capitalizeDescription } from "@/app/helpers";
import placeholderVehicleImg from "@/common/assets/images/placeholder-vehicle-image.webp";
import Rating from "@/app/components/rating";
import { removeRateDuplicates } from "@/common/lib/vehicle-utils";

import {
  Carousel,
  CarouselIndicator,
  CarouselMainContainer,
  CarouselNext,
  CarouselPrevious,
  CarouselThumbsContainer,
  SliderMainItem,
} from "@/common/components/ui/multi-carousel";
import { cn } from "@/common/lib/shadcn-utils";
import { Badge } from "@/common/components/ui/badge";
import { usd<PERSON><PERSON><PERSON>er, formatUserCurrency, getUserPreferredCurrency } from "@/common/lib/currency-utils";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import { usePriceFormatter } from "@/common/hooks/use-currency-conversion";
import { Separator } from "@/common/components/ui/separator";
import { isKarlinkBrand } from "@/common/config/brands/utils";
import DepositBadge from "@/app/components/deposit-badge";
import { CurrencyIndicator } from "@/common/components/currency-indicator";
import { Fragment } from "react";

interface Props {
  vehicle: Vehicle;
  queryParams?: string;
}

export default function VehicleCard({ vehicle, queryParams }: Props) {
  const { preferredCurrency } = useUserCurrency();
  const { formatPriceSync } = usePriceFormatter();
  const mainRate = removeRateDuplicates(vehicle.vehicleRates).find(
    (rate) => rate.weekDay === "MTF",
  );
  const agencyLogo = vehicle.agency?.logo;

  const getMainImage = () => {
    if (vehicle.mainPhoto) {
      return vehicle.mainPhoto;
    }

    if (vehicle.photos.length === 1) {
      return vehicle.photos[0].url;
    }

    return placeholderVehicleImg;
  };

  const hasPromotion = vehicle.promotions && vehicle.promotions.length > 0;
  const mainPromotion = hasPromotion ? vehicle.promotions[0] : null;
  const promoType = mainPromotion?.promotionType;

  const originalRate = mainRate?.rate;
  const discountedRate = mainPromotion?.discount
    ? (originalRate ?? 0) - ((originalRate ?? 0) * mainPromotion.discount) / 100
    : null;

  // Helper function to format price in user's preferred currency
  const formatPrice = (amount: number) => {
    // Get the agency's base currency (default to USD if not set)
    const agencyBaseCurrency = vehicle.agency?.baseCurrency || "USD";

    // Use the price formatter with currency conversion
    return formatPriceSync(amount, agencyBaseCurrency);
  };

  return (
    <div className="relative p-1 text-sm">
      {hasPromotion && (
        <div className="absolute -top-1 -left-1 z-20">
          <div
            className={cn(
              "flex items-center justify-center rounded-full p-2",
              getCircleColor(mainPromotion?.promotionType),
            )}
          >
            {getPromoIcon(mainPromotion?.promotionType)}
          </div>
        </div>
      )}

      {/* Promotion badge */}
      {hasPromotion && (
        <div className="absolute top-4 right-4 z-10">
          <Link href={`/vehicles/${vehicle.id}?${queryParams}`}>
            {getPromoBadge(vehicle)}
          </Link>
        </div>
      )}
      <Card
        className={cn("overflow-hidden pt-0", {
          [`border-2 ${getPromoBorderColor(mainPromotion?.promotionType)}`]:
            hasPromotion,
        })}
      >
        <CardHeader className="flex h-72 cursor-pointer p-0">
          {vehicle.photos.length > 0 ? (
            <VehicleImagesCarousel
              vehicle={vehicle}
              queryParams={queryParams}
            />
          ) : (
            <Link
              className="h-full w-full"
              href={`/vehicles/${vehicle.id}?${queryParams}`}
            >
              <Image
                src={getMainImage()}
                alt={`image of ${vehicle.color} ${vehicle.name} ${vehicle.model}`}
                className="h-full w-full object-cover"
                height={288}
                width={400}
              />
            </Link>
          )}
        </CardHeader>
        <CardContent className="grid gap-4 px-6">
          <div>
            <div className="grid gap-1">
              <div className="flex justify-between">
                <div>
                  <Rating rating={vehicle.rating} />
                  <h3 className="text-xl font-bold text-zinc-800">
                    {vehicle.name + " " + vehicle.model}
                  </h3>
                </div>

                {isKarlinkBrand() && agencyLogo ? (
                  <Image
                    className="self-start"
                    width={80}
                    height={80}
                    src={agencyLogo}
                    alt="logo of the vehicle provider"
                  />
                ) : null}
              </div>
              <div className="flex flex-wrap items-center justify-between gap-2">
                {promoType === "DISCOUNT" && mainRate ? (
                  <Fragment>
                    <div className="space-x-2">
                      <span>
                        <span className="text-lg font-bold text-orange-600">
                          {formatPrice(discountedRate ?? 0)}/Day
                        </span>
                      </span>
                      <span className="text-muted-foreground text-sm line-through">
                        {formatPrice(originalRate ?? 0)}
                      </span>
                    </div>
                    <CurrencyIndicator
                      originalCurrency={vehicle.agency?.baseCurrency || "USD"}
                      variant="icon"
                      className="ml-1"
                    />
                  </Fragment>
                ) : (
                  <div className="flex items-center gap-1">
                    <span className="text-lg font-semibold">
                      {formatPrice(mainRate?.rate ?? 0)}/Day
                    </span>
                    <CurrencyIndicator
                      originalCurrency={vehicle.agency?.baseCurrency || "USD"}
                      variant="icon"
                    />
                  </div>
                )}
                <DepositBadge
                  depositAmount={vehicle.depositAmt}
                  agencyBaseCurrency={vehicle.agency?.baseCurrency || "USD"}
                />
              </div>
            </div>
          </div>

          <Separator orientation="horizontal" className="bg-foreground/5" />

          <div className="grid grid-cols-2 grid-rows-2 gap-4">
            <span className="flex items-center gap-2">
              <span className="bg-secondary/15 flex items-center justify-center rounded-md p-1">
                <Armchair className="text-secondary" />
              </span>
              <span className="capitalize">{vehicle.seats || 0} Seats</span>
            </span>

            <span className="flex items-center gap-2">
              <span className="bg-secondary/15 flex items-center justify-center rounded-md p-1">
                <Cog className="text-secondary" />
              </span>
              <span className="capitalize">
                {capitalizeDescription(vehicle.transmissionType || "Not set")}
              </span>
            </span>

            <span className="flex items-center gap-2">
              <span className="bg-secondary/15 flex items-center justify-center rounded-md p-1">
                <Gauge className="text-secondary" />
              </span>
              <span className="capitalize">
                {vehicle?.maxDailyMileage && vehicle.maxDailyMileage !== 0
                  ? `${vehicle.maxDailyMileage}KM/Day`
                  : "Unlimited mileage"}
              </span>
            </span>

            <span className="flex items-center gap-2">
              <span className="bg-secondary/15 flex items-center justify-center rounded-md p-1">
                <Fuel className="text-secondary" />
              </span>
              <span className="capitalize">
                {capitalizeDescription(vehicle.fuelType || "Not set")}
              </span>
            </span>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full text-sm" asChild>
            <Link href={`/vehicles/${vehicle.id}?${queryParams}`}>
              <span>Book Now</span>
              <ArrowRight />
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

function VehicleImagesCarousel({
  vehicle,
  queryParams,
}: {
  vehicle: Vehicle;
  queryParams: string | undefined;
}) {
  const images = vehicle.photos || [];
  return (
    <Carousel className="group">
      {images.length > 1 && (
        <>
          <CarouselNext className="invisible right-2 opacity-0 transition-all duration-200 group-hover:visible group-hover:opacity-100" />
          <CarouselPrevious className="invisible left-2 opacity-0 transition-all duration-200 group-hover:visible group-hover:opacity-100" />
        </>
      )}
      <div className="relative">
        <CarouselMainContainer className="h-72">
          {images.map((image) => (
            <SliderMainItem key={image.id} className="bg-transparent p-0">
              <Link href={`/vehicles/${vehicle.id}?${queryParams}`}>
                <Image
                  src={image.url}
                  alt="vehicle preview image"
                  className="h-full w-full object-cover"
                  height={288}
                  width={400}
                />
              </Link>
            </SliderMainItem>
          ))}
        </CarouselMainContainer>
        {images.length > 1 && (
          <div className="invisible absolute bottom-2 left-1/2 -translate-x-1/2 opacity-0 transition-all duration-200 group-hover:visible group-hover:opacity-100">
            <CarouselThumbsContainer className="gap-x-1">
              {Array.from({ length: images.length }).map((_, index) => (
                <CarouselIndicator key={index} index={index} />
              ))}
            </CarouselThumbsContainer>
          </div>
        )}
      </div>
    </Carousel>
  );
}

// Get promo badge color and icon based on type
const getPromoBadge = (vehicle: Vehicle) => {
  const hasPromotion = vehicle.promotions && vehicle.promotions.length > 0;
  const mainPromotion = hasPromotion ? vehicle.promotions[0] : null;
  const promoType = mainPromotion?.promotionType;

  // Get discount percentage for the badge
  const discountPercentage = mainPromotion?.discount || 0;

  if (!hasPromotion) return null;

  switch (promoType) {
    case "DISCOUNT":
      return (
        <Badge
          variant="secondary"
          className="bg-orange-500 text-white hover:bg-orange-600"
        >
          {discountPercentage}% OFF
        </Badge>
      );
    case "EXTRA_MILEAGE":
      return (
        <Badge
          variant="secondary"
          className="bg-green-500 text-white hover:bg-green-600"
        >
          +{mainPromotion?.extraMileage}KM/DAY
        </Badge>
      );
    case "EXTRA_DAYS":
      return (
        <Badge
          variant="secondary"
          className="bg-blue-500 text-white hover:bg-blue-600"
        >
          +{mainPromotion?.extraDays} FREE{" "}
          {mainPromotion?.extraDays === 1 ? "DAY" : "DAYS"}
        </Badge>
      );
    case "OTHER_AWARD":
      return (
        <Badge
          variant="secondary"
          className="bg-purple-500 text-white hover:bg-purple-600"
        >
          ON SPECIAL OFFER
        </Badge>
      );
    default:
      return null;
  }
};

// Get promo indicator icon
const getPromoIcon = (promoType: Promotion["promotionType"]) => {
  switch (promoType) {
    case "DISCOUNT":
      return <Tag className="h-5 w-5 text-white" />;
    case "EXTRA_MILEAGE":
      return <Gauge className="-mt-0.5 h-5 w-5 text-white" />;
    case "EXTRA_DAYS":
      return <Calendar className="-mt-0.5 h-5 w-5 text-white" />;
    case "OTHER_AWARD":
      return <Gift className="-mt-0.5 h-5 w-5 text-white" />;
    default:
      return <Tag className="h-5 w-5 text-white" />;
  }
};

// Get circle background color based on promotion type
const getCircleColor = (promoType: Promotion["promotionType"]) => {
  switch (promoType) {
    case "DISCOUNT":
      return "bg-orange-500";
    case "EXTRA_MILEAGE":
      return "bg-green-500";
    case "EXTRA_DAYS":
      return "bg-blue-500";
    case "OTHER_AWARD":
      return "bg-purple-500";
    default:
      return "bg-orange-500";
  }
};

// Get promo border color based on promotion type
const getPromoBorderColor = (promoType: Promotion["promotionType"]) => {
  switch (promoType) {
    case "DISCOUNT":
      return "border-orange-200";
    case "EXTRA_MILEAGE":
      return "border-green-200";
    case "EXTRA_DAYS":
      return "border-blue-200";
    case "OTHER_AWARD":
      return "border-purple-200";
    default:
      return "border-orange-200";
  }
};
