import { VehicleBooking } from "@/common/models";
import { Nullish, PaymentMethod } from "@/common/types/utils";
import { getAddonIds } from "@/common/lib/booking-utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { vehicleBookingService } from "@/common/services/vehicle-booking.service";

export default function useCreateBooking() {
    const queryClient = useQueryClient()
    return useMutation({
        mutationFn: ({ booking, gatewayType, promoCode }: { booking: Nullish<VehicleBooking>, gatewayType: PaymentMethod, promoCode: string | null }) => {
            if (!booking?.firstname || !booking?.surname || !booking?.email || !booking?.phone || !booking?.start || !booking?.end || !booking?.vehicle?.id) {
                throw new Error("Missing required booking information");
            }
            return vehicleBookingService.createBooking({
                firstname: booking.firstname,
                surname: booking.surname,
                email: booking.email,
                phone: booking.phone,
                start: booking.start,
                end: booking.end,
                vehicleId: booking.vehicle.id,
                clientId: booking.clientId,
                vehicleAddonsIds: getAddonIds(booking),
                gatewayType,
                promoCode
            })

        },
        onSuccess(data) {
            queryClient.setQueryData<VehicleBooking>(['reserved-booking'], data)
        },
    });
}
