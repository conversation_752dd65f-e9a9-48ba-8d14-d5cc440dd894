import { z } from "zod"

// Maximum file size: 1MB in bytes
export const MAX_FILE_SIZE = 1024 * 1024

// Allowed file types
export const ACCEPTED_IMAGE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"]

// Document type validation
export const documentTypeSchema = z.enum(["DRIVER", "PASSPORT", "ID", "PROOF_RESIDENCE"])

// File validation schema
export const fileSchema = z.object({
    name: z.string().min(1, "File name is required"),
    size: z.number().max(MAX_FILE_SIZE, "File size must be less than 1MB"),
    type: z.enum(["image/jpeg", "image/jpg", "image/png", "image/webp"], { message: "Only JPEG, JPG, PNG, and WEBP images are allowed" }),
})

// Document upload validation schema
export const documentUploadSchema = z.object({
    documentType: documentTypeSchema,
    file: fileSchema,
})

// Type for document upload form
export type DocumentUploadForm = z.infer<typeof documentUploadSchema>
