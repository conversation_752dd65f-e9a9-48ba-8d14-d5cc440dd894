"use client";
import { useFilters } from "./filter-context";
import { Label } from "@/common/components/ui/label";
import { TabsFilter } from "./tabs-filter";

const MILEAGE_OPTIONS = {
  any: null,
  "100": 100,
  "200": 200,
  "300": 300,
  "400": 400,
  "500": 500,
  unlimited: -1,
} as const;

export default function MileageLimitSelector() {
  const { filters, setFilters } = useFilters();

  const getCurrentValue = () => {
    switch (filters.mileageAllowance) {
      case 100:
        return "100";
      case 200:
        return "200";
      case 300:
        return "300";
      case 400:
        return "400";
      case 500:
        return "500";
      case -1:
        return "unlimited";
      default:
        return "any";
    }
  };

  return (
    <div className="space-y-3">
      <Label>Mileage Allowance</Label>
      <TabsFilter
        value={getCurrentValue()}
        onValueChange={(value) => {
          setFilters({
            mileageAllowance:
              MILEAGE_OPTIONS[value as keyof typeof MILEAGE_OPTIONS],
          });
        }}
        options={[
          { value: "any", label: "Any" },
          { value: "100", label: "100KM" },
          { value: "200", label: "200KM" },
          { value: "300", label: "300KM" },
          { value: "400", label: "400KM" },
          { value: "500", label: "500KM" },
          { value: "unlimited", label: "Unlimited" },
        ]}
      />
    </div>
  );
}
