import { definitions } from "@/common/types/api/schema";

// Main Client Types
export type Client = definitions["Client"];
export type ClientDto = definitions["ClientDto"];

// Client Status
export type ClientStatus = NonNullable<Client["status"]>;

// Client Documents
export type ClientDocs = definitions["ClientDocs"];
export type ClientDocsName = NonNullable<ClientDocs["name"]>;
export type ClientDocsStatus = NonNullable<ClientDocs["status"]>;

// Client Statistics
export type ClientStats = definitions["ClientStats"];

// Paginated Client Types
export type PageClientDto = definitions["Page«ClientDto»"];