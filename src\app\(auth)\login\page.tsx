"use client";

import { useActionState, useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Eye, EyeOff } from "lucide-react";

import { <PERSON><PERSON> } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { loginAction } from "@/app/(auth)/actions";
import Loader from "@/common/components/loader";
import { useProviderRedirect } from "@/common/hooks/use-provider-redirect";

export default function LoginPage() {
  const { redirectToProviderDashboard } = useProviderRedirect();
  const [state, action, isPending] = useActionState(loginAction, undefined);
  const [authMessage, setAuthMessage] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const { update } = useSession();
  const router = useRouter();

  useEffect(() => {
    // Check for stored message
    const message = sessionStorage.getItem("auth_message");
    if (message) {
      setAuthMessage(message);
      sessionStorage.removeItem("auth_message");
    }
  }, []);

  // Clear auth message when there's an error
  useEffect(() => {
    if (state && "success" in state && !state.success) {
      setAuthMessage(null);
    }
  }, [state]);

  // Wrap the action to clear auth message on form submission
  const wrappedAction = async (formData: FormData) => {
    setAuthMessage(null);
    return action(formData);
  };

  useEffect(() => {
    if (state && "success" in state && state.success) {
      update().then((session) => {
        if (session?.user?.userType === "AGENCY") {
          redirectToProviderDashboard(session);
        }
        if (session?.user?.userType === "CLIENT") {
          router.push("/");
        }
      });
    }
  }, [state]);

  return (
    <div className="mx-auto w-full max-w-sm space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Login</h1>
        <p className="text-gray-500">Enter your email and password to login</p>
      </div>
      <form className="grid gap-4" action={wrappedAction}>
        {authMessage && (
          <p className="text-center text-sm text-green-500">{authMessage}</p>
        )}
        {state && "success" in state && !state.success && (
          <p className="text-destructive text-center text-sm">
            Invalid email or password
          </p>
        )}
        <div className="grid gap-2">
          <Label htmlFor="username">Email</Label>
          <Input
            id="username"
            type="text"
            name="username"
            defaultValue={
              state && "formData" in state ? state.formData.username : ""
            }
          />
        </div>
        <div className="grid gap-2">
          <Label
            htmlFor="password"
            className="flex items-center justify-between"
          >
            Password
            <Button type="button" variant="link" asChild>
              <Link href="/forgot-password">Reset password</Link>
            </Button>
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              name="password"
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-gray-500" />
              )}
              <span className="sr-only">
                {showPassword ? "Hide password" : "Show password"}
              </span>
            </Button>
          </div>
        </div>
        <Button type="submit">
          {isPending && <Loader />}
          {isPending ? "Logging in..." : "Login"}
        </Button>
      </form>
      <div className="text-center">
        <Button type="button" variant="link" asChild>
          <Link href="/signup">Don&apos;t have an account? Sign up</Link>
        </Button>
      </div>
    </div>
  );
}
