import * as React from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

import { cn } from "@/common/lib/shadcn-utils";
import { Button } from "@/common/components/ui/button";
import { Calendar } from "@/common/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/common/components/ui/popover";

interface DatePickerProps {
  date: Date | undefined;
  onSelect: (date: Date | undefined) => void;
  label: string;
  minDate?: Date;
  disabled?: boolean;
}

export function DatePicker({
  date,
  onSelect,
  label,
  minDate,
  disabled,
}: DatePickerProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            "bg-background text-foreground hover:bg-accent border-input h-12 w-full justify-start border text-left font-normal",
            !date && "text-muted-foreground",
          )}
          disabled={disabled}
        >
          <div className="flex flex-col">
            <span className="text-xs">{label}</span>
            {date ? (
              format(date, "EEE dd MMM")
            ) : (
              <div className="flex items-center gap-2">
                <CalendarIcon className="mr-2 h-4 w-4" />
                <span>Select date</span>
              </div>
            )}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={onSelect}
          initialFocus
          disabled={(date: Date) =>
            (minDate ? date < minDate : false) ||
            date >
              new Date(new Date().setFullYear(new Date().getFullYear() + 1))
          }
          defaultMonth={date}
        />
      </PopoverContent>
    </Popover>
  );
}
