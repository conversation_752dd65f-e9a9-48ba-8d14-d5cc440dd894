import { useQuery } from "@tanstack/react-query";
import ClientService from "@/common/services/client.service";

const service = new ClientService();

export function useClient(id: number | null | undefined) {
    return useQuery({
        queryKey: ["client", id],
        queryFn: () => service.findById(id),
        enabled: !!id,
        staleTime: 1000 * 60, // 1 minute
        gcTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: true,
    });
}
