import { DivideIcon as LucideIcon , AirVent, Radio, Shield, Calendar, Box, Truck, AlertTriangle, Circle, ArrowRight, Battery, Thermometer, Link, Smartphone, Headset, Fuel, Tv } from 'lucide-react';

export type CategoryType = {
  id: string;
  label: string;
  icon: typeof LucideIcon;
};

export const categories: CategoryType[] = [
  // { id: 'airConditioning', label: 'Air Conditioning', icon: AirConditioning },
  { id: 'airVent', label: 'Air Vent', icon: AirVent},
  { id: 'radioSystem', label: 'radio System', icon: Radio },
  {id: 'headset', label: ' headset', icon: Headset},
  {id: 'fuel', label: ' fuel', icon: Fuel},
  {id: 'tv', label: ' tv', icon: Tv },
  {id: 'driverAssistance', label: 'Driver Assistance', icon: Shield },
  { id: 'wheelBase', label: 'Wheel Base', icon: Calendar },
  { id: 'carLength', label: 'Car Length', icon: Box },
  { id: 'airBags', label: 'Air Bags', icon: Circle },
  { id: 'safetyFeatures', label: 'Safety Features', icon: AlertTriangle },
  { id: 'color', label: 'Car Color', icon: ArrowRight },
  { id: 'mileage', label: 'Mileage', icon: Truck },
  { id: 'enginePower', label: 'Engine Power', icon: Calendar },
  { id: 'seatingCapacity', label: 'Seating Capacity', icon: Calendar },
  { id: 'battery', label: 'Battery Health', icon: Battery },
  { id: 'climateControl', label: 'Climate Control', icon: Thermometer },
  { id: 'roofType', label: 'Roof Type', icon: Link },
  { id: 'connectivity', label: 'Connectivity', icon: Smartphone },
  { id: 'driveType', label: 'Drive Type', icon: ArrowRight },
  { id: 'maxSpeed', label: 'Max Speed', icon: Calendar },
  { id: 'acceleration', label: 'Acceleration (0-60)', icon: Calendar },
  { id: 'price', label: 'Price Range', icon: Calendar },
  { id: 'year', label: 'Year', icon: Calendar },
];

export type FilterType = {
  priceRange: [number, number];
  airConditioning: boolean;
  audioSystem: boolean;
  driverAssistance: boolean;
  transmission: string[]; // Automatic, Manual, etc.
  wheelBase: number; // Specific wheelbase range or values
  cylinders: number; // Number of cylinders
  carLength: number; // Car length range
  airBags: number; // Number of airbags
  safetyFeatures: string[]; // List of safety features like ABS, traction control, etc.
  color: string[];
  fuelType: string[]; // Petrol, Diesel, Electric
  mileage: number; // Mileage range
  enginePower: number; // Engine power in horsepower or kilowatts
  seatingCapacity: number; // Seating capacity
  battery: string[]; // Battery condition (Good, Fair, Needs replacement)
  climateControl: boolean; // Whether climate control is available
  roofType: string[]; // Roof options like Sunroof, Moonroof, Convertible
  connectivity: boolean; // Connectivity features like Bluetooth, Wi-Fi, Apple CarPlay
  driveType: string[]; // Front-Wheel Drive, Rear-Wheel Drive, All-Wheel Drive
  maxSpeed: number; // Max speed of the car
  acceleration: string; // 0-60 acceleration time
  year: number; // Year of manufacture
};

export const transmissionTypes = [
  'Automatic',
  'Manual',
  'CVT', // Continuously Variable Transmission
];

export const fuelTypes = [
  'Petrol',
  'Diesel',
  'Electric',
  'Hybrid',
];

export const safetyFeatures = [
  'ABS',
  'Traction Control',
  'Electronic Stability Control',
  'Blind Spot Monitoring',
  'Lane Departure Warning',
  'Adaptive Cruise Control',
];

export const colors = [
  'Red',
  'Blue',
  'Black',
  'White',
  'Silver',
  'Gray',
  'Green',
  'Yellow',
  'Orange',
];

export const roofTypes = [
  'Sunroof',
  'Moonroof',
  'Convertible',
  'Fixed Roof',
];

export const driveTypes = [
  'Front-Wheel Drive',
  'Rear-Wheel Drive',
  'All-Wheel Drive',
  'Four-Wheel Drive',
];

