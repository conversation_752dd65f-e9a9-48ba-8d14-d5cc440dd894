import { metadata } from "./metadata";

export { metadata };

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Structured data for Contact Page
  const contactPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    name: "Contact MyKarLink",
    description:
      "Contact page for MyKarLink car rental and vehicle sharing platform",
    url: "https://mykarlink.com/contact-us",
    mainEntity: {
      "@type": "Organization",
      name: "MyKarLink",
      url: "https://mykarlink.com",
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: "https://mykarlink.com",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Contact Us",
          item: "https://mykarlink.com/contact-us",
        },
      ],
    },
  };

  const localBusinessJsonLd = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "@id": "https://mykarlink.com/#business",
    name: "MyKarLink",
    legalName: "MyKarLink PVT LTD",
    description: "Car rental and vehicle sharing platform in Zimbabwe",
    url: "https://mykarlink.com",
    logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
    image: "https://mykarlink.com/brands/karlink/images/og-contact.jpg",

    // Contact Information
    telephone: "+263779144386",
    email: "<EMAIL>",

    // Address
    address: {
      "@type": "PostalAddress",
      streetAddress: "7 St Antony, Cnr Five Av and Fifth St",
      addressLocality: "Harare",
      addressRegion: "Harare Province",
      addressCountry: "ZW",
      postalCode: "00263",
    },

    // Geographic coordinates (approximate for Harare CBD)
    geo: {
      "@type": "GeoCoordinates",
      latitude: "-17.8252",
      longitude: "31.0335",
    },

    // Area served
    areaServed: {
      "@type": "Country",
      name: "Zimbabwe",
    },

    // Business hours
    openingHoursSpecification: {
      "@type": "OpeningHoursSpecification",
      dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      opens: "09:00",
      closes: "17:00",
    },

    // Services offered
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Car Rental Services",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Car Rental",
            description: "Peer-to-peer car rental services",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Vehicle Sharing",
            description:
              "Vehicle sharing platform connecting car owners with renters",
          },
        },
      ],
    },

    // Contact points
    contactPoint: [
      {
        "@type": "ContactPoint",
        telephone: "+263779144386",
        contactType: "customer service",
        contactOption: "TollFree",
        availableLanguage: ["English"],
        areaServed: "ZW",
      },
      {
        "@type": "ContactPoint",
        email: "<EMAIL>",
        contactType: "customer service",
        availableLanguage: ["English"],
        areaServed: "ZW",
      },
    ],

    // Social media
    sameAs: [
      "https://www.facebook.com/people/My-Karlink/61572568015730/",
      "https://www.instagram.com/my_karlink/",
      "https://x.com/My_KarLink",
      "https://www.youtube.com/@mykarlink",
      "https://www.tiktok.com/@mykarlink",
    ],

    // Business category
    category: "Car Rental Service",
    priceRange: "$$",
  };

  const faqJsonLd = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: [
      {
        "@type": "Question",
        name: "How can I contact MyKarLink customer support?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "You can contact MyKarLink through WhatsApp at +263779144386, email <NAME_EMAIL>, or use our contact form on this page. We respond to all inquiries promptly.",
        },
      },
      {
        "@type": "Question",
        name: "What are MyKarLink's business hours?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Our customer support is available Monday to Friday from 9am to 5pm. However, you can submit inquiries through WhatsApp or email 24/7 and we'll respond during business hours.",
        },
      },
      {
        "@type": "Question",
        name: "Where is MyKarLink located?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "MyKarLink is located at 7 St Antony, Cnr Five Av and Fifth St, Harare, Zimbabwe. We serve customers throughout Zimbabwe.",
        },
      },
      {
        "@type": "Question",
        name: "How quickly do you respond to inquiries?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "We aim to respond to all customer inquiries within 24 hours during business days. For urgent matters, WhatsApp typically provides the fastest response time.",
        },
      },
    ],
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(contactPageJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(localBusinessJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      {children}
    </>
  );
}
