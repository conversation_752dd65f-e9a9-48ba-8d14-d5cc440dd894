import Footer from "@/app/components/footer";
import React from "react";
import "@/common/assets/styles/globals.css";
import Providers from "@/common/components/providers";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { auth } from "@/app/(auth)/auth";
import Navbar from "@/app/components/navbar";
import { Toaster } from "@/common/components/ui/sonner";
import { FlashMessageHandler } from "@/common/components/flash-message-handler";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";
import { cn } from "@/common/lib/shadcn-utils";
import { inter, rubik } from "@/app/fonts";
import { Metadata } from "next";

interface Props {
  children: React.ReactNode;
}

export async function generateMetadata(): Promise<Metadata> {
  const brandConfig = getCurrentBrandConfig();

  // Only implement SEO for Karlink brand
  if (brandConfig.name.toLowerCase() !== "karlink") {
    return {
      title: brandConfig.name,
      icons: {
        icon: [
          {
            url: `/brands/${brandConfig.name.toLowerCase()}/images/favicon.png`,
            type: "image/png",
          },
        ],
      },
    };
  }

  return {
    metadataBase: new URL("https://mykarlink.com"),
    title: {
      default: "MyKarLink - Car Rental & Vehicle Sharing in Zimbabwe",
      template: "%s | MyKarLink",
    },
    description:
      "Rent cars from trusted hosts across Zimbabwe. MyKarLink offers affordable, convenient car rental and vehicle sharing services with verified hosts and comprehensive insurance coverage.",
    keywords: [
      "car rental Zimbabwe",
      "vehicle sharing Zimbabwe",
      "car hire Zimbabwe",
      "Harare car rental",
      "peer-to-peer car sharing",
      "affordable car rental",
      "MyKarLink",
      "car booking Zimbabwe",
      "vehicle rental platform",
      "trusted car hosts",
    ],

    authors: [{ name: "MyKarLink Team" }],
    creator: "MyKarLink",
    publisher: "MyKarLink",
    category: "Transportation",

    icons: {
      icon: [
        {
          url: `/brands/${brandConfig.name.toLowerCase()}/images/favicon.png`,
          type: "image/png",
        },
      ],
      apple: [
        {
          url: `/brands/${brandConfig.name.toLowerCase()}/images/apple-touch-icon.png`,
          sizes: "180x180",
          type: "image/png",
        },
      ],
    },

    manifest: "/manifest.json",

    robots: {
      index: true,
      follow: true,
      nocache: false,
      googleBot: {
        index: true,
        follow: true,
        noimageindex: false,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },

    openGraph: {
      type: "website",
      locale: "en_US",
      url: "https://mykarlink.com",
      siteName: "MyKarLink",
      title: "MyKarLink - Car Rental & Vehicle Sharing in Zimbabwe",
      description:
        "Rent cars from trusted hosts across Zimbabwe. Affordable, convenient car rental and vehicle sharing services with verified hosts and comprehensive insurance coverage.",
      images: [
        {
          url: "https://mykarlink.com/brands/karlink/images/og-image.jpg",
          width: 1200,
          height: 630,
          alt: "MyKarLink - Car Rental & Vehicle Sharing Platform",
        },
      ],
    },

    twitter: {
      card: "summary_large_image",
      site: "@My_KarLink",
      creator: "@My_KarLink",
      title: "MyKarLink - Car Rental & Vehicle Sharing in Zimbabwe",
      description:
        "Rent cars from trusted hosts across Zimbabwe. Affordable, convenient car rental and vehicle sharing services.",
      images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
    },

    alternates: {
      canonical: "https://mykarlink.com",
    },
  };
}

async function BaseLayout({ children }: Props) {
  const session = await auth();
  const brandConfig = getCurrentBrandConfig();

  return (
    <html
      lang="en"
      className={cn(rubik.variable, inter.variable)}
      data-theme={brandConfig.theme}
    >
      <body className="bg-background text-foreground">
        <NuqsAdapter>
          <Providers session={session}>
            <div className="flex min-h-screen flex-col">
              <Navbar />
              <main className="flex-1">{children}</main>
              <Toaster richColors />
              <FlashMessageHandler />
              <Footer />
            </div>
          </Providers>
        </NuqsAdapter>
      </body>
    </html>
  );
}

export default BaseLayout;
