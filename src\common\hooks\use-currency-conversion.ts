"use client";

import { useState, useEffect, useCallback } from "react";
import { currencyService } from "@/common/services/currency.service";
import { formatCurrency } from "@/common/lib/currency-utils";
import { useUserCurrency } from "./use-user-preferences";

interface ConversionResult {
  originalAmount: number;
  convertedAmount: number;
  originalCurrency: string;
  targetCurrency: string;
  exchangeRate: number;
  formattedOriginal: string;
  formattedConverted: string;
}

interface UseCurrencyConversionReturn {
  convertAmount: (amount: number, fromCurrency: string) => Promise<ConversionResult>;
  convertAndFormat: (amount: number, fromCurrency: string) => Promise<string>;
  isLoading: boolean;
  error: string | null;
  cachedRates: Map<string, { rate: number; timestamp: number }>;
}

/**
 * Hook for currency conversion with caching and user preference integration
 */
export function useCurrencyConversion(): UseCurrencyConversionReturn {
  const { preferredCurrency } = useUserCurrency();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cachedRates, setCachedRates] = useState<Map<string, { rate: number; timestamp: number }>>(new Map());

  // Cache duration: 1 hour
  const CACHE_DURATION = 60 * 60 * 1000;

  /**
   * Get exchange rate with caching
   */
  const getExchangeRate = useCallback(async (fromCurrency: string, toCurrency: string): Promise<number> => {
    // Same currency, no conversion needed
    if (fromCurrency === toCurrency) {
      return 1;
    }

    const cacheKey = `${fromCurrency}_${toCurrency}`;
    const cached = cachedRates.get(cacheKey);
    
    // Check if we have a valid cached rate
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.rate;
    }

    try {
      setIsLoading(true);
      const exchangeRateData = await currencyService.getExchangeRate(fromCurrency, toCurrency);
      const rate = exchangeRateData.rate;

      // Cache the rate
      setCachedRates(prev => new Map(prev).set(cacheKey, {
        rate,
        timestamp: Date.now()
      }));

      return rate;
    } catch (err) {
      console.error(`Failed to get exchange rate for ${fromCurrency} to ${toCurrency}:`, err);
      
      // Fallback to cached rate if available, even if expired
      if (cached) {
        console.warn(`Using expired cached rate for ${fromCurrency} to ${toCurrency}`);
        return cached.rate;
      }
      
      throw new Error(`Unable to get exchange rate for ${fromCurrency} to ${toCurrency}`);
    } finally {
      setIsLoading(false);
    }
  }, [cachedRates]);

  /**
   * Convert amount from one currency to another
   */
  const convertAmount = useCallback(async (amount: number, fromCurrency: string): Promise<ConversionResult> => {
    try {
      setError(null);
      const targetCurrency = preferredCurrency;
      const exchangeRate = await getExchangeRate(fromCurrency, targetCurrency);
      const convertedAmount = amount * exchangeRate;

      return {
        originalAmount: amount,
        convertedAmount,
        originalCurrency: fromCurrency,
        targetCurrency,
        exchangeRate,
        formattedOriginal: formatCurrency(amount, fromCurrency),
        formattedConverted: formatCurrency(convertedAmount, targetCurrency),
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Currency conversion failed";
      setError(errorMessage);
      throw err;
    }
  }, [preferredCurrency, getExchangeRate]);

  /**
   * Convert amount and return formatted string
   */
  const convertAndFormat = useCallback(async (amount: number, fromCurrency: string): Promise<string> => {
    try {
      const result = await convertAmount(amount, fromCurrency);
      return result.formattedConverted;
    } catch (err) {
      // Fallback to original currency formatting if conversion fails
      console.warn("Currency conversion failed, using original currency:", err);
      return formatCurrency(amount, fromCurrency);
    }
  }, [convertAmount]);

  return {
    convertAmount,
    convertAndFormat,
    isLoading,
    error,
    cachedRates,
  };
}

/**
 * Hook for simple price formatting with currency conversion
 */
export function usePriceFormatter() {
  const { convertAndFormat, isLoading } = useCurrencyConversion();
  const { preferredCurrency } = useUserCurrency();

  /**
   * Format price with automatic currency conversion
   * Falls back to original currency if conversion fails
   */
  const formatPrice = useCallback(async (amount: number, originalCurrency: string = "USD"): Promise<string> => {
    try {
      if (originalCurrency === preferredCurrency) {
        return formatCurrency(amount, originalCurrency);
      }
      return await convertAndFormat(amount, originalCurrency);
    } catch (err) {
      console.warn("Price formatting failed, using fallback:", err);
      return formatCurrency(amount, originalCurrency);
    }
  }, [convertAndFormat, preferredCurrency]);

  /**
   * Synchronous price formatter that uses cached rates or falls back to original currency
   */
  const formatPriceSync = useCallback((amount: number, originalCurrency: string = "USD"): string => {
    if (originalCurrency === preferredCurrency) {
      return formatCurrency(amount, originalCurrency);
    }
    
    // For now, just format in the original currency
    // In a real implementation, you might want to use cached rates here
    return formatCurrency(amount, originalCurrency);
  }, [preferredCurrency]);

  return {
    formatPrice,
    formatPriceSync,
    isLoading,
  };
}
