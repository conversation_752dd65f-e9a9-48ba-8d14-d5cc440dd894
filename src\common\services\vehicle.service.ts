import { apiClient } from "@/common/lib/api-client";
import { PaginatedResponse, Vehicle } from "@/common/models";
import { VehicleQueryState } from "../types/vehicle-query";

export enum VehicleType {
    SUV = "SUV",
    ANY = "ANY",
    HATCHBACK = "HATCHBACK",
    MPV = "MPV",
    SEDAN = "SEDAN",
    PICKUP = "PICKUP",
}

export interface SearchVehicleParams {
    location: number | null;
    start: string | undefined;
    end: string | undefined;
    startTime: string;
    endTime: string;
    vehicleType: string;
    promotionType: string | null;
    page: number;
    size: number;
}

export default class VehicleService {
    private endPoint = "/vehicle";

    private makeUrl(path: string) {
        return `${this.endPoint}/${path}`;
    }
    async getTopRatedVehicles(page = 0, size = 10) {
        const url = this.makeUrl(`/top-rated/${page}/${size}`);
        const response = await apiClient.get<PaginatedResponse<Vehicle>>(url);

        return response.data;
    }

    async getVehicleById(id: number) {
        const url = this.makeUrl(`/${id}`);
        const response = await apiClient.get<Vehicle>(url);

        return response.data;
    }

    async searchVehicles(params: VehicleQueryState) {
        const { page, size, ...rest } = params;
        const baseUrl = this.makeUrl(`/public-search/${page}/${size}`);
        const searchParams = new URLSearchParams();

        // Add non-empty params to query string
        Object.entries(rest).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== "") {
                searchParams.append(key, value.toString());
            }
        });

        // Combine URL with search params
        const url = searchParams.toString()
            ? `${baseUrl}?${searchParams.toString()}`
            : baseUrl;
        const response = await apiClient.get<PaginatedResponse<Vehicle>>(url);

        return response.data;
    }
}
