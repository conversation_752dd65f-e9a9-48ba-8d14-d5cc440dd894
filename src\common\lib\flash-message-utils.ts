import Cookies from "js-cookie";

export const FLASH_COOKIE_NAME = "flash-message";

export interface FlashMessage {
  title?: string;
  description: string;
  variant?: "default" | "destructive" | "info" | "success" | "warning";
}

export const getFlashMessage = () => {
  const cookie = Cookies.get(FLASH_COOKIE_NAME);
  const message: FlashMessage | null = JSON.parse(cookie || "null");
  return message;
};

export const setFlashMessage = (message: FlashMessage): void => {
  Cookies.set(FLASH_COOKIE_NAME, JSON.stringify(message), {
    path: "/",
    sameSite: "strict",
  });
};

export const clearFlashMessage = (): void => {
  Cookies.remove(FLASH_COOKIE_NAME, { path: "/" });
};
