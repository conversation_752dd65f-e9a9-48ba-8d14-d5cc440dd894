import { Star } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    location: "London",
    rating: 5,
    comment:
      "Excellent service! The car was in perfect condition and the pickup process was smooth and quick.",
    date: "December 2024",
  },
  {
    name: "<PERSON>",
    location: "Texas",
    rating: 5,
    comment:
      "Best car rental experience I've had. The staff was very helpful and professional.",
    date: "November 2024",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    location: "Lusaka",
    rating: 5,
    comment:
      "Very impressed with the quality of service. Will definitely use MyKarLink again!",
    date: "December 2024",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    location: "Harare",
    rating: 5,
    comment: "Best car rental service I have ever used. Highly recommend!",
    date: "December 2024",
  },
];

export default function Testimonials() {
  return (
    <section className="w-full bg-white py-20">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mb-12 flex flex-col items-center justify-center space-y-4 text-center">
          <h2 className="text-primary text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            What Our Customers Say
          </h2>
          <p className="max-w-[700px] text-gray-600 md:text-xl">
            Don&apos;t just take our word for it - hear from our satisfied
            customers
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.name}
              className="flex flex-col rounded-lg bg-gray-50 p-6"
            >
              <div className="mb-4 flex items-center">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-5 w-5 fill-yellow-400 text-yellow-400"
                  />
                ))}
              </div>
              <p className="mb-4 text-gray-600">
                &quot;{testimonial.comment}&quot;
              </p>
              <div className="mt-auto">
                <div className="flex items-center justify-between">
                  <p className="text-primary font-semibold">
                    {testimonial.name}
                  </p>
                  <span className="text-sm text-gray-500">
                    {testimonial.date}
                  </span>
                </div>
                <p className="text-sm text-gray-500">{testimonial.location}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
