"use client";

import type React from "react";
import { useState, useRef, useEffect, type ChangeEvent } from "react";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/common/components/ui/dialog";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/common/components/ui/avatar";
import { Camera, Loader2 } from "lucide-react";
import { Client } from "@/common/models";
import { fileService } from "@/common/services/file.service";
import useUpdateClientMutation from "@/common/hooks/use-update-client-mutation";
import { z } from "zod";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { PhoneInput } from "@/common/components/ui/phone-input";
import { cn } from "@/common/lib/shadcn-utils";
import { CountryComboBox } from "@/common/components/ui/country-combobox";
import { parsePhoneNumberWithError } from "libphonenumber-js";

interface EditProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  client: Client;
}

const profileSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  phone: z.string().min(1, "Phone number is required"),
  firstLine: z.string(),
  town: z.string(),
  country: z.string(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export function EditProfileDialog({
  open,
  onOpenChange,
  client,
}: EditProfileDialogProps) {
  const [firstName = "", lastName = ""] = client?.name?.split(" ") ?? [];
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Phone validation state
  const [phoneValidation, setPhoneValidation] = useState({
    isValid: false,
    error: null as string | null,
  });

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting, isValid },
    reset,
    setError,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    mode: "onChange",
    reValidateMode: "onChange",
    defaultValues: {
      firstName,
      lastName,
      phone: client?.telephone ?? "",
      firstLine: client?.address?.firstLine ?? "",
      town: client?.address?.town ?? "",
      country: client?.address?.county ?? "",
    },
  });

  const updateClientMutation = useUpdateClientMutation();

  // Validate the prefilled phone number on mount
  useEffect(() => {
    const phone = client?.telephone;
    if (phone) {
      // Use the same validation logic as the PhoneInput component
      try {
        const phoneNumber = parsePhoneNumberWithError(phone, "ZW");
        const isValid = phoneNumber?.isValid() ?? false;

        setPhoneValidation({
          isValid,
          error: isValid ? null : "Invalid phone number",
        });
      } catch {
        // If parsing fails, treat as invalid
        setPhoneValidation({
          isValid: false,
          error: "Invalid phone number",
        });
      }
    }
  }, [client?.telephone]);

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFileError(null);

      // Validate file type
      if (!file.type.startsWith("image/")) {
        setFileError("Please select an image file");
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setFileError("File size must be less than 5MB");
        return;
      }

      setSelectedFile(file);

      // Show preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setAvatarPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: ProfileFormData) => {
    // Check phone validation before submitting
    if (!phoneValidation.isValid && data.phone) {
      setError("phone", {
        type: "manual",
        message: phoneValidation.error || "Please enter a valid phone number",
      });
      return;
    }

    try {
      let logoUrl = client.logo;

      if (selectedFile) {
        const uploadResult = await fileService.compressAndUpload(selectedFile);
        logoUrl = uploadResult.fileUrl;
      }

      const updateData: Partial<Client> = {
        id: client?.id,
        email: client?.email,
        name: `${data.firstName} ${data.lastName}`,
        telephone: data.phone,
        logo: logoUrl,
        address: {
          firstLine: data.firstLine,
          secondLine: "",
          town: data.town,
          postcode: "",
          county: data.country,
        },
      };

      await updateClientMutation.mutateAsync(updateData);

      toast("Profile Updated", {
        description: "Your profile has been successfully updated",
        duration: 3000,
      });

      onOpenChange(false);
    } catch (error) {
      toast("Update Failed", {
        description:
          error instanceof Error ? error.message : "Failed to update profile",
        duration: 5000,
      });
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      const dialogElement = document.querySelector("[data-state='closed']");
      if (dialogElement) {
        dialogElement.addEventListener(
          "animationend",
          () => {
            reset();
            setSelectedFile(null);
            setAvatarPreview(null);
            setFileError(null);
            setPhoneValidation({ isValid: false, error: null });
          },
          { once: true },
        );
      }
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="w-[95%] p-4 sm:max-w-[700px] sm:p-6">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Edit Profile</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 py-4">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Avatar
                className="h-20 w-20 cursor-pointer border-2 border-gray-100 sm:h-24 sm:w-24"
                onClick={() => fileInputRef.current?.click()}
              >
                <AvatarImage
                  src={avatarPreview || client?.logo}
                  alt="User avatar"
                  className="object-cover"
                />
                <AvatarFallback>
                  {firstName ? firstName[0] : ""}
                  {lastName ? lastName[0] : ""}
                </AvatarFallback>
              </Avatar>
              <div
                className="absolute right-0 bottom-0 cursor-pointer rounded-full bg-blue-600 p-1.5"
                onClick={() => fileInputRef.current?.click()}
              >
                <Camera className="h-4 w-4 text-white" />
              </div>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              accept="image/*"
            />
            {fileError && (
              <p className="text-destructive text-sm">{fileError}</p>
            )}
            <p className="text-sm text-gray-500">
              Click to change profile picture
            </p>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="firstName"
                    className={cn(
                      errors.firstName &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.firstName && (
                <p className="text-destructive text-sm">
                  {errors.firstName.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="lastName"
                    className={cn(
                      errors.lastName &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.lastName && (
                <p className="text-destructive text-sm">
                  {errors.lastName.message}
                </p>
              )}
            </div>

            <div className="space-y-2 sm:col-span-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Controller
                name="phone"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <PhoneInput
                    value={value}
                    onChange={(phoneValue, isValid) => {
                      onChange(phoneValue);
                      setPhoneValidation({
                        isValid,
                        error: isValid ? null : "Invalid phone number",
                      });
                    }}
                    onValidationChange={(isValid, error) => {
                      setPhoneValidation({
                        isValid,
                        error: error || null,
                      });
                    }}
                    placeholder="Enter your phone number"
                    defaultCountry="ZW"
                    error={!!errors.phone}
                  />
                )}
              />
              {errors.phone && (
                <p className="text-destructive text-sm">
                  {errors.phone.message}
                </p>
              )}
              {!errors.phone && phoneValidation.error && (
                <p className="text-destructive text-sm">
                  {phoneValidation.error}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-medium">Address</h3>
            <div className="space-y-2">
              <Label htmlFor="firstLine">Address Line</Label>
              <Controller
                name="firstLine"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="firstLine"
                    className={cn(
                      errors.firstLine &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.firstLine && (
                <p className="text-destructive text-sm">
                  {errors.firstLine.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="town">City</Label>
                <Controller
                  name="town"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="town"
                      className={cn(
                        errors.town &&
                          "border-destructive focus-visible:ring-destructive",
                      )}
                    />
                  )}
                />
                {errors.town && (
                  <p className="text-destructive text-sm">
                    {errors.town.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Controller
                  name="country"
                  control={control}
                  render={({ field }) => (
                    <CountryComboBox
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Select country..."
                      error={!!errors.country}
                    />
                  )}
                />
                {errors.country && (
                  <p className="text-destructive text-sm">
                    {errors.country.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="flex flex-col gap-3 sm:flex-row sm:justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="w-full sm:w-auto"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !isValid || !phoneValidation.isValid}
              className="w-full sm:w-auto"
            >
              {isSubmitting && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
