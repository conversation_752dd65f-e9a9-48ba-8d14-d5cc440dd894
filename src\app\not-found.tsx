"use client";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import { ArrowLeft, Home } from "lucide-react";

export default function NotFound() {
  return (
    <main className="flex min-h-[75vh] flex-col items-center justify-center">
      <div className="container flex max-w-[64rem] flex-col items-center gap-4 text-center">
        <p className="text-sm font-medium text-primary">404</p>
        <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
          Page not found
        </h1>
        <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
          Sorry, we couldn&apos;t find the page you&apos;re looking for.
        </p>
        <div className="flex gap-4">
          <Button
            onClick={() => window.history.back()}
            variant="default"
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Go back
          </Button>
          <Button variant="ghost" asChild className="gap-2">
            <Link href="/">
              <Home size={16} />
              Go home
            </Link>
          </Button>
        </div>
      </div>
    </main>
  );
}
