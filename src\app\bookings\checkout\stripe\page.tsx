"use client";
import Image from "next/image";
import React from "react";
import { Appearance, loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import CheckoutForm from "@/app/bookings/checkout/stripe/checkout-form";
import { formatDateWithTime } from "@/common/lib/date-utils";
import { usdFormatter } from "@/common/lib/currency-utils";
import useReservedVehicleBooking from "@/common/hooks/use-reserved-vehicle-booking";
import { getBookingTotals } from "@/common/lib/booking-utils";
import placeholderImg from "@/common/assets/images/placeholder-vehicle-image.webp";

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? "",
);

function StripePayPage() {
  const { booking: reservedBooking } = useReservedVehicleBooking()!;
  const invoice = reservedBooking!.invoices[0]!;
  const vehicle = reservedBooking?.vehicle;
  const clientSecret = invoice.clientSecret!;
  const totals = getBookingTotals(reservedBooking);

  const appearance: Appearance = {
    theme: "stripe",
  };
  const options = {
    clientSecret,
    appearance,
  };
  return (
    <div className="bg-white pt-28 pb-20 lg:p-0">
      <div className="container grid min-h-screen gap-8 lg:grid-cols-2 lg:gap-4">
        <div className="flex h-full flex-col items-center justify-center gap-4 lg:p-8 lg:pt-28">
          <div className="flex w-full flex-col gap-2 self-start text-sm text-gray-500 md:text-base">
            <div className="text-primary flex items-end justify-between text-2xl font-semibold md:text-3xl">
              <span>
                {vehicle?.name} {vehicle?.model}
              </span>
              <span className="flex flex-col">
                <span className="text-end text-xs font-bold text-gray-500">
                  Payable At Collection
                </span>
                <span className="text-end text-xl md:text-2xl">
                  {usdFormatter.format(totals?.payableAtCollection || 0)}
                </span>
                {vehicle?.depositAmt && vehicle.depositAmt > 0 && (
                  <span className="text-end text-xs text-red-500">
                    Deposit = {usdFormatter.format(vehicle.depositAmt)}
                  </span>
                )}
              </span>
            </div>
            <div className="mt-2 flex justify-between gap-8">
              <div className="flex flex-col">
                <span className="text-xs font-bold">Start Date</span>
                <span>{formatDateWithTime(reservedBooking?.start || "")}</span>
              </div>
              <div className="flex flex-col">
                <span className="text-end text-xs font-bold">End Date</span>
                <span className="text-end">
                  {formatDateWithTime(reservedBooking?.end ?? "")}
                </span>
              </div>
            </div>
          </div>
          <div className="overflow-hidden rounded-md">
            <Image
              src={vehicle?.mainPhoto || placeholderImg}
              alt={`${vehicle?.name} ${vehicle?.model}`}
              className="w-full"
              width={1920}
              height={1080}
              priority
            />
          </div>
        </div>
        <div className="flex items-center justify-center md:p-8 lg:pt-28">
          <div className="mx-auto w-full max-w-lg space-y-6">
            <Elements options={options} stripe={stripePromise}>
              <CheckoutForm
                bookingId={reservedBooking?.id || 0}
                payableNow={totals?.payableOnline || 0}
              />
            </Elements>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StripePayPage;
