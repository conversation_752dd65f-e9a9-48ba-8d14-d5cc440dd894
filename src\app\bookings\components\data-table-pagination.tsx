"use client";

import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";
import * as React from "react";

import { But<PERSON> } from "@/common/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
} from "@/common/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import useBookingsQuery from "@/common/hooks/use-bookings-query";
import useBookings from "@/common/hooks/use-bookings";
import { useBookingsPagination } from "@/app/bookings/components/use-bookings-pagination";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  totalItems: number;
}

export function DataTablePagination<TData>({
  totalItems,
}: DataTablePaginationProps<TData>) {
  const {
    state,
    nextPage,
    lastPage,
    previousPage,
    firstPage,
    updateSize,
    updatePage,
  } = useBookingsQuery();
  const { isLoading } = useBookings();
  const pageCount = Math.ceil(totalItems / state.size);

  // Calculate showing range
  const startItem = state.page * state.size + 1;
  const endItem = Math.min((state.page + 1) * state.size, totalItems);
  const displayPage = state.page + 1;

  const { pages, showEllipsis } = useBookingsPagination({
    currentPage: displayPage,
    totalPages: pageCount,
  });

  if (totalItems === 0) return null;

  return (
    <div className="w-full space-y-4">
      <div className="flex w-full flex-col gap-4 sm:gap-3 md:flex-row md:items-center md:justify-between">
        <div className="order-2 text-center md:order-1 md:text-left">
          <p className="text-muted-foreground text-sm" aria-live="polite">
            <span className="md:hidden">
              Page {displayPage}/{pageCount}
            </span>
            <span className="hidden md:inline">
              Page{" "}
              <span className="text-foreground font-medium">{displayPage}</span>{" "}
              of{" "}
              <span className="text-foreground font-medium">{pageCount}</span> -
              Showing{" "}
              <span className="text-foreground font-medium">
                {startItem}-{endItem}
              </span>{" "}
              of{" "}
              <span className="text-foreground font-medium">{totalItems}</span>{" "}
              Bookings
            </span>
          </p>
        </div>

        <div className="order-3 flex justify-center md:order-3 md:justify-end">
          <Select
            value={state.size.toString()}
            onValueChange={(value) => {
              updateSize(Number(value));
            }}
            aria-label="Rows per page"
            disabled={isLoading}
          >
            <SelectTrigger
              id="rows-per-page"
              className="h-8 w-fit whitespace-nowrap"
            >
              <SelectValue placeholder="Select number of rows" />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50].map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size} Per Page
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="order-1 flex justify-center md:order-2">
          <Pagination>
            <PaginationContent className="flex flex-wrap justify-center gap-1">
              <PaginationItem className="hidden md:block">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 disabled:pointer-events-none disabled:opacity-50"
                  onClick={firstPage}
                  disabled={state.page === 0 || isLoading}
                  aria-label="Go to first page"
                >
                  <DoubleArrowLeftIcon className="h-4 w-4" aria-hidden="true" />
                </Button>
              </PaginationItem>

              <PaginationItem className="block md:hidden">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 disabled:pointer-events-none disabled:opacity-50"
                  onClick={firstPage}
                  disabled={state.page === 0 || isLoading}
                  aria-label="Go to first page"
                >
                  <DoubleArrowLeftIcon className="h-4 w-4" aria-hidden="true" />
                </Button>
              </PaginationItem>

              <PaginationItem>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex h-8 items-center gap-1 px-2 disabled:pointer-events-none disabled:opacity-50 sm:h-8 sm:px-3"
                  onClick={previousPage}
                  disabled={state.page === 0 || isLoading}
                  aria-label="Go to previous page"
                >
                  <ChevronLeftIcon className="h-4 w-4" aria-hidden="true" />
                  <span className="hidden sm:inline">Previous</span>
                </Button>
              </PaginationItem>

              <div className="hidden md:contents">
                {pages.map((page, index) => {
                  const isActive = page === displayPage;

                  if (
                    showEllipsis &&
                    index > 0 &&
                    page > pages[index - 1] + 1
                  ) {
                    return (
                      <React.Fragment key={`ellipsis-${index}`}>
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                        <PaginationItem key={`page-${page}`}>
                          <Button
                            size="sm"
                            variant={isActive ? "default" : "outline"}
                            onClick={() => updatePage(page - 1)}
                            disabled={isLoading}
                            aria-current={isActive ? "page" : undefined}
                            className="h-8 w-8"
                          >
                            {page}
                          </Button>
                        </PaginationItem>
                      </React.Fragment>
                    );
                  }

                  return (
                    <PaginationItem key={`page-${page}`}>
                      <Button
                        size="sm"
                        variant={isActive ? "default" : "outline"}
                        onClick={() => updatePage(page - 1)}
                        disabled={isLoading}
                        aria-current={isActive ? "page" : undefined}
                        className="h-8 w-8"
                      >
                        {page}
                      </Button>
                    </PaginationItem>
                  );
                })}
              </div>

              <PaginationItem>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex h-8 items-center gap-1 px-2 disabled:pointer-events-none disabled:opacity-50 sm:h-8 sm:px-3"
                  onClick={nextPage}
                  disabled={state.page >= pageCount - 1 || isLoading}
                  aria-label="Go to next page"
                >
                  <span className="hidden sm:inline">Next</span>
                  <ChevronRightIcon className="h-4 w-4" aria-hidden="true" />
                </Button>
              </PaginationItem>

              <PaginationItem className="hidden md:block">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 disabled:pointer-events-none disabled:opacity-50"
                  onClick={() => lastPage(totalItems)}
                  disabled={state.page >= pageCount - 1 || isLoading}
                  aria-label="Go to last page"
                >
                  <DoubleArrowRightIcon
                    className="h-4 w-4"
                    aria-hidden="true"
                  />
                </Button>
              </PaginationItem>

              <PaginationItem className="block md:hidden">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 disabled:pointer-events-none disabled:opacity-50"
                  onClick={() => lastPage(totalItems)}
                  disabled={state.page >= pageCount - 1 || isLoading}
                  aria-label="Go to last page"
                >
                  <DoubleArrowRightIcon
                    className="h-4 w-4"
                    aria-hidden="true"
                  />
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>

      <div className="block text-center md:hidden">
        <p className="text-muted-foreground text-xs">
          Showing {startItem}-{endItem} of {totalItems} bookings
        </p>
      </div>
    </div>
  );
}
