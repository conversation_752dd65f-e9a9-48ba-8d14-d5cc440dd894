"use client";

import React from "react";
import { Info, ArrowRightLeft } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/common/components/ui/tooltip";
import { Badge } from "@/common/components/ui/badge";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import { getCurrencySymbol } from "@/common/lib/currency-utils";

interface CurrencyIndicatorProps {
  originalCurrency: string;
  className?: string;
  showTooltip?: boolean;
  variant?: "badge" | "icon" | "text";
}

/**
 * Component that indicates when prices are displayed in a converted currency
 */
export function CurrencyIndicator({
  originalCurrency,
  className = "",
  showTooltip = true,
  variant = "icon",
}: CurrencyIndicatorProps) {
  const { preferredCurrency } = useUserCurrency();

  // Don't show indicator if currencies are the same
  if (originalCurrency === preferredCurrency) {
    return null;
  }

  const originalSymbol = getCurrencySymbol(originalCurrency);
  const preferredSymbol = getCurrencySymbol(preferredCurrency);

  const content = (
    <div className={`flex items-center gap-1 ${className}`}>
      {variant === "badge" && (
        <Badge variant="secondary" className="text-xs">
          <ArrowRightLeft className="h-3 w-3 mr-1" />
          {originalCurrency} → {preferredCurrency}
        </Badge>
      )}
      
      {variant === "icon" && (
        <div className="flex items-center text-muted-foreground">
          <ArrowRightLeft className="h-3 w-3" />
        </div>
      )}
      
      {variant === "text" && (
        <span className="text-xs text-muted-foreground">
          Converted from {originalCurrency}
        </span>
      )}
    </div>
  );

  if (!showTooltip) {
    return content;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {content}
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <p className="font-medium">Currency Conversion</p>
            <p>
              Prices converted from {originalCurrency} ({originalSymbol}) to {preferredCurrency} ({preferredSymbol})
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Exchange rates updated hourly
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

interface PriceWithCurrencyIndicatorProps {
  amount: number;
  originalCurrency: string;
  formattedPrice: string;
  className?: string;
  showIndicator?: boolean;
}

/**
 * Component that displays a price with currency conversion indicator
 */
export function PriceWithCurrencyIndicator({
  amount,
  originalCurrency,
  formattedPrice,
  className = "",
  showIndicator = true,
}: PriceWithCurrencyIndicatorProps) {
  const { preferredCurrency } = useUserCurrency();
  const isConverted = originalCurrency !== preferredCurrency;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span>{formattedPrice}</span>
      {showIndicator && isConverted && (
        <CurrencyIndicator originalCurrency={originalCurrency} variant="icon" />
      )}
    </div>
  );
}

interface CurrencyConversionNoticeProps {
  className?: string;
}

/**
 * Global notice component that informs users about currency conversion
 */
export function CurrencyConversionNotice({ className = "" }: CurrencyConversionNoticeProps) {
  const { preferredCurrency } = useUserCurrency();

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-start gap-2">
        <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
        <div className="text-sm text-blue-800">
          <p className="font-medium">Currency Display</p>
          <p>
            Prices are shown in your preferred currency ({preferredCurrency}) and converted from the vehicle owner's base currency using real-time exchange rates.
          </p>
        </div>
      </div>
    </div>
  );
}
