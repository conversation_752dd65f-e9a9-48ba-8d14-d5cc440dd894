"use client";

import Image from "next/image";
import carSubIcon from "@/common/assets/images/car-sub-icon.png";

interface Props {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
}

export default function HeadingSection({ title, subtitle, children }: Props) {
  return (
    <section className="bg-white bg-no-repeat">
      <div className="container-top-rated container grid gap-8 py-24 lg:py-28">
        <div className="flex flex-col items-center gap-1">
          <Image src={carSubIcon} alt="icon of a car" />
          {subtitle && (
            <h3 className="font-semibold text-orange-500 md:text-lg">
              {subtitle}
            </h3>
          )}
          <h2 className="text-3xl font-bold text-primary md:text-5xl">
            {title}
          </h2>
        </div>
        {children}
      </div>
    </section>
  );
}
