"use client";

import React from "react";
import { But<PERSON> } from "@/common/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/common/components/ui/dialog";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { Alert, AlertDescription } from "@/common/components/ui/alert";
import { PhoneInput } from "@/common/components/ui/phone-input";
import { Info } from "lucide-react";
import { useState } from "react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { cn } from "@/common/lib/shadcn-utils";

interface CreateProviderProfileModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContinue: (data: ProviderFormData) => void;
  userFirstName: string;
  userLastName: string;
  userEmail: string;
  userPhone: string;
}

// Separate schema for profile data capture only (no password validation)
const profileDataSchema = z.object({
  firstName: z
    .string()
    .min(1, "First name is required")
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must be less than 50 characters"),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must be less than 50 characters"),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  telephone: z.string().min(1, "Phone number is required"),
  companyName: z
    .string()
    .min(1, "Company name is required")
    .min(2, "Company name must be at least 2 characters")
    .max(100, "Company name must be less than 100 characters"),
});

export type ProviderFormData = z.infer<typeof profileDataSchema>;

export function CreateProviderProfileModal({
  open,
  onOpenChange,
  onContinue,
  userFirstName,
  userLastName,
  userEmail,
  userPhone,
}: CreateProviderProfileModalProps) {
  // Phone validation state
  const [phoneValidation, setPhoneValidation] = useState({
    isValid: true, // Start as valid since we're pre-filling with existing data
    error: null as string | null,
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setError,
    reset,
  } = useForm<ProviderFormData>({
    resolver: zodResolver(profileDataSchema),
    mode: "onChange",
    defaultValues: {
      firstName: userFirstName,
      lastName: userLastName,
      email: userEmail,
      telephone: userPhone,
      companyName: "",
    },
  });

  const onSubmit = (data: ProviderFormData) => {
    // Check phone validation before submitting
    if (!phoneValidation.isValid && data.telephone) {
      setError("telephone", {
        type: "manual",
        message: phoneValidation.error || "Please enter a valid phone number",
      });
      return;
    }

    // Pass data to parent for next step (password confirmation)
    onContinue(data);
  };

  const handleClose = () => {
    onOpenChange(false);
    reset();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Car Rental Profile</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Info Alert */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Please confirm your details and add a company name for your car
              rental business.
            </AlertDescription>
          </Alert>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* First Name */}
            <div className="grid gap-2">
              <Label htmlFor="firstName">First Name</Label>
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="firstName"
                    type="text"
                    className={cn(
                      errors.firstName &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.firstName && (
                <p className="text-destructive text-sm">
                  {errors.firstName.message}
                </p>
              )}
            </div>

            {/* Last Name */}
            <div className="grid gap-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="lastName"
                    type="text"
                    className={cn(
                      errors.lastName &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.lastName && (
                <p className="text-destructive text-sm">
                  {errors.lastName.message}
                </p>
              )}
            </div>

            {/* Email (disabled) */}
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="email"
                    type="email"
                    disabled
                    className="bg-gray-50"
                  />
                )}
              />
            </div>

            {/* Phone */}
            <div className="grid gap-2">
              <Label htmlFor="telephone">Phone Number</Label>
              <Controller
                name="telephone"
                control={control}
                render={({ field }) => (
                  <PhoneInput
                    {...field}
                    onValidationChange={(isValid, error) =>
                      setPhoneValidation({ isValid, error: error || null })
                    }
                    className={cn(
                      (errors.telephone || phoneValidation.error) &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {(errors.telephone || phoneValidation.error) && (
                <p className="text-destructive text-sm">
                  {errors.telephone?.message || phoneValidation.error}
                </p>
              )}
            </div>

            {/* Company Name */}
            <div className="grid gap-2">
              <Label htmlFor="companyName">Company or Trade Name</Label>
              <Controller
                name="companyName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="companyName"
                    type="text"
                    placeholder="Enter your company name"
                    className={cn(
                      errors.companyName &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.companyName && (
                <p className="text-destructive text-sm">
                  {errors.companyName.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!isValid || !phoneValidation.isValid}
              >
                Continue
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
