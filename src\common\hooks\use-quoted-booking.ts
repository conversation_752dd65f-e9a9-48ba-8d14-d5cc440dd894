import { useLocalStorage } from "@uidotdev/usehooks";
import { VehicleBooking } from "@/common/models";

export const QUOTED_BOOKING_KEY = "quoted-booking";

export default function useQuotedBooking() {
    const [booking, setBooking] = useLocalStorage<VehicleBooking | null>(QUOTED_BOOKING_KEY, null);
    const saveQuotedBooking = (newBooking: VehicleBooking | null) => {
        setBooking(newBooking);
    };
    const clearQuotedBooking = () => {
        setBooking(null);
    };

    return {
        booking,
        saveQuotedBooking,
        clearQuotedBooking,
    };
}
