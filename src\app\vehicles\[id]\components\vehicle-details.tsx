import { LucideIcon } from "lucide-react";
import Image from "next/image";
import { Vehicle } from "@/common/models";
import Separator from "@/app/vehicles/[id]/components/seperator";
import { capitalizeDescription } from "@/app/helpers";
import Rating from "@/app/components/rating";
import {
  getDescription,
  getVehicleFeatures,
  removeRateDuplicates,
} from "@/common/lib/vehicle-utils";
import { usdFormatter } from "@/common/lib/currency-utils";
import DepositBadge from "@/app/components/deposit-badge";

export default function VehicleDetails({ vehicle }: { vehicle: Vehicle }) {
  const vehicleFeatures = getVehicleFeatures(vehicle);
  const description = getDescription(vehicle); // Get the description from the backend

  const agencyLogo = vehicle.agency?.logo;
  const mainRate = removeRateDuplicates(vehicle.vehicleRates).find(
    (rate) => rate.weekDay === "MTF",
  );

  return (
    <div className="grid gap-4">
      <div className="flex justify-between">
        <div className="grid gap-1">
          <Rating rating={vehicle.rating} />
          <h3 className="text-xl font-bold text-zinc-800">
            {vehicle.name + " " + vehicle.model}
          </h3>
          <span className="flex items-center gap-4">
            <div className="text-lg font-semibold">
              {usdFormatter.format(mainRate?.rate ?? 0)}/Day
            </div>
            <div className="text-secondary">
              <DepositBadge depositAmount={vehicle.depositAmt} />
            </div>
          </span>
        </div>

        {agencyLogo && (
          <Image
            className="self-start"
            width={90}
            height={90}
            src={agencyLogo}
            alt="logo of the vehicle provider"
          />
        )}
      </div>
      <Separator />
      <div className="grid gap-4">
        <h2 className="text-xl font-bold text-zinc-800">Key features</h2>

        <div className="grid grid-cols-[repeat(auto-fit,minmax(150px,1fr))] gap-2 lg:gap-4">
          {vehicleFeatures.map((feature) => (
            <VehicleFeature
              key={feature.title}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
            />
          ))}
        </div>
      </div>

      {description && description.trim() && (
        <div>
          <h2 className="text-xl font-bold text-zinc-800">Description</h2>
          <p className="text-sm text-zinc-600">{description}</p>
        </div>
      )}
    </div>
  );
}

function VehicleFeature({
  title,
  description,
  icon: Icon,
}: {
  title: string;
  description: unknown;
  icon: LucideIcon;
}) {
  return (
    <div className="flex items-start gap-2">
      <Icon
        size={36}
        className="bg-secondary/15 text-secondary rounded-md p-1"
      />
      <div className="flex flex-col">
        <span className="text-sm font-bold uppercase">{title}</span>
        <span className="text-sm capitalize">
          {typeof description === "string"
            ? capitalizeDescription(description)
            : (description as React.ReactNode)}
        </span>
      </div>
    </div>
  );
}
