import { useRouter } from "next/navigation";
import { Session } from "next-auth";

export function useProviderRedirect() {
    const router = useRouter();

    const redirectToProviderDashboard = (session: Session | null) => {
        if (!session?.user) return;

        const isLocalhost = window.location.hostname === "localhost";
        const expiryDate = new Date(new Date().getTime() + 30 * 60 * 1000); // 30 minutes

        // Extract specific values from session.user
        const {
            access_token,
            refresh_token,
            userType,
            agentId,
            clientId,
            firstName,
            lastName,
        } = session.user;

        // Format display name as "Lastname .F"
        const displayName = `${lastName} .${firstName.charAt(0)}`;

        // Helper function to set cookie with consistent options
        const setCookie = (name: string, value: string) => {
            const baseOptions = `Path=/; SameSite=Strict; expires=${expiryDate.toUTCString()};`;
            const domainOption = !isLocalhost ? "Domain=.mykarlink.com;" : "";
            const secureOption = !isLocalhost ? "Secure;" : "";

            document.cookie = `${name}=${value}; ${baseOptions} ${domainOption} ${secureOption}`;
        };

        // Set individual cookies
        setCookie("x_access_token", access_token);
        setCookie("x_refresh_token", refresh_token);
        setCookie("x_user_type", userType.toLowerCase());
        setCookie("x_agent_id", ((agentId || clientId) ?? '').toString());
        setCookie("x_display_name", displayName);

        router.push(getRedirectUrl());
    };

    return { redirectToProviderDashboard };
}

// Same redirect logic as in login page
const getRedirectUrl = () => {
    const baseUrl = process.env.NEXT_PUBLIC_URL;

    if (!baseUrl) {
        console.warn("NEXT_PUBLIC_URL is not defined");
        return "https://admin.mykarlink.com/agency/home?redirected=true";
    }

    const url = new URL(baseUrl);

    switch (url.hostname) {
        case "localhost":
            return "http://localhost:4200/agency/home?redirected=true";
        case "test.mykarlink.com":
            return "https://test-admin.mykarlink.com/agency/home?redirected=true";
        case "mykarlink.com":
            return "https://admin.mykarlink.com/agency/home?redirected=true";
        default:
            return "https://admin.mykarlink.com/agency/home?redirected=true";
    }
};
