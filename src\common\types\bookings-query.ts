export interface BookingsQueryParams {
    page: number;
    size: number;
    searchCriteria: string | null;
    statuses: string[];
}

export interface BookingsQueryActions {
    updatePage: (page: number) => void;
    nextPage: () => void
    lastPage: (totalItems: number) => void;
    previousPage: () => void;
    firstPage: () => void;
    updateSize: (size: number) => void;
    updateSearchCriteria: (criteria: string | null) => void;
    updateStatuses: (statuses: string[]) => void;
}
