type UsePaginationProps = {
    currentPage: number
    totalPages: number
}

type UsePaginationReturn = {
    pages: number[]
    showEllipsis: boolean
}

export function useSearchPagination({ currentPage, totalPages }: UsePaginationProps): UsePaginationReturn {
    // Function to generate pagination with first 3 and last 3 pages
    function generatePagination(): { pages: number[]; showEllipsis: boolean } {
        // If we have 6 or fewer pages, just show all pages
        if (totalPages <= 6) {
            return {
                pages: Array.from({ length: totalPages }, (_, i) => i + 1),
                showEllipsis: false,
            }
        }

        // For many pages, we need a more sophisticated approach
        let pages: number[] = []
        const showEllipsis = true

        // Always include first and last page
        const firstPage = 1
        const lastPage = totalPages

        // Case 1: Current page is in the first 3 pages
        if (currentPage <= 3) {
            pages = [1, 2, 3, 4, 5, lastPage]
            return { pages, showEllipsis }
        }

        // Case 2: Current page is in the last 3 pages
        if (currentPage >= totalPages - 2) {
            pages = [firstPage, totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages]
            return { pages, showEllipsis }
        }

        // Case 3: Current page is somewhere in the middle
        // Show current page and one page before and after it
        pages = [firstPage, currentPage - 1, currentPage, currentPage + 1, lastPage]

        return { pages, showEllipsis }
    }

    const { pages, showEllipsis } = generatePagination()

    return {
        pages,
        showEllipsis,
    }
}
