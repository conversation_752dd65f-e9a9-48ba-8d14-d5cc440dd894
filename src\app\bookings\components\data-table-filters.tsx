"use client";

import { Search, X } from "lucide-react";
import { Table } from "@tanstack/react-table";
import * as React from "react";

import { Button } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import { DataTableViewOptions } from "./data-table-view-options";
import useBookings from "@/common/hooks/use-bookings";

interface DataTableFiltersProps<TData> {
  table: Table<TData>;
  searchCriteria: string | null;
  statuses: string[];
  onSearchChange: (search: string | null) => void;
  onStatusesChange: (statuses: string[]) => void;
}

const bookingStatuses = [
  { value: "RESERVED", label: "Reserved", color: "bg-blue-100 text-blue-800" },
  { value: "BOOKED", label: "Booked", color: "bg-green-100 text-green-800" },
  {
    value: "WAITINGAUTH",
    label: "In Progress",
    color: "bg-yellow-100 text-yellow-800",
  },
  { value: "COMPLETE", label: "Complete", color: "bg-gray-100 text-gray-800" },
  { value: "CANCELLED", label: "Cancelled", color: "bg-red-100 text-red-800" },
];

export function DataTableFilters<TData>({
  table,
  searchCriteria,
  statuses,
  onSearchChange,
  onStatusesChange,
}: DataTableFiltersProps<TData>) {
  const [searchInput, setSearchInput] = React.useState(searchCriteria ?? "");
  const { isLoading } = useBookings();
  const isFiltered =
    (searchCriteria && searchCriteria.length > 0) || statuses.length > 0;

  // Update local state when searchCriteria prop changes
  React.useEffect(() => {
    setSearchInput(searchCriteria ?? "");
  }, [searchCriteria]);

  const handleSearch = () => {
    onSearchChange(searchInput.length > 0 ? searchInput : null);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  const handleStatusChange = (status: string) => {
    const newStatuses = statuses.includes(status)
      ? statuses.filter((s: string) => s !== status)
      : [...statuses, status];
    onStatusesChange(newStatuses);
  };

  const removeStatus = (status: string) => {
    onStatusesChange(statuses.filter((s: string) => s !== status));
  };

  const clearFilters = () => {
    setSearchInput("");
    onSearchChange(null);
    onStatusesChange([]);
  };

  return (
    <div className="grid w-full gap-4">
      <div className="flex w-full flex-col justify-between gap-4 lg:flex-row lg:gap-0">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search bookings..."
            value={searchInput}
            onChange={(event) => setSearchInput(event.target.value)}
            onKeyUp={handleKeyPress}
            disabled={isLoading}
          />
          <Button onClick={handleSearch} disabled={isLoading}>
            <Search className="mr-1 h-4 w-4" />
            Search
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <DataTableViewOptions table={table} />
          <Select
            value=""
            onValueChange={handleStatusChange}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Status" />
              {statuses.length > 0 && (
                <span className="bg-primary/10 text-primary rounded-full px-2 py-0.5 text-xs font-semibold">
                  {statuses.length}
                </span>
              )}
            </SelectTrigger>
            <SelectContent>
              {bookingStatuses.map((status) => (
                <SelectItem
                  key={status.value}
                  value={status.value}
                  disabled={statuses.includes(status.value)}
                >
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {isFiltered && (
            <Button variant="ghost" onClick={clearFilters}>
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      {statuses.length > 0 && (
        <div className="bg-muted/30 rounded-md border p-3">
          <div className="grid gap-2">
            <div className="text-sm font-medium">Active Filters:</div>
            <div className="flex flex-wrap items-center gap-2">
              {statuses.map((status: string) => {
                const statusLabel =
                  bookingStatuses.find((s) => s.value === status)?.label ||
                  status;
                return (
                  <div
                    key={status}
                    className="bg-primary/50 text-primary-foreground hover:bg-primary/70 inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium transition-all"
                  >
                    <span className="whitespace-nowrap">{statusLabel}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeStatus(status)}
                      className="h-3 w-3 cursor-pointer rounded-full p-2"
                    >
                      <X className="h-2.5 w-2.5" />
                    </Button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
