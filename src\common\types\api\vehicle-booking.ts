import { definitions } from "@/common/types/api/schema";

// Main Vehicle Booking Types
export type VehicleBooking = definitions["VehicleBooking"];
export type VehicleBookingDto = definitions["VehicleBookingDto"];

// Vehicle Booking Status
export type VehicleBookingStatus = NonNullable<VehicleBooking["status"]>;

// Vehicle Booking Deposit
export type VehicleBookingDepositDto = definitions["VehicleBookingDepositDto"];

// Vehicle Booking Photo
export type VehicleBookingPhoto = definitions["VehicleBookingPhoto"];

// Damage Information
export type DamageInfo = definitions["DamageInfo"];
export type DamageInfoType = NonNullable<DamageInfo["type"]>;

// Payment Gateway Types
export type VehicleBookingGatewayType = NonNullable<
  VehicleBookingDto["gatewayType"]
>;

// Paginated Vehicle Booking
export type PageVehicleBookingDto = definitions["Page«VehicleBookingDto»"];

// General Booking Types
export type BookingResultDto = definitions["BookingResultDto"];
export type PageBookingResultDto = definitions["Page«BookingResultDto»"];

// Booking Type Enum (from schema)
export type BookingType = NonNullable<BookingResultDto["bookingType"]>;
