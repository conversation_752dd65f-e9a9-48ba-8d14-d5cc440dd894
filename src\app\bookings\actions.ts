"use server";

import { VehicleBooking } from "@/common/models";
import VehicleBookingService, {
  CreateBookingInput,
} from "@/common/services/vehicle-booking.service";
import { AxiosError } from "axios";
import { redirect } from "next/navigation";
import { formatToISO8601 } from "@/common/lib/date-utils";

export async function createBookingAction(state: unknown, form: FormData) {
  const formData = Object.fromEntries(form);
  const gatewayType = formData.gatewayType as "STRIPE" | "PAYNOW";

  const start = formatToISO8601(new Date(formData.start as string));
  const end = formatToISO8601(new Date(formData.end as string));

  let booking = {} as VehicleBooking;
  const bookingInput: CreateBookingInput = {
    firstname: formData.firstname as string,
    surname: formData.surname as string,
    email: formData.email as string,
    phone: formData.phone as string,
    start,
    end,
    vehicleId: Number(formData.vehicleId),
    clientId: formData.clientId ? Number(formData.clientId) : undefined,
    gatewayType,
    vehicleAddonsIds: JSON.parse(formData.addons as string),
  };

  try {
    const service = new VehicleBookingService();
    booking = await service.createBooking(bookingInput);
  } catch (e) {
    if (e instanceof AxiosError) {
      console.error(e.response?.data);
    }
  }

  // TODO: Add error handling for cases when either booking.redirectUrl or booking.clientSecret are falsy
  if (gatewayType === "STRIPE") {
    return booking;
  } else {
    const invoice = booking.invoices[0];
    redirect(invoice.redirectUrl!);
  }
}
