import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/common/components/ui/table";
import { Button } from "@/common/components/ui/button";
import { Eye } from "lucide-react";
import { VehicleBooking } from "@/common/models";
import { formatDateWithTime } from "@/common/lib/date-utils";
import { usdFormatter } from "@/common/lib/currency-utils";
import Link from "next/link";

interface Props {
  bookings: VehicleBooking[] | null | undefined;
}

export function BookingsTable({ bookings }: Props) {
  return (
    <div className="overflow-hidden rounded-md border">
      <Table>
        <TableHeader className="bg-slate-100">
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Vehicle</TableHead>
            <TableHead>Reg Number</TableHead>
            <TableHead>Days Hired</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Full Details</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {!bookings || bookings.length === 0 ? (
            <TableRow className="text-center">
              <TableCell colSpan={7} className="h-24 text-center">
                No bookings yet
              </TableCell>
            </TableRow>
          ) : (
            bookings.map((booking) => (
              <TableRow key={booking.id}>
                <TableCell>{booking.id}</TableCell>
                <TableCell>{formatDateWithTime(booking.start)}</TableCell>
                <TableCell>
                  {booking.vehicle.name} {booking.vehicle.model}
                </TableCell>
                <TableCell>{booking.vehicle.regno}</TableCell>
                <TableCell>{calculateDaysHired(booking)}</TableCell>
                <TableCell>
                  {usdFormatter.format(booking.invoices[0]?.totalAmount)}
                </TableCell>
                <TableCell>
                  <Button variant="ghost" size="icon" asChild>
                    <Link href={`/bookings/${booking.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}

function calculateDaysHired(booking: VehicleBooking) {
  return (
    booking.invoices[0]?.invoiceItemResult.filter((item) =>
      item.description.includes("Car rental for"),
    ).length || 0
  );
}
