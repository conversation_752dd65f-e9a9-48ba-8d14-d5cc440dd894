import { getCountries, getCountryCallingCode, type CountryCode } from "libphonenumber-js";

export interface Country {
    code: CountryCode;
    name: string;
    dialCode: string;
    flag: string;
}

// Timezone to country mapping for auto-detection
export const TIMEZONE_TO_COUNTRY: Record<string, CountryCode> = {
    // North America
    "America/New_York": "US",
    "America/Chicago": "US",
    "America/Denver": "US",
    "America/Los_Angeles": "US",
    "America/Phoenix": "US",
    "America/Anchorage": "US",
    "America/Honolulu": "US",
    "America/Toronto": "CA",
    "America/Vancouver": "CA",
    "America/Montreal": "CA",
    "America/Mexico_City": "MX",

    // Europe
    "Europe/London": "GB",
    "Europe/Dublin": "IE",
    "Europe/Paris": "FR",
    "Europe/Berlin": "DE",
    "Europe/Rome": "IT",
    "Europe/Madrid": "ES",
    "Europe/Amsterdam": "NL",
    "Europe/Brussels": "BE",
    "Europe/Vienna": "AT",
    "Europe/Zurich": "CH",
    "Europe/Stockholm": "SE",
    "Europe/Oslo": "NO",
    "Europe/Copenhagen": "DK",
    "Europe/Helsinki": "FI",
    "Europe/Warsaw": "PL",
    "Europe/Prague": "CZ",
    "Europe/Budapest": "HU",
    "Europe/Bucharest": "RO",
    "Europe/Sofia": "BG",
    "Europe/Athens": "GR",
    "Europe/Istanbul": "TR",
    "Europe/Moscow": "RU",

    // Asia Pacific
    "Asia/Tokyo": "JP",
    "Asia/Seoul": "KR",
    "Asia/Shanghai": "CN",
    "Asia/Hong_Kong": "HK",
    "Asia/Singapore": "SG",
    "Asia/Bangkok": "TH",
    "Asia/Jakarta": "ID",
    "Asia/Manila": "PH",
    "Asia/Kuala_Lumpur": "MY",
    "Asia/Ho_Chi_Minh": "VN",
    "Asia/Kolkata": "IN",
    "Asia/Dubai": "AE",
    "Asia/Riyadh": "SA",
    "Asia/Tehran": "IR",
    "Asia/Karachi": "PK",
    "Asia/Dhaka": "BD",
    "Asia/Colombo": "LK",

    // Australia & Oceania
    "Australia/Sydney": "AU",
    "Australia/Melbourne": "AU",
    "Australia/Brisbane": "AU",
    "Australia/Perth": "AU",
    "Australia/Adelaide": "AU",
    "Australia/Darwin": "AU",
    "Pacific/Auckland": "NZ",
    "Pacific/Fiji": "FJ",

    // Africa
    "Africa/Cairo": "EG",
    "Africa/Lagos": "NG",
    "Africa/Johannesburg": "ZA",
    "Africa/Nairobi": "KE",
    "Africa/Casablanca": "MA",
    "Africa/Algiers": "DZ",
    "Africa/Tunis": "TN",
    "Africa/Harare": "ZW",
    "Africa/Lusaka": "ZM",
    "Africa/Maputo": "MZ",
    "Africa/Gaborone": "BW",
    "Africa/Windhoek": "NA",
    "Africa/Kigali": "RW",
    "Africa/Kampala": "UG",
    "Africa/Dar_es_Salaam": "TZ",
    "Africa/Addis_Ababa": "ET",
    "Africa/Accra": "GH",
    "Africa/Abidjan": "CI",
    "Africa/Dakar": "SN",

    // South America
    "America/Sao_Paulo": "BR",
    "America/Argentina/Buenos_Aires": "AR",
    "America/Santiago": "CL",
    "America/Lima": "PE",
    "America/Bogota": "CO",
    "America/Caracas": "VE",
    "America/La_Paz": "BO",
    "America/Asuncion": "PY",
    "America/Montevideo": "UY",
    "America/Paramaribo": "SR",
    "America/Cayenne": "GF",
    "America/Guyana": "GY",
};

// Language code to country mapping for fallback detection
export const LANGUAGE_TO_COUNTRY: Record<string, CountryCode> = {
    "en-US": "US",
    "en-GB": "GB",
    "en-CA": "CA",
    "en-AU": "AU",
    "en-NZ": "NZ",
    "en-ZA": "ZA",
    "fr-FR": "FR",
    "fr-CA": "CA",
    "de-DE": "DE",
    "de-AT": "AT",
    "de-CH": "CH",
    "es-ES": "ES",
    "es-MX": "MX",
    "es-AR": "AR",
    "pt-BR": "BR",
    "pt-PT": "PT",
    "it-IT": "IT",
    "nl-NL": "NL",
    "sv-SE": "SE",
    "no-NO": "NO",
    "da-DK": "DK",
    "fi-FI": "FI",
    "pl-PL": "PL",
    "cs-CZ": "CZ",
    "hu-HU": "HU",
    "ro-RO": "RO",
    "bg-BG": "BG",
    "el-GR": "GR",
    "tr-TR": "TR",
    "ru-RU": "RU",
    "ja-JP": "JP",
    "ko-KR": "KR",
    "zh-CN": "CN",
    "zh-TW": "TW",
    "zh-HK": "HK",
    "th-TH": "TH",
    "vi-VN": "VN",
    "id-ID": "ID",
    "ms-MY": "MY",
    "hi-IN": "IN",
    "ar-SA": "SA",
    "ar-AE": "AE",
    "ar-EG": "EG",
    "he-IL": "IL",
    "fa-IR": "IR",
};
export const COUNTRIES_CACHE = getCountries()
    .map((country) => {
        let countryName: string = country;
        try {
            if (typeof Intl !== "undefined" && Intl.DisplayNames) {
                const displayName = new Intl.DisplayNames(["en"], {
                    type: "region",
                }).of(country);
                countryName = displayName || country;
            }
        } catch {
            countryName = country;
        }

        const codePoints = country
            .toUpperCase()
            .split("")
            .map((char) => 127397 + char.charCodeAt(0));
        const flag = String.fromCodePoint(...codePoints);

        return {
            code: country as CountryCode,
            name: countryName,
            dialCode: getCountryCallingCode(country as CountryCode),
            flag,
        };
    })
    .sort((a, b) => a.name.localeCompare(b.name));

export const useCountries = () => COUNTRIES_CACHE;



/**
 * Detect country from browser timezone
 */
export const detectCountryFromTimezone = (): CountryCode | null => {
    try {
        if (typeof Intl !== "undefined" && Intl.DateTimeFormat) {
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            return TIMEZONE_TO_COUNTRY[timezone] || null;
        }
    } catch (error) {
        console.warn("Failed to detect timezone:", error);
    }
    return null;
};

/**
 * Detect country from browser language
 */
export const detectCountryFromLanguage = (): CountryCode | null => {
    try {
        if (typeof navigator !== "undefined" && navigator.language) {
            const language = navigator.language;
            // Try exact match first
            if (LANGUAGE_TO_COUNTRY[language]) {
                return LANGUAGE_TO_COUNTRY[language];
            }
            // Try language code without region (e.g., "en" from "en-US")
            const languageCode = language.split("-")[0];
            const fallbackMapping: Record<string, CountryCode> = {
                "en": "US",
                "fr": "FR",
                "de": "DE",
                "es": "ES",
                "pt": "PT",
                "it": "IT",
                "nl": "NL",
                "sv": "SE",
                "no": "NO",
                "da": "DK",
                "fi": "FI",
                "pl": "PL",
                "cs": "CZ",
                "hu": "HU",
                "ro": "RO",
                "bg": "BG",
                "el": "GR",
                "tr": "TR",
                "ru": "RU",
                "ja": "JP",
                "ko": "KR",
                "zh": "CN",
                "th": "TH",
                "vi": "VN",
                "id": "ID",
                "ms": "MY",
                "hi": "IN",
                "ar": "SA",
                "he": "IL",
                "fa": "IR",
            };
            return fallbackMapping[languageCode] || null;
        }
    } catch (error) {
        console.warn("Failed to detect language:", error);
    }
    return null;
};

/**
 * Detect country from IP address using a free geolocation service with retry logic
 */
export async function detectCountryFromIP(retries: number = 2): Promise<CountryCode | null> {
    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            // Using ipapi.co - free tier allows 1000 requests per day
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch("https://ipapi.co/json/", {
                method: "GET",
                headers: {
                    "Accept": "application/json",
                    "User-Agent": "Mozilla/5.0 (compatible; CountryDetection/1.0)",
                },
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.error) {
                throw new Error(data.reason || "IP detection service error");
            }

            // Return the country code if it's valid
            if (data.country_code && typeof data.country_code === "string") {
                const countryCode = data.country_code.toUpperCase() as CountryCode;

                // Validate that it's a real country code
                if (countryCode.length === 2 && /^[A-Z]{2}$/.test(countryCode)) {
                    return countryCode;
                }
            }

            return null;
        } catch (error) {
            if (attempt === retries) {
                console.warn("IP-based country detection failed after retries:", error);
                return null;
            }

            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
    }

    return null;
}

/**
 * Alternative IP detection using different service (fallback) with retry logic
 */
export async function detectCountryFromIPFallback(retries: number = 2): Promise<CountryCode | null> {
    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            // Using ipinfo.io - free tier allows 50,000 requests per month
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch("https://ipinfo.io/json", {
                method: "GET",
                headers: {
                    "Accept": "application/json",
                    "User-Agent": "Mozilla/5.0 (compatible; CountryDetection/1.0)",
                },
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.country && typeof data.country === "string") {
                const countryCode = data.country.toUpperCase() as CountryCode;

                // Validate that it's a real country code
                if (countryCode.length === 2 && /^[A-Z]{2}$/.test(countryCode)) {
                    return countryCode;
                }
            }

            return null;
        } catch (error) {
            if (attempt === retries) {
                console.warn("Fallback IP detection failed after retries:", error);
                return null;
            }

            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
    }

    return null;
}

/**
 * Enhanced timezone detection with better mapping
 */
export const detectCountryFromTimezoneEnhanced = (): { country: CountryCode; confidence: number } | null => {
    try {
        if (typeof Intl !== "undefined" && Intl.DateTimeFormat) {
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const country = TIMEZONE_TO_COUNTRY[timezone];

            if (country) {
                // Higher confidence for more specific timezones
                const confidence = timezone.includes("/") ? 85 : 70;
                return { country, confidence };
            }
        }
    } catch (error) {
        console.warn("Failed to detect timezone:", error);
    }
    return null;
};

/**
 * Enhanced language detection with confidence scoring
 */
export const detectCountryFromLanguageEnhanced = (): { country: CountryCode; confidence: number } | null => {
    try {
        if (typeof navigator !== "undefined" && navigator.language) {
            const language = navigator.language;

            // Try exact match first (higher confidence)
            if (LANGUAGE_TO_COUNTRY[language]) {
                return {
                    country: LANGUAGE_TO_COUNTRY[language],
                    confidence: 75 // Higher confidence for exact matches
                };
            }

            // Try language code without region (lower confidence)
            const languageCode = language.split("-")[0];
            const fallbackMapping: Record<string, CountryCode> = {
                "en": "US", "fr": "FR", "de": "DE", "es": "ES", "pt": "PT",
                "it": "IT", "nl": "NL", "sv": "SE", "no": "NO", "da": "DK",
                "fi": "FI", "pl": "PL", "cs": "CZ", "hu": "HU", "ro": "RO",
                "bg": "BG", "el": "GR", "tr": "TR", "ru": "RU", "ja": "JP",
                "ko": "KR", "zh": "CN", "th": "TH", "vi": "VN", "id": "ID",
                "ms": "MY", "hi": "IN", "ar": "SA", "he": "IL", "fa": "IR",
            };

            if (fallbackMapping[languageCode]) {
                return {
                    country: fallbackMapping[languageCode],
                    confidence: 50 // Lower confidence for fallback matches
                };
            }
        }
    } catch (error) {
        console.warn("Failed to detect language:", error);
    }
    return null;
};
