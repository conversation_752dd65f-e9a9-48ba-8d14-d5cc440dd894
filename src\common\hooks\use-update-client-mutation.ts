import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Client } from "@/common/models";
import { clientService } from "@/common/services/client.service";

export default function useUpdateClientMutation() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (data: Partial<Client>) => {
            return clientService.update(data);
        },
        onSuccess: (_, inputData) => {
            queryClient.invalidateQueries({ queryKey: ["client", inputData.id] });
        }
    });
}
