"use client";
import { useFilters } from "./filter-context";
import { Label } from "@/common/components/ui/label";
import { TabsFilter } from "./tabs-filter";

const PROMOTION_TYPE_OPTIONS = {
  none: null,
  any: "ALL",
  discount: "DISCOUNT",
  extraMileage: "EXTRA_MILEAGE",
  extraDays: "EXTRA_DAYS",
  otherAward: "OTHER_AWARD",
} as const;

// Helper type for keys
type PromotionTypeKey = keyof typeof PROMOTION_TYPE_OPTIONS;

export default function PromotionTypeSelector() {
  const { filters, setFilters } = useFilters();

  const getCurrentValue = (): PromotionTypeKey => {
    const currentPromotionType = filters.promotionType;
    // Find the key corresponding to the current filter value
    for (const key in PROMOTION_TYPE_OPTIONS) {
      if (
        PROMOTION_TYPE_OPTIONS[key as PromotionTypeKey] === currentPromotionType
      ) {
        return key as PromotionTypeKey;
      }
    }
    return "none"; // Default to 'any' if no match found or value is null
  };

  return (
    <div className="space-y-3">
      <Label>Promotion Type</Label> {/* Changed Label */}
      <TabsFilter
        value={getCurrentValue()}
        onValueChange={(value) => {
          console.log(value, value!='none' ? true : null);
          setFilters({
            // Use the selected value (key) to get the corresponding backend value
            hasPromotion: value!="none" ? true : null,
            promotionType: PROMOTION_TYPE_OPTIONS[value as PromotionTypeKey],
          });
        }}
        // Updated options based on PROMOTION_TYPE_OPTIONS
        options={[
          { value: "any", label: "Any" },
          { value: "discount", label: "Discount" },
          { value: "extraMileage", label: "Extra Mileage" },
          { value: "extraDays", label: "Extra Days" },
          { value: "otherAward", label: "Special Offer" },
        ]}
      />
    </div>
  );
}
