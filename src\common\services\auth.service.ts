import { apiClient } from "@/common/lib/api-client";
import { AxiosError, isAxiosError } from "axios";

export type UserType = "ADMIN" | "AGENCY" | "CLIENT";

// TODO: service models should definitely go in common/services/models
export interface SignUpInput {
    administratorCreateDto: {
        adminEmail?: string;
        firstname?: string;
        lastname?: string;
    };
    billingEmail?: string;
    email?: string;
    name?: string;
    userType?: Exclude<UserType, "ADMIN">;
    agencyType?: string;
}

export interface SignInResponse {
    version: number;
    createdDate: number;
    lastModifiedDate: number;
    id: number;
    name: number;
    telephone: string;
    address: string;
    email: string;
    billingEmail: string;
    logo: string;
    sbsCode: string;
    purchaseOrder: string;
    invoices: [];
    status: string;
}

// TODO: This should probably just be an APIError
export interface AuthError {
    success?: false;
    status: string;
    violations?: {
        field: string;
        message: string;
    }[];
    title?: string;
    message: string;
    timestamp?: string;
    statusCode: number;
    errors?: string[];
    error?: string;
    formData?: {
        firstName?: string;
        lastName?: string;
        email?: string;
        userType?: "AGENCY" | "CLIENT";
        companyName?: string;
        username?: string;
    };
}

export interface LoginInput {
    username?: string;
    password?: string;
}

export interface Permission {
    id: number;
    authority: string;
    description: string;
}

export interface Role {
    id: number;
    name: string;
    permissions: Permission[];
}

export interface User {
    userType: UserType;
    agentId: number;
    clientId: null | number;
    workerId: number;
    roles: Role[];
    id: number;
    firstName: string;
    lastName: string;
    access_token: string;
    token_type: string;
    refresh_token: string;
    scope: string;
    expires_in: string;
}

export type UserWithoutRoles = Omit<User, "roles">;

export default class AuthService {
    baseUrl: string = process.env.NEXT_PUBLIC_API_URL!;

    async signUp(data: SignUpInput) {
        // TODO: Create separate services for agencies and clients
        const endpoint = data?.userType === "AGENCY" ? "/agency" : "/client";
        const response = await apiClient.post<AuthError | User>(endpoint, data);
        return response.data;
    }

    async login(data: LoginInput) {

        try {
            const baseUrl =
                this.baseUrl + "/oauth-service/api/v1/user-permission/login";
            const response = await apiClient.post<User>(baseUrl, data);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { roles, ...user } = response.data;
            return user;
        } catch (e) {
            if (isAxiosError(e)) {
                const axiosError = e as AxiosError<AuthError>;
                throw axiosError;
            }

            throw e
        }
    }

    async forgotPassword(email: string | undefined) {
        // TODO: Find a way to separate the host from the paths
        const baseURL =
            this.baseUrl + "/user-service/api/v1/user-management/user/resetPassword";

        const url = `${baseURL}/?email=${email}`;
        const response = await apiClient.post<AuthError>(url);

        return response.data;
    }

    async changePassword(token: string, newPassword: string) {
        const baseURL =
            this.baseUrl + "/user-service/api/v1/user-management/user/savePassword";
        const response = await apiClient.post<AuthError>(baseURL, {
            token,
            newPassword,
        });
        return response.data;
    }
}
