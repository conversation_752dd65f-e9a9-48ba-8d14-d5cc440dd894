// TODO: Find a better way to name this. `data.ts` seems bad.
import { Clock3, Mail, MapPin, Phone } from "lucide-react";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";

// Use the utility function to get brand config
const brandConfig = getCurrentBrandConfig();

// Export primary links from brand config for backward compatibility
export const primaryNavLinks = brandConfig.links.primary;

// Export contact data from brand config
export const contactData = [
    {
        icon: Phone,
        type: "phone",
        value: brandConfig.contactInfo.phone,
    },
    {
        icon: Mail,
        type: "email",
        value: brandConfig.contactInfo.email,
    },
    {
        icon: MapPin,
        type: "location",
        value: brandConfig.contactInfo.location,
    },
    {
        icon: Clock3,
        type: "hours",
        value: brandConfig.contactInfo.hours,
    },
];

export const VEHICLE_TYPES = [
    { label: "ALL", value: "ALL" },
    { label: "SUV", value: "SUV" },
    { label: "HATCHBACK", value: "HATCHBACK" },
    { label: "MPV", value: "MPV" },
    { label: "SEDAN", value: "SEDAN" },
    { label: "PICKUP/BAKKIE", value: "PICKUP" },
    { label: "SPECIAL/LUXURY", value: "SPECIAL" },
];

// export const carBrands = [
//     "/images/car-brands/13.svg",
//     "/images/car-brands/02.svg",
//     "/images/car-brands/14.svg",
//     "/images/car-brands/15.svg",
//     "/images/car-brands/05.svg",
//     "/images/car-brands/06.svg",
// ];
