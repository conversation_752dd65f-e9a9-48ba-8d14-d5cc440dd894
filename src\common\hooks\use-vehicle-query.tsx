import { useQueryStates } from "nuqs";
import { vehicleQueryConfig } from "@/common/config/vehicle-query-config";
import { parseDate, formatDate } from "@/common/lib/date-utils";
import type {
  VehicleQueryState,
  VehicleQueryActions,
} from "@/common/types/vehicle-query";

export function useVehicleQuery() {
  const [query, setQuery] = useQueryStates(vehicleQueryConfig, {
    clearOnDefault: false,
  });

  const state: VehicleQueryState = {
    location: query.location,
    start: parseDate(query.start),
    end: parseDate(query.end),
    startTime: query.startTime,
    endTime: query.endTime,
    vehicleType: query.vehicleType,
    page: query.page,
    size: query.size,
    agencyId: query.agencyId,
    lowMileageLimit: query.lowMileageLimit,
    maxDeposit: query.maxDeposit,
    minPrice: query.minPrice,
    maxPrice: query.maxPrice,
    transmission: query.transmission,
    hasPromotion: query.hasPromotion,
    promotionType: query.promotionType,
    sortBy: query.sortBy,
    sortDirection: query.sortDirection,
  };

  const actions: VehicleQueryActions = {
    updateLocation: (id) => setQuery({ location: id, page: 0 }),
    updateStart: (date) =>
      setQuery({ start: date ? formatDate(date) : "", page: 0 }),
    updateEnd: (date) =>
      setQuery({ end: date ? formatDate(date) : "", page: 0 }),
    updateStartTime: (time) => setQuery({ startTime: time, page: 0 }),
    updateEndTime: (time) => setQuery({ endTime: time, page: 0 }),
    updateVehicleType: (type) => setQuery({ vehicleType: type, page: 0 }),
    updatePage: (page) => setQuery({ page }),
    updateSize: (size) => setQuery({ size }),
    updateLowMileageLimit: (limit) =>
      setQuery({ lowMileageLimit: limit, page: 0 }),
    updateMaxDeposit: (deposit) => setQuery({ maxDeposit: deposit, page: 0 }),
    updateMinPrice: (price) => setQuery({ minPrice: price, page: 0 }),
    updateMaxPrice: (price) => setQuery({ maxPrice: price, page: 0 }),
    updateTransmission: (transmission) => setQuery({ transmission, page: 0 }),
    updateHasPromotion: (hasPromotion) => setQuery({ hasPromotion, page: 0 }),
    updatePromotionType: (promotionType) =>
      setQuery({ promotionType, page: 0 }),
    updateSortBy: (sortBy) => setQuery({ sortBy, page: 0 }),
    updateSortDirection: (sortDirection) =>
      setQuery({ sortDirection, page: 0 }),
  };

  const queryString = () => {
    const params = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value !== null && value !== "") {
        params.set(key, value.toString());
      }
    });
    return params.toString();
  };

  return {
    state,
    ...actions,
    queryString,
  };
}
