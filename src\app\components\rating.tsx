"use client";

import { Star } from "lucide-react";

interface RatingProps {
  rating: number | null;
}

export default function Rating({ rating }: RatingProps) {
  if (rating === null) {
    return <span className="italic text-muted-foreground">Not yet rated</span>;
  }

  return (
    <div className="flex items-center gap-2">
      <div className="flex">
        {[1, 2, 3, 4, 5].map((index) => {
          const difference = rating - index;
          return (
            <div key={index} className="relative">
              {/* Background star (gray) */}
              <Star className="h-5 w-5 fill-muted stroke-muted-foreground" />

              {/* Foreground star (yellow) with clip path for partial fills */}
              <div
                className="absolute inset-0"
                style={{
                  clipPath:
                    difference >= 0
                      ? "inset(0 0 0 0)"
                      : difference > -1
                        ? `inset(0 ${(1 - (difference + 1)) * 100}% 0 0)`
                        : "inset(0 100% 0 0)",
                }}
              >
                <Star className="h-5 w-5 fill-yellow-400 stroke-yellow-500" />
              </div>
            </div>
          );
        })}
      </div>
      <span className="text-sm text-muted-foreground">
        {rating.toFixed(1)} {rating === 1 ? "Star" : "Stars"}
      </span>
    </div>
  );
}
