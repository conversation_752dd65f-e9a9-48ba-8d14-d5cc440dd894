import Image from "next/image";
import authImg from "@/common/assets/images/auth.jpeg";
import { FlashMessageHandler } from "@/common/components/flash-message-handler";

interface Props {
  children: React.ReactNode;
}

export default function AuthLayout({ children }: Props) {
  return (
    <div className="grid min-h-screen bg-white lg:grid-cols-2">
      <div className="relative hidden lg:block">
        <Image
          src={authImg}
          alt="Luxury cars in a row"
          className="absolute inset-0 h-full w-full object-cover"
          width={1920}
          height={1080}
          priority
        />
        <FlashMessageHandler />
      </div>
      <div className="container flex items-center justify-center">
        {children}
      </div>
    </div>
  );
}
