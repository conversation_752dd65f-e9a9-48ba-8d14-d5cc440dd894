"use client";

import { Alert, AlertDescription } from "@/common/components/ui/alert";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/common/components/ui/alert-dialog";
import { Button } from "@/common/components/ui/button";
import { VehicleBooking } from "@/common/models";
import { useState } from "react";
import { useCancelBooking } from "@/common/hooks/use-cancel-booking";
import { Label } from "@/common/components/ui/label";
import { toast } from "sonner";
import Loader from "@/common/components/loader";
import { Textarea } from "@/common/components/ui/textarea";

export default function CancelBookingDialog({
  booking,
}: {
  booking: VehicleBooking;
}) {
  const [open, setOpen] = useState(false);
  const [reason, setReason] = useState("");
  const cancelBookingMutation = useCancelBooking();

  const handleProceed = () => {
    cancelBookingMutation.mutate(
      {
        id: booking.id,
        byAgency: booking.byAgency,
        reason,
      },
      {
        onSuccess: () => {
          setOpen(false);
          window.location.reload();
        },
        onError: (error) => {
          console.error("Cancel booking error:", error);
          toast.error(
            error instanceof Error
              ? error.message
              : "Failed to cancel booking.",
          );
        },
      },
    );
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" className="hover:cursor-pointer">
          Cancel Booking
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader className="gap-3">
          <AlertDialogTitle>Cancel this booking?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. Once cancelled, you will need to make
            a new booking if you change your mind.
          </AlertDialogDescription>
          <Alert
            variant="destructive"
            className="border-red-200 bg-red-50 text-red-800"
          >
            <AlertDescription className="block">
              Note: Full refund is only available if you cancel more than 48
              hours before your booking start time. Cancellations within 48
              hours of the booking will not be eligible for a refund.
            </AlertDescription>
          </Alert>

          <div className="mt-4">
            <Label
              className="mb-1 block text-sm font-medium"
              htmlFor="cancel-reason"
            >
              Reason for cancellation (optional)
            </Label>
            <Textarea
              id="cancel-reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Enter reason (optional)"
              disabled={cancelBookingMutation.isPending}
              rows={3}
            />
          </div>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={cancelBookingMutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <Button
            type="button"
            variant="destructive"
            onClick={handleProceed}
            disabled={cancelBookingMutation.isPending}
          >
            {cancelBookingMutation.isPending && <Loader />}
            {cancelBookingMutation.isPending ? "Cancelling..." : "Proceed"}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
