import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/common/components/ui/table";
import { formatUserCurrency } from "@/common/lib/currency-utils";
import { CurrencyAwarePrice, CurrencyAwareTotal } from "@/app/components/currency-aware-price";
import { formatDateWithoutTime } from "@/common/lib/date-utils";
import { VehicleBooking } from "@/common/models";
import { getBookingItems, getBookingTotals } from "@/common/lib/booking-utils";
import { getBookingInvoicePayments } from "@/common/lib/booking-utils";
import { Alert, AlertDescription } from "@/common/components/ui/alert";
import { Info, AlertCircle } from "lucide-react";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/common/components/ui/card";
import { Badge } from "@/common/components/ui/badge";
import Image from "next/image";

interface Props {
  booking: VehicleBooking;
}

function BookingInvoice({ booking }: Props) {
  const vehicle = booking.vehicle;
  const bookingItems = getBookingItems(booking);
  const bookingTotals = getBookingTotals(booking);
  const payments = getBookingInvoicePayments(booking);

  // Get the agency's base currency (where prices are originally stored)
  const agencyBaseCurrency = booking.vehicle?.agency?.baseCurrency || "USD";

  return (
    <Card>
      <CardHeader className="grid gap-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {booking.vehicle?.agency?.logo ? (
              <Image
                src={booking.vehicle.agency.logo}
                alt={`${booking.vehicle.agency.name} logo`}
                width={128}
                height={64}
                className="max-h-16 max-w-32 object-contain"
              />
            ) : (
              <div className="text-sm text-gray-500">
                {booking.vehicle?.agency?.name}
              </div>
            )}
          </div>

          <Badge variant="secondary" className="px-3 py-1 text-sm">
            {getBookingStatus(booking)}
          </Badge>
        </div>

        <div className="flex items-start justify-between gap-8">
          <div className="grid flex-1 gap-0.5 text-sm">
            <h3 className="font-semibold">Bill from:</h3>
            <div>
              <p className="font-medium">
                {booking.vehicle?.agency?.name || "Car Rental Agency"}
              </p>
              {booking.vehicle?.agency?.email && (
                <p>{booking.vehicle.agency.email}</p>
              )}
              {booking.vehicle?.agency?.telephone && (
                <p>{booking.vehicle.agency.telephone}</p>
              )}
            </div>
          </div>

          <div className="grid flex-1 gap-0.5 text-right text-sm">
            <h3 className="font-semibold">Bill to:</h3>
            <div>
              <p>
                {booking.firstname} {booking.surname}
              </p>
              <p>{booking.email}</p>
              <p>{booking.phone}</p>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="overflow-x-auto">
        <div className="mb-6 overflow-hidden rounded-lg border">
          <Table>
            <TableHeader className="bg-primary text-white">
              <TableRow>
                <TableHead className="text-left text-white">
                  Description
                </TableHead>
                <TableHead className="text-center text-white">
                  Days Hired
                </TableHead>
                <TableHead className="text-right text-white">
                  Total Amount
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bookingItems.map((item) => (
                <TableRow key={item.description}>
                  <TableCell className="capitalize">
                    {item.description}
                  </TableCell>
                  <TableCell className="text-center">{item.units}</TableCell>
                  <TableCell className="text-right">
                    <CurrencyAwarePrice
                      amount={item.total}
                      originalCurrency={agencyBaseCurrency}
                      showOriginal={false}
                      showConversionInfo={false}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="mb-6 overflow-hidden rounded-lg border">
          <Table>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">Subtotal</TableCell>
                <TableCell className="text-right">
                  <CurrencyAwarePrice
                    amount={bookingTotals.subTotal ?? 0}
                    originalCurrency={agencyBaseCurrency}
                    showOriginal={false}
                    showConversionInfo={false}
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Reservation Fee</TableCell>
                <TableCell className="text-right">
                  <CurrencyAwarePrice
                    amount={bookingTotals.reservationFee ?? 0}
                    originalCurrency={agencyBaseCurrency}
                    showOriginal={false}
                    showConversionInfo={false}
                  />
                </TableCell>
              </TableRow>
              {bookingTotals?.discount && bookingTotals.discount > 0 ? (
                <TableRow>
                  <TableCell className="font-medium">Discount</TableCell>
                  <TableCell className="text-right">
                    -<CurrencyAwarePrice
                      amount={bookingTotals.discount ?? 0}
                      originalCurrency={agencyBaseCurrency}
                      showOriginal={false}
                      showConversionInfo={false}
                    />
                  </TableCell>
                </TableRow>
              ) : null}
              <TableRow className="bg-gray-50 font-medium">
                <TableCell>Total</TableCell>
                <TableCell className="text-right">
                  <CurrencyAwarePrice
                    amount={bookingTotals.total ?? 0}
                    originalCurrency={agencyBaseCurrency}
                    showOriginal={false}
                    showConversionInfo={false}
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Amount Due</TableCell>
                <TableCell className="text-right">
                  <CurrencyAwarePrice
                    amount={bookingTotals.amountDue ?? 0}
                    originalCurrency={agencyBaseCurrency}
                    showOriginal={false}
                    showConversionInfo={false}
                  />
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>

      <CardFooter className="grid gap-4">
        <div>
          {payments.length > 0 && (
            <div className="grid gap-2 overflow-x-auto">
              <h2 className="text-lg font-semibold">Transactions</h2>
              <div className="overflow-hidden rounded-lg border">
                <Table>
                  <TableHeader className="bg-primary text-white">
                    <TableRow>
                      <TableHead className="text-left text-white">
                        Date Processed
                      </TableHead>
                      <TableHead className="text-left text-white">
                        Paid Via
                      </TableHead>
                      <TableHead className="text-right text-white">
                        Total Amount
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {payments.map((payment) => (
                      <TableRow key={payment.total + payment.date}>
                        <TableCell className="capitalize">
                          {payment.date
                            ? formatDateWithoutTime(payment.date)
                            : "-"}
                        </TableCell>
                        <TableCell>{payment.paymentMethod}</TableCell>
                        <TableCell className="text-right">
                          <CurrencyAwarePrice
                            amount={payment.total}
                            originalCurrency={agencyBaseCurrency}
                            showOriginal={false}
                            showConversionInfo={false}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col items-stretch gap-4">
          {booking?.promotion &&
          booking.promotion?.promotionType === "OTHER_AWARD" ? (
            <Alert className="border-purple-200 bg-purple-50 text-purple-800">
              <Info className="h-4 w-4" />
              <AlertDescription className="block">
                Special Offer:{" "}
                <span className="font-semibold">
                  {booking.promotion?.title}
                </span>{" "}
                - To claim your special offer, please contact{" "}
                {booking.promotion?.adminDiscount ? (
                  <span>
                    MyKarLink Support at{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="underline"
                    >
                      <EMAIL>
                    </a>
                  </span>
                ) : (
                  <span>
                    the vehicle provider at{" "}
                    <span className="font-semibold">
                      {booking.vehicle?.agency?.name || "the vehicle provider"}{" "}
                      {booking.vehicle?.agency?.telephone &&
                        `(${booking.vehicle.agency.telephone})`}
                    </span>
                  </span>
                )}
              </AlertDescription>
            </Alert>
          ) : null}
          {vehicle?.excessMileageRate > 0 && (
            <Alert
              variant="destructive"
              className="border-red-200 bg-red-50 text-red-800"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="block">
                Note: Exceeding the daily mileage limit of{" "}
                <span className="font-semibold">
                  {vehicle?.maxDailyMileage}KM per day
                </span>{" "}
                will incur an additional charge of{" "}
                <span className="font-semibold">
                  <CurrencyAwarePrice
                    amount={vehicle?.excessMileageRate || 0}
                    originalCurrency={agencyBaseCurrency}
                    showOriginal={false}
                    showConversionInfo={false}
                  /> per KM
                </span>
                .
              </AlertDescription>
            </Alert>
          )}
          {vehicle?.depositAmt > 0 && (
            <Alert className="border-blue-200 bg-blue-50 text-blue-800">
              <Info className="h-4 w-4" />
              <AlertDescription className="block">
                Deposit:{" "}
                <span className="font-semibold">
                  <CurrencyAwarePrice
                    amount={vehicle?.depositAmt || 0}
                    originalCurrency={agencyBaseCurrency}
                    showOriginal={false}
                    showConversionInfo={false}
                  />
                </span>{" "}
                cash is required for deposit at vehicle pick up/collection, the
                amount to be returned at vehicle drop off after inspection.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}

export default BookingInvoice;

function getBookingStatus(booking: VehicleBooking) {
  if (booking.status === "CANCELLED") {
    return "Cancelled";
  } else if (booking.status === "COMPLETE") {
    return "Completed";
  } else if (booking.status === "WAITINGAUTH") {
    return "In Progress";
  } else if (booking.status === "RESERVED") {
    return "Reserved";
  } else if (booking.status === "BOOKED") {
    return "Booked";
  } else {
    return "Unknown Status";
  }
}
