"use client";
import { useFilters } from "./filter-context";
import { Label } from "@/common/components/ui/label";
import { TabsFilter } from "./tabs-filter";

const TRANSMISSION_OPTIONS = {
  any: null,
  auto: "AUTO",
  manual: "MANUAL",
} as const;

export default function TransmissionSelector() {
  const { filters, setFilters } = useFilters();

  const getCurrentValue = () => {
    switch (filters.transmission) {
      case "AUTO":
        return "auto";
      case "MANUAL":
        return "manual";
      default:
        return "any";
    }
  };

  return (
    <div className="space-y-3">
      <Label>Transmission</Label>
      <TabsFilter
        value={getCurrentValue()}
        onValueChange={(value) => {
          setFilters({
            transmission:
              TRANSMISSION_OPTIONS[value as keyof typeof TRANSMISSION_OPTIONS],
          });
        }}
        options={[
          { value: "any", label: "Any" },
          { value: "auto", label: "Automatic" },
          { value: "manual", label: "Manual" },
        ]}
      />
    </div>
  );
}
