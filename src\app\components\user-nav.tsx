"use client";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/common/components/ui/avatar";
import { Button } from "@/common/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/common/components/ui/dropdown-menu";
import Link from "next/link";
import { useActionState, useEffect } from "react";
import { logoutAction } from "@/app/(auth)/actions";
import { useSession } from "next-auth/react";
import defaultAvatarImg from "@/common/assets/images/default-avatar.svg";
import { usePathname } from "next/navigation";
import Loader from "@/common/components/loader";
import { useProviderRedirect } from "@/common/hooks/use-provider-redirect";
import { useClient } from "@/common/hooks/use-client";

export function UserNav() {
  const session = useSession();
  const { redirectToProviderDashboard } = useProviderRedirect();
  const isAdmin = session?.data?.user?.userType === "AGENCY";
  const isClient = session?.data?.user?.userType === "CLIENT" || isAdmin;
  const { data: client } = useClient(session?.data?.user?.client?.id);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full">
          <Avatar className="h-10 w-10">
            <AvatarImage
              src={client?.logo || defaultAvatarImg}
              alt={`${session?.data?.user?.firstName} ${session?.data?.user?.lastName}'s avatar`}
              className="object-cover"
            />
            <AvatarFallback>
              {getInitals(
                session?.data?.user?.firstName || "",
                session?.data?.user?.lastName || "",
              )}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56 bg-white" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm leading-none font-medium">{`${session?.data?.user?.firstName} ${session?.data?.user?.lastName}`}</p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {isAdmin && (
            <DropdownMenuItem>
              <p
                className="hover:cursor-pointer"
                onClick={() => redirectToProviderDashboard(session?.data)}
              >
                Go To Admin
              </p>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem>
            <Link href="/profile" className="inline-block h-full w-full">
              Profile
            </Link>
          </DropdownMenuItem>
          {isClient && (
            <DropdownMenuItem>
              <Link href="/bookings" className="inline-block h-full w-full">
                Bookings
              </Link>
            </DropdownMenuItem>
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <LogoutButton />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function LogoutButton() {
  const [state, action, isPending] = useActionState(logoutAction, undefined);
  const pathname = usePathname();

  //   TODO: Fix the bug where the update function does not cause the user auth button component to re-render so
  // that the sign out and  my account buttons are hidden then sign and sign out shown.
  useEffect(() => {
    if (state && "success" in state && state.success) {
      if (pathname === "/bookings") {
        window.location.replace("/");
      } else {
        window.location.reload();
      }
    }
  }, [state]);

  return (
    <form action={action} className="p-1">
      <Button className="w-full" variant="destructive" disabled={isPending}>
        {isPending && <Loader />}
        {isPending ? "Signing out..." : "Sign Out"}
      </Button>
    </form>
  );
}

function getInitals(firstName: string, lastName: string) {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`;
}
