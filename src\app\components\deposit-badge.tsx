"use client";

import { usdFormatter, formatUserCurrency } from "@/common/lib/currency-utils";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import { usePriceFormatter } from "@/common/hooks/use-currency-conversion";

export default function DepositBadge({
  depositAmount,
  agencyBaseCurrency = "USD",
}: {
  depositAmount?: number;
  agencyBaseCurrency?: string;
}) {
  const { preferredCurrency } = useUserCurrency();
  const { formatPriceSync } = usePriceFormatter();
  const hasDeposit = depositAmount && depositAmount > 0;

  const formatPrice = (amount: number) => {
    return formatPriceSync(amount, agencyBaseCurrency);
  };

  return (
    <div className="bg-primary/10 flex items-center gap-1 rounded-md pl-2 text-xs">
      <span className="text-primary font-medium">Deposit</span>
      <div className="bg-primary rounded-md px-2 py-0.5">
        <span className="font-semibold text-white">
          {hasDeposit ? formatPrice(depositAmount) : formatPrice(0)}
        </span>
      </div>
    </div>
  );
}
