import { parseAsString, parseAsInteger } from "nuqs";
import { getDefaultDates, formatDate } from "@/common/lib/date-utils";
import type { TransmissionType, SortByType, SortDirectionType } from "@/common/types/vehicle-query";
import type { PromotionType } from "@/common/models"
import { getCurrentBrandConfig } from "./brands/utils";

const { today, tomorrow } = getDefaultDates();

const currentBrand = getCurrentBrandConfig()

export const vehicleQueryConfig = {
    location: {
        defaultValue: null,
        parse: (value: string) => parseInt(value, 10) ?? null,
    },
    start: parseAsString.withDefault(formatDate(today)),
    end: parseAsString.withDefault(formatDate(tomorrow)),
    startTime: parseAsString.withDefault(new Date().toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' })),
    endTime: parseAsString.withDefault(new Date().toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' })),
    vehicleType: parseAsString.withDefault(""),
    page: parseAsInteger.withDefault(0),
    size: parseAsInteger.withDefault(25),
    agencyId: parseAsString.withDefault(currentBrand.agencyId ?? ""),
    lowMileageLimit: {
        defaultValue: null,
        parse: (value: string) => parseInt(value, 10) ?? null,
    },
    maxDeposit: {
        defaultValue: null,
        parse: (value: string) => parseInt(value, 10) ?? null,
    },
    minPrice: {
        defaultValue: null,
        parse: (value: string) => parseInt(value, 10) ?? null,
    },
    maxPrice: {
        defaultValue: null,
        parse: (value: string) => parseInt(value, 10) ?? null,
    },
    transmission: {
        defaultValue: null as TransmissionType,
        parse: (value: string) =>
            (value === "AUTO" || value === "MANUAL" ? value : null) as TransmissionType,
    },
    hasPromotion: {
        defaultValue: null,
        parse: (value: string) => value === "true" ? true : null,
    },
    promotionType: {
        defaultValue: null as PromotionType | null,
        parse: (value: string) => value as PromotionType || null,
    },
    sortBy: {
        defaultValue: "random" as SortByType,
        parse: (value: string) =>
            (value === "random" || value === "rate" || value === "rating" ? value : "random") as SortByType,
    },
    sortDirection: {
        defaultValue: "ASC" as SortDirectionType,
        parse: (value: string) =>
            (value === "ASC" || value === "DESC" ? value : "ASC") as SortDirectionType,
    },
};
