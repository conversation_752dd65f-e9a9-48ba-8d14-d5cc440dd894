"use client";

import useVehicles from "@/common/hooks/use-vehicles";
import VehicleCard from "@/app/components/vehicle-card";
import { useVehicleQuery } from "@/common/hooks/use-vehicle-query";
import VehicleSearchForm from "@/app/components/vehicle-search-form";
import { <PERSON><PERSON> } from "@/common/components/ui/button";
import VehicleCardSkeleton from "@/app/components/vehicle-card-skeleton";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/common/components/ui/alert";
import { AlertCircle, ArrowDownUp, Filter, X } from "lucide-react";
import { useState } from "react";
import { useIsMobile } from "@/common/hooks/use-mobile";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/common/components/ui/sheet";
import { Dialog, DialogTrigger } from "@radix-ui/react-dialog";
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/common/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import MaximumDepositSlider from "@/app/vehicles/search/components/maximum-deposit-slider";
import RatePerDaySlider from "@/app/vehicles/search/components/rate-per-day-slider";
import TransmissionSelector from "@/app/vehicles/search/components/transmission-selector";
import MileageLimitSelector from "@/app/vehicles/search/components/mileage-limit-selector";
import {
  FilterProvider,
  useFilters,
} from "@/app/vehicles/search/components/filter-context";
import PromotionTypeSelector from "@/app/vehicles/search/components/promotion-type-selector";
import { PromotionType } from "@/common/models";
import SearchPagination from "@/app/vehicles/search/components/search-pagination";

// TODO: Implement search without third party libs
// TODO: If above fails,implement search using NUQS and (possibly) Jotai for global state management
export default function CarResultsPage() {
  const { state, queryString } = useVehicleQuery();
  const { isLoading, isError, data } = useVehicles(state);

  return (
    <FilterProvider>
      <div className="container grid gap-6 pt-24 pb-14">
        <VehicleSearchForm variant="compact" />
        <SearchHeading />
        {isLoading && (
          <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 lg:gap-6">
              {Array(3)
                .fill(0)
                .map((_, index) => (
                  <VehicleCardSkeleton key={index} />
                ))}
            </div>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 lg:gap-6">
              {Array(3)
                .fill(0)
                .map((_, index) => (
                  <VehicleCardSkeleton key={index} />
                ))}
            </div>
          </div>
        )}
        {isError && (
          <div className="grid place-content-center">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Search Error</AlertTitle>
              <AlertDescription>
                We couldn&apos;t process your search request. Please try again.
              </AlertDescription>
            </Alert>
          </div>
        )}
        {data?.content.length === 0 && (
          <div className="p-8 text-center">
            <p className="text-lg text-gray-500">No cars available</p>
          </div>
        )}
        {data?.content && (
          <div className="grid gap-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 lg:gap-6">
              {data.content.map((vehicle) => (
                <VehicleCard
                  key={vehicle.id}
                  vehicle={vehicle}
                  queryParams={queryString()}
                />
              ))}
            </div>
            <SearchPagination searchData={data} />
          </div>
        )}
      </div>
    </FilterProvider>
  );
}

function SearchHeading() {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const isMobile = useIsMobile();
  const { applyFilters, resetFilters, clearFilter, appliedFilters } =
    useFilters();
  const { state, updateSortBy, updateSortDirection } = useVehicleQuery();

  const handleApplyFilters = () => {
    applyFilters();
    setIsFilterOpen(false);
  };

  const handleResetFilters = () => {
    resetFilters();
    setIsFilterOpen(false);
  };

  const handleSortChange = (value: string) => {
    switch (value) {
      case "recommended":
        updateSortBy("random");
        updateSortDirection("ASC");
        break;
      case "minDailyRate":
        updateSortBy("minDailyRate");
        updateSortDirection("ASC");
        break;
      case "avgRating":
        updateSortBy("avgRating");
        updateSortDirection("DESC");
        break;
      case "name":
        updateSortBy("name");
        updateSortDirection("ASC");
        break;
      case "model":
        updateSortBy("model");
        updateSortDirection("ASC");
        break;
      default:
        updateSortBy("random");
        updateSortDirection("ASC");
        break;
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-primary text-2xl font-bold">Search Results</h1>

        <div className="flex items-center gap-4">
          <Select value={state.sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-[180px] md:w-auto">
              <ArrowDownUp />
              <span>Sort By: </span>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="random">Recommended</SelectItem>
              <SelectItem value="minDailyRate">
                Daily Rate (lowest first)
              </SelectItem>
              <SelectItem value="avgRating">
                Average Rating (highest first)
              </SelectItem>
              <SelectItem value="name">Make (A-Z)</SelectItem>
              <SelectItem value="model">Model (A-Z)</SelectItem>
            </SelectContent>
          </Select>

          {isMobile ? (
            <Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </SheetTrigger>
              <SheetContent
                side="left"
                className="flex w-screen flex-col p-0 sm:w-[400px]"
              >
                <SheetHeader className="p-4">
                  <SheetTitle>Filters</SheetTitle>
                  <SheetDescription>
                    Refine your vehicle search results
                  </SheetDescription>
                </SheetHeader>
                <div className="overflow-y-scroll px-4">
                  <FilterContent />
                </div>
                <SheetFooter>
                  <Button variant="outline" onClick={handleResetFilters}>
                    Reset
                  </Button>
                  <Button onClick={handleApplyFilters}>Apply Filters</Button>
                </SheetFooter>
              </SheetContent>
            </Sheet>
          ) : (
            <Dialog open={isFilterOpen} onOpenChange={setIsFilterOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </DialogTrigger>
              <DialogContent className="flex flex-col gap-0 p-0 sm:max-h-[min(640px,80vh)] sm:max-w-lg [&>button:last-child]:top-3.5">
                <DialogHeader className="contents space-y-0 text-left">
                  <div className="border-b px-6 py-4 text-base">
                    <DialogTitle>Filters</DialogTitle>
                    <DialogDescription>
                      Refine your vehicle search results
                    </DialogDescription>
                  </div>
                  <div className="overflow-y-auto">
                    <DialogDescription asChild>
                      <div className="px-6 py-4">
                        <FilterContent />
                      </div>
                    </DialogDescription>
                  </div>
                </DialogHeader>
                <DialogFooter className="border-t px-6 py-4 sm:items-center">
                  <DialogClose asChild>
                    <Button type="button" variant="outline">
                      Cancel
                    </Button>
                  </DialogClose>
                  <DialogClose asChild>
                    <Button onClick={handleApplyFilters}>Apply Filters</Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {/* Active filters display */}
      {(appliedFilters.maxDeposit !== null ||
        appliedFilters.minPrice !== null ||
        appliedFilters.maxPrice !== null ||
        appliedFilters.mileageAllowance !== null ||
        appliedFilters.transmission !== null ||
        appliedFilters.promotionType !== null ||
        appliedFilters.hasPromotion !== null) && (
        <div className="bg-muted mt-4 space-y-2 rounded-md p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Active filters:</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs"
              onClick={handleResetFilters}
            >
              Clear All
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {appliedFilters.maxDeposit !== null && (
              <div className="bg-primary/10 text-primary flex items-center rounded-full px-3 py-1 text-xs">
                Max Deposit: ${appliedFilters.maxDeposit}
                <button
                  onClick={() => clearFilter("maxDeposit")}
                  className="hover:bg-primary/20 ml-1.5 rounded-full p-0.5"
                  aria-label="Remove max deposit filter"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </div>
            )}

            {(appliedFilters.minPrice !== null ||
              appliedFilters.maxPrice !== null) && (
              <div className="bg-primary/10 text-primary flex items-center rounded-full px-3 py-1 text-xs">
                Price: ${appliedFilters.minPrice} - ${appliedFilters.maxPrice}
                /day
                <button
                  onClick={() => {
                    clearFilter("minPrice");
                    clearFilter("maxPrice");
                  }}
                  className="hover:bg-primary/20 ml-1.5 rounded-full p-0.5"
                  aria-label="Remove price range filter"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </div>
            )}

            {appliedFilters.mileageAllowance !== null && (
              <div className="bg-primary/10 text-primary flex items-center rounded-full px-3 py-1 text-xs">
                Mileage Allowance:{" "}
                {appliedFilters.mileageAllowance === -1
                  ? "Unlimited"
                  : `${appliedFilters.mileageAllowance}KM`}
                <button
                  onClick={() => clearFilter("mileageAllowance")}
                  className="hover:bg-primary/20 ml-1.5 rounded-full p-0.5"
                  aria-label="Remove mileage allowance filter"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </div>
            )}

            {appliedFilters.transmission !== null && (
              <div className="bg-primary/10 text-primary flex items-center rounded-full px-3 py-1 text-xs">
                Transmission:{" "}
                {appliedFilters.transmission === "AUTO"
                  ? "Automatic"
                  : "Manual"}
                <button
                  onClick={() => clearFilter("transmission")}
                  className="hover:bg-primary/20 ml-1.5 rounded-full p-0.5"
                  aria-label="Remove transmission filter"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </div>
            )}

            {appliedFilters.promotionType !== null && (
              <div className="bg-primary/10 text-primary flex items-center rounded-full px-3 py-1 text-xs">
                Promotion Type:{" "}
                {getPromotionTypeDescription(appliedFilters.promotionType)}
                <button
                  onClick={() => clearFilter("promotionType")}
                  className="hover:bg-primary/20 ml-1.5 rounded-full p-0.5"
                  aria-label="Remove transmission filter"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </div>
            )}

            {/* {appliedFilters.hasPromotion !== null && (
              <div className="flex items-center rounded-full bg-primary/10 px-3 py-1 text-xs text-primary">
                Only on promotion: {appliedFilters.hasPromotion ? "Yes" : "No"}
                <button
                  onClick={() => clearFilter("hasPromotion")}
                  className="ml-1.5 rounded-full p-0.5 hover:bg-primary/20"
                  aria-label="Remove transmission filter"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </div>
            )} */}
          </div>
        </div>
      )}
    </div>
  );
}

function FilterContent() {
  return (
    <div className="space-y-6">
      <MaximumDepositSlider />
      <RatePerDaySlider />
      <TransmissionSelector />
      <MileageLimitSelector />
      <PromotionTypeSelector />
    </div>
  );
}

function getPromotionTypeDescription(promotionType: PromotionType): string {
  switch (promotionType) {
    case "DISCOUNT":
      return "Discount";
    case "EXTRA_MILEAGE":
      return "Extra Mileage";
    case "EXTRA_DAYS":
      return "Extra Days";
    case "OTHER_AWARD":
      return "Special Offer";
    default:
      return "Promotion";
  }
}
