import { useQuery } from "@tanstack/react-query";
import LocationsService from "@/common/services/locations.service";

const service = new LocationsService();
export function useLocations(query: string) {
    return useQuery({
        queryKey: ["locations", query],
        queryFn: () => service.search(query),
        enabled: query.length >= 2,
        staleTime: 1000 * 60 * 30, // 30 minutes (locations change less frequently)
        gcTime: 1000 * 60 * 60, // 1 hour
        refetchOnWindowFocus: false, // Keep this false for search queries
    });
}
