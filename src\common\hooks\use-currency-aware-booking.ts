"use client";

import { useState, useEffect, useMemo } from "react";
import { VehicleBooking } from "@/common/models";
import { getBookingTotals, BookingTotals } from "@/common/lib/booking-utils";
import { useCurrencyConversion } from "./use-currency-conversion";
import { useUserCurrency } from "./use-user-preferences";

export interface CurrencyAwareBookingTotals extends BookingTotals {
  originalCurrency: string;
  displayCurrency: string;
  exchangeRate?: number;
  isConverted: boolean;
  conversionError?: string;
}

interface UseCurrencyAwareBookingOptions {
  booking: VehicleBooking | null | undefined;
  autoConvert?: boolean;
}

/**
 * Hook that provides currency-aware booking totals
 * Converts booking amounts from agency's base currency to user's preferred currency
 */
export function useCurrencyAwareBooking({ 
  booking, 
  autoConvert = true 
}: UseCurrencyAwareBookingOptions) {
  const { preferredCurrency } = useUserCurrency();
  const { convertAmount, isLoading: isConverting, error: conversionError } = useCurrencyConversion();
  
  const [convertedTotals, setConvertedTotals] = useState<CurrencyAwareBookingTotals | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Get original totals and agency's base currency
  const originalTotals = useMemo(() => {
    return booking ? getBookingTotals(booking) : null;
  }, [booking]);

  const agencyBaseCurrency = booking?.vehicle?.agency?.baseCurrency || "USD";
  const needsConversion = agencyBaseCurrency !== preferredCurrency;

  useEffect(() => {
    if (!originalTotals || !autoConvert) {
      setConvertedTotals(null);
      return;
    }

    const convertBookingTotals = async () => {
      if (!needsConversion) {
        // No conversion needed
        setConvertedTotals({
          ...originalTotals,
          originalCurrency: agencyBaseCurrency,
          displayCurrency: agencyBaseCurrency,
          isConverted: false,
        });
        return;
      }

      setIsLoading(true);
      try {
        // Convert all monetary values
        const conversions = await Promise.all([
          originalTotals.subTotal ? convertAmount(originalTotals.subTotal, agencyBaseCurrency) : null,
          originalTotals.total ? convertAmount(originalTotals.total, agencyBaseCurrency) : null,
          originalTotals.discount ? convertAmount(originalTotals.discount, agencyBaseCurrency) : null,
          originalTotals.amountDue ? convertAmount(originalTotals.amountDue, agencyBaseCurrency) : null,
          originalTotals.payableOnline ? convertAmount(originalTotals.payableOnline, agencyBaseCurrency) : null,
          originalTotals.payableAtCollection ? convertAmount(originalTotals.payableAtCollection, agencyBaseCurrency) : null,
          originalTotals.reservationFee ? convertAmount(originalTotals.reservationFee, agencyBaseCurrency) : null,
        ]);

        const [
          subTotalResult,
          totalResult,
          discountResult,
          amountDueResult,
          payableOnlineResult,
          payableAtCollectionResult,
          reservationFeeResult,
        ] = conversions;

        setConvertedTotals({
          subTotal: subTotalResult?.convertedAmount || originalTotals.subTotal,
          total: totalResult?.convertedAmount || originalTotals.total,
          discount: discountResult?.convertedAmount || originalTotals.discount,
          amountDue: amountDueResult?.convertedAmount || originalTotals.amountDue,
          payableOnline: payableOnlineResult?.convertedAmount || originalTotals.payableOnline,
          payableAtCollection: payableAtCollectionResult?.convertedAmount || originalTotals.payableAtCollection,
          reservationFee: reservationFeeResult?.convertedAmount || originalTotals.reservationFee,
          originalCurrency: agencyBaseCurrency,
          displayCurrency: preferredCurrency,
          exchangeRate: subTotalResult?.exchangeRate,
          isConverted: true,
        });
      } catch (error) {
        console.error("Failed to convert booking totals:", error);
        // Fallback to original totals with error indication
        setConvertedTotals({
          ...originalTotals,
          originalCurrency: agencyBaseCurrency,
          displayCurrency: agencyBaseCurrency,
          isConverted: false,
          conversionError: error instanceof Error ? error.message : "Conversion failed",
        });
      } finally {
        setIsLoading(false);
      }
    };

    convertBookingTotals();
  }, [originalTotals, agencyBaseCurrency, preferredCurrency, needsConversion, autoConvert, convertAmount]);

  /**
   * Get booking totals in original currency
   */
  const getOriginalTotals = () => {
    if (!originalTotals) return null;
    
    return {
      ...originalTotals,
      originalCurrency: agencyBaseCurrency,
      displayCurrency: agencyBaseCurrency,
      isConverted: false,
    };
  };

  /**
   * Get booking totals in user's preferred currency (converted if needed)
   */
  const getDisplayTotals = () => {
    return convertedTotals || getOriginalTotals();
  };

  /**
   * Convert a specific amount from agency currency to user currency
   */
  const convertBookingAmount = async (amount: number) => {
    if (!needsConversion) {
      return {
        originalAmount: amount,
        convertedAmount: amount,
        originalCurrency: agencyBaseCurrency,
        targetCurrency: preferredCurrency,
        exchangeRate: 1,
        isConverted: false,
      };
    }

    try {
      const result = await convertAmount(amount, agencyBaseCurrency);
      return {
        ...result,
        isConverted: true,
      };
    } catch (error) {
      return {
        originalAmount: amount,
        convertedAmount: amount,
        originalCurrency: agencyBaseCurrency,
        targetCurrency: agencyBaseCurrency,
        exchangeRate: 1,
        isConverted: false,
        error: error instanceof Error ? error.message : "Conversion failed",
      };
    }
  };

  /**
   * Get currency information for the booking
   */
  const getCurrencyInfo = () => {
    return {
      agencyBaseCurrency,
      userPreferredCurrency: preferredCurrency,
      needsConversion,
      isConverting: isLoading || isConverting,
      conversionError,
    };
  };

  return {
    originalTotals: getOriginalTotals(),
    displayTotals: getDisplayTotals(),
    convertedTotals,
    isLoading: isLoading || isConverting,
    error: conversionError,
    needsConversion,
    agencyBaseCurrency,
    userPreferredCurrency: preferredCurrency,
    convertBookingAmount,
    getCurrencyInfo,
  };
}

/**
 * Simplified hook for getting currency-aware totals
 */
export function useBookingDisplayTotals(booking: VehicleBooking | null | undefined) {
  const { displayTotals, isLoading, error, needsConversion } = useCurrencyAwareBooking({ 
    booking, 
    autoConvert: true 
  });

  return {
    totals: displayTotals,
    isLoading,
    error,
    needsConversion,
  };
}

/**
 * Hook for currency conversion in booking context
 */
export function useBookingCurrencyConversion(booking: VehicleBooking | null | undefined) {
  const { convertBookingAmount, getCurrencyInfo } = useCurrencyAwareBooking({ 
    booking, 
    autoConvert: false 
  });

  return {
    convertAmount: convertBookingAmount,
    currencyInfo: getCurrencyInfo(),
  };
}
