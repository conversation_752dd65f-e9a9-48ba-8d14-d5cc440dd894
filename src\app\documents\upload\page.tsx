"use client";

import DocumentUpload from "@/app/documents/upload/components/document-upload";
import ClientNotFound from "@/app/documents/upload/components/client-not-found";
import ErrorDisplay from "@/app/documents/upload/components/error-display";
import DocumentUploadSkeleton from "@/app/documents/upload/components/document-upload-skeleton";
import { useClient } from "@/common/hooks/use-client";
import { useSearchParams } from "next/navigation";

export default function Home() {
  const params = useSearchParams();

  const {
    data: client,
    isLoading,
    isError,
    error,
  } = useClient(Number(params.get("clientId")));

  // Handle 404 - client not found
  if (
    !params.get("clientId") ||
    (error instanceof Error && error.message === "Client not found")
  ) {
    return <ClientNotFound />;
  }

  // Handle other errors
  if (isError) {
    return <ErrorDisplay error={error as Error} />;
  }

  if (isLoading) {
    return <DocumentUploadSkeleton />;
  }

  return (
    <main className="container pt-28 pb-20">
      <DocumentUpload client={client!} />
    </main>
  );
}
