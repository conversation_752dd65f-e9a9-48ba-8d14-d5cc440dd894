"use client";

import { Checkbox } from "@/common/components/ui/checkbox";
import { useState } from "react";
import { Button } from "@/common/components/ui/button";
import Link from "next/link";

interface Props {
  id: number;
  deposit: number;
}

export default function ProceedForm({ id, deposit }: Props) {
  const [termsAccepted, setTermsAccepted] = useState(false);

  return (
    <div className="flex flex-col items-center gap-4 p-4 lg:p-8">
      <div className="flex gap-2.5">
        <Checkbox
          id="terms"
          name="terms"
          checked={termsAccepted}
          onCheckedChange={(checked) => setTermsAccepted(checked as boolean)}
        />
        <label htmlFor="terms" className="text-sm text-foreground">
          I accept the{" "}
          <a href="#" className="text-blue-500">
            terms and conditions
          </a>{" "}
          in addition to the{" "}
          <span className="text-blue-500">${deposit.toFixed(2)}</span> deposit
          for booking this vehicle
        </label>
      </div>
      <div className="flex w-full items-center">
        <Button
          size="lg"
          className="mx-auto block w-full max-w-xl bg-green-500 text-center font-semibold text-white hover:bg-green-600"
          disabled={!termsAccepted}
        >
          <Link href={`/bookings/${id}/payment`}>Proceed to Payment</Link>
        </Button>
      </div>
    </div>
  );
}
