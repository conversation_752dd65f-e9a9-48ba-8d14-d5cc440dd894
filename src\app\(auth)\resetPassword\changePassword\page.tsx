"use client";

import { useActionState, useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { But<PERSON> } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { useQueryState } from "nuqs";
import { changePasswordAction } from "@/app/(auth)/actions";
import Loader from "@/common/components/loader";

export default function ResetPassword() {
  const [state, action, isPending] = useActionState(
    changePasswordAction,
    undefined,
  );
  const router = useRouter();

  useEffect(() => {
    if (state && "success" in state && state.success && state.redirect) {
      // Store the message in sessionStorage before redirecting
      if (state.message) {
        sessionStorage.setItem("auth_message", state.message);
      }
      router.push(state.redirect);
    }
  }, [state, router]);

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [token] = useQueryState("token");

  const getPasswordError = () => {
    if (password.length === 0) return null;
    if (password.length < 8)
      return "Password must be at least 8 characters long";
    if (!/[A-Z]/.test(password))
      return "Password must include an uppercase letter";
    if (!/[a-z]/.test(password))
      return "Password must include a lowercase letter";
    if (!/\d/.test(password)) return "Password must include a number";
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password))
      return "Password must include a special character";
    return null;
  };

  const passwordError = getPasswordError();
  const passwordsMatch = password === confirmPassword;
  const isValid =
    !passwordError && passwordsMatch && confirmPassword.length > 0;

  return (
    <div className="mx-auto w-full max-w-sm space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Change Password</h1>
        <p className="text-gray-500">Enter your new password below</p>
      </div>
      <form className="grid gap-4" action={action}>
        <input type="hidden" name="token" value={token!} />
        <div className="-mb-2 text-center">
          {/* TODO: Needs improvement */}
          {state && "success" in state && state.success ? (
            <p className="text-sm text-green-500">
              Password changed successfully. You can login now.
            </p>
          ) : (
            <p className="text-sm text-red-500">
              {state && "message" in state && state.message}
            </p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">New Password</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className={`pr-10 ${passwordError ? "border-red-500" : ""}`}
              placeholder="Enter your new password"
              name="password"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-gray-500" />
              )}
              <span className="sr-only">
                {showPassword ? "Hide password" : "Show password"}
              </span>
            </Button>
          </div>
          {passwordError && (
            <p className="text-sm text-red-500">{passwordError}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="confirm">Confirm Password</Label>
          <div className="relative">
            <Input
              id="confirm"
              type={showConfirmPassword ? "text" : "password"}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="pr-10"
              placeholder="Confirm your new password"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-gray-500" />
              )}
              <span className="sr-only">
                {showConfirmPassword ? "Hide password" : "Show password"}
              </span>
            </Button>
          </div>
          {confirmPassword.length > 0 && !passwordsMatch && (
            <p className="text-sm text-red-500">Passwords do not match</p>
          )}
        </div>
        <Button
          className="w-full"
          type="submit"
          disabled={!isValid || isPending}
        >
          {isPending && <Loader />}
          {isPending ? "Resetting..." : "Reset Password"}
        </Button>
      </form>
    </div>
  );
}
