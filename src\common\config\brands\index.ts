import { BrandConfig } from "@/common/config/brands/types";
import karlinkConfig from "@/common/config/brands/karlink";
import murareConfig from "@/common/config/brands/murare";
import khenautoConfig from "@/common/config/brands/khenauto";

// Add all brand configurations here
const brands: Record<string, BrandConfig> = {
    karlink: karlinkConfig,
    murare: murareConfig,
    khenauto: khenautoConfig
};

export default brands;
export type { BrandConfig, Link, SocialLink, ContactInfo, PopularLocation } from "@/common/config/brands/types";
export { DEFAULT_POPULAR_LOCATIONS } from "@/common/config/brands/default-locations";
