"use client";

import type * as React from "react";
import { cn } from "@/common/lib/utils";

interface TabsFilterProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
  onValueChange: (value: string) => void;
  options: {
    value: string;
    label: string;
  }[];
  className?: string;
}

export function TabsFilter({
  value,
  onValueChange,
  options,
  className,
}: TabsFilterProps) {
  return (
    <div className={cn("flex w-full flex-wrap gap-2", className)}>
      {options.map((option) => (
        <button
          key={option.value}
          onClick={() =>
            onValueChange(option.value === value ? "none" : option.value)
          }
          className={cn(
            "min-w-[100px] flex-1 rounded-full px-4 py-2 text-sm transition-all",
            value === option.value
              ? "border-2 border-primary bg-background font-medium text-primary"
              : "border-2 border-transparent bg-muted/50 hover:bg-muted",
          )}
        >
          {option.label}
        </button>
      ))}
    </div>
  );
}
