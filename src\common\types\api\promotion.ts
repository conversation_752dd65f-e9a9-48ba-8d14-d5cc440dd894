import { definitions } from "@/common/types/api/schema";

// Main Promotion Types
export type PromotionDto = definitions["PromotionDto"];

// Promotion Type
export type PromotionType = NonNullable<PromotionDto["promotionType"]>;

// Promotion Status
export type PromotionStatus = NonNullable<PromotionDto["status"]>;

// Paginated Promotion Types
export type PagePromotionDto = definitions["Page«PromotionDto»"];