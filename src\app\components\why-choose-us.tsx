import { TextLogo } from "@/common/components/text-logo";
import { Clock, Car, MapPin, HeartHandshake } from "lucide-react";

const benefits = [
  {
    title: "24/7 Support",
    description:
      "Round-the-clock customer service to assist you anytime, anywhere",
    icon: Clock,
  },
  {
    title: "Extensive Fleet",
    description:
      "Wide selection of vehicles for every need, with no credit card required",
    icon: Car,
  },
  {
    title: "Multiple Locations",
    description:
      "Convenient pickup and drop-off points located near you or your destination",
    icon: MapPin,
  },
  {
    title: "Best Price Guarantee",
    description:
      "Choose from a wide variety of vehicle providers for the best price.",
    icon: HeartHandshake,
  },
];

export default function WhyChooseUs() {
  return (
    <section className="w-full bg-white py-20">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mb-12 flex flex-col items-center justify-center space-y-4 text-center">
          <h2 className="text-3xl font-bold tracking-tighter text-primary sm:text-4xl md:text-5xl">
            Why Book Via <TextLogo />
          </h2>
          <p className="max-w-[700px] text-gray-600 md:text-xl">
            Experience hassle-free car rental with benefits designed for your
            convenience and peace of mind
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {benefits.map((benefit) => (
            <div
              key={benefit.title}
              className="flex flex-col items-center rounded-lg bg-gray-50 p-6 text-center transition-colors hover:bg-gray-100"
            >
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary">
                <benefit.icon className="h-6 w-6 text-white" />
              </div>
              <h3 className="mb-2 text-xl font-semibold text-primary">
                {benefit.title}
              </h3>
              <p className="text-gray-600">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
