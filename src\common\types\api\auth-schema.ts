/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  "/api/v1/user-permission/refresh-token/{token}": {
    post: operations["refreshToken"];
  };
  "/api/v1/user-permission/login": {
    post: operations["login"];
  };
  "/api/v1/user-permission/client-login": {
    post: operations["login_1"];
  };
}

export interface components {
  schemas: {
    ResultDTO: {
      /** @enum {string} */
      userType?: "AGENCY" | "CLIENT" | "WORKER" | "ADMIN";
      /** Format: int64 */
      agentId?: number;
      /** Format: int64 */
      clientId?: number;
      /** Format: int64 */
      workerId?: number;
      roles?: components["schemas"]["Role"][];
      /** Format: int64 */
      id?: number;
      firstName?: string;
      lastName?: string;
      access_token?: string;
      token_type?: string;
      refresh_token?: string;
      scope?: string;
      expires_in?: string;
    };
    Role: {
      /** Format: int64 */
      id?: number;
      name?: string;
      permissions?: components["schemas"]["UserPermission"][];
    };
    UserPermission: {
      /** Format: int64 */
      id?: number;
      authority?: string;
      description?: string;
    };
    UserLogin: {
      username?: string;
      password?: string;
      otp?: string;
    };
    UserLoginRequest: {
      username?: string;
      password?: string;
      clientSecret?: string;
      clientId?: string;
      otp?: string;
    };
  };
}

export interface operations {
  refreshToken: {
    parameters: {
      path: {
        token: string;
      };
    };
    responses: {
      /** default response */
      200: {
        content: {
          "*/*": components["schemas"]["ResultDTO"];
        };
      };
    };
  };
  login: {
    responses: {
      /** default response */
      200: {
        content: {
          "*/*": { [key: string]: unknown };
        };
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["UserLogin"];
      };
    };
  };
  login_1: {
    responses: {
      /** default response */
      200: {
        content: {
          "*/*": { [key: string]: unknown };
        };
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["UserLoginRequest"];
      };
    };
  };
}

export interface external {}
