"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
} from "@/common/components/ui/dialog";
import { But<PERSON> } from "@/common/components/ui/button";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import { ClientDocument } from "@/common/models";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { DialogTitle } from "@radix-ui/react-dialog";
import { getDocumentDisplayName } from "@/common/lib/document-utils";

interface DocumentViewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: ClientDocument;
}

export function ViewDocumentModal({
  open,
  onOpenChange,
  document,
}: DocumentViewDialogProps) {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <VisuallyHidden>
        <DialogTitle>View Document</DialogTitle>
      </VisuallyHidden>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[700px]">
        <DialogHeader className="text-center">
          <h2 className="text-xl font-bold">
            {getDocumentDisplayName(document.name)}
          </h2>
          <p className="text-sm text-gray-500">
            Uploaded on {new Date(document.createdDate).toLocaleDateString()}
          </p>
        </DialogHeader>

        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-75">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            </div>
          )}

          <img
            src={document.url || "/placeholder.svg"}
            alt={getDocumentDisplayName(document.name)}
            className="h-auto max-h-[70vh] w-full rounded-md object-contain"
            onLoad={() => setIsLoading(false)}
            onError={() => setIsLoading(false)}
          />
        </div>

        <div className="mt-4 flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
