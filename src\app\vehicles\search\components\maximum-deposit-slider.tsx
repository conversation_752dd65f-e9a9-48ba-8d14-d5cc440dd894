"use client";
import { useFilters } from "./filter-context";
import { useSliderWithInput } from "@/common/hooks/use-slider-with-input";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { Slider } from "@/common/components/ui/slider";

export default function MaximumDepositSlider() {
  const { filters, setFilters } = useFilters();
  const minValue = 1;
  const maxValue = 1000;
  const defaultValue = [maxValue];
  const initialValue = [filters.maxDeposit ?? defaultValue[0]];

  const {
    sliderValue,
    inputValues,
    validateAndUpdateValue,
    handleInputChange,
    handleSliderChange,
  } = useSliderWithInput({
    minValue,
    maxValue,
    initialValue,
    defaultValue,
  });

  // Update filters when slider or input changes
  const onSliderChange = (newValue: number[]) => {
    handleSliderChange(newValue);
    setFilters({ maxDeposit: newValue[0] });
  };

  const onInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
  ) => {
    handleInputChange(e, index);
  };

  const onInputValidate = (value: string, index: number) => {
    validateAndUpdateValue(value, index);
    setFilters({ maxDeposit: Number(value) });
  };

  return (
    <div className="space-y-3">
      <Label className="text-base font-medium">Maximum Deposit</Label>
      <div className="space-y-3">
        <div>
          <div
            className="mb-1.5 flex w-full items-center justify-between gap-2 text-xs font-medium text-muted-foreground"
            aria-hidden="true"
          >
            <span>${minValue}</span>
            <span>${maxValue}</span>
          </div>
          <Slider
            value={sliderValue}
            defaultValue={defaultValue}
            onValueChange={onSliderChange}
            min={minValue}
            max={maxValue}
            className="py-1"
            aria-label="Maximum deposit amount"
          />
        </div>
        <div className="flex justify-end">
          <div className="relative w-20 overflow-hidden rounded-full border border-gray-200 bg-white">
            <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
              <span className="text-gray-500">$</span>
            </div>
            <Input
              className="h-10 rounded-full border-0 pl-6 pr-3 text-right"
              type="text"
              inputMode="decimal"
              value={inputValues[0]}
              onChange={(e) => onInputChange(e, 0)}
              onBlur={() => onInputValidate(inputValues[0], 0)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  onInputValidate(inputValues[0], 0);
                }
              }}
              aria-label="Enter maximum deposit amount"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
