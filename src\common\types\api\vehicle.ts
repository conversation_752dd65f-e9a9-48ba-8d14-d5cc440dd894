import { definitions } from "@/common/types/api/schema";

// Main Vehicle Types
export type Vehicle = definitions["Vehicle"];
export type VehicleDto = definitions["VehicleDto"];

// Vehicle Status
export type VehicleStatus = NonNullable<Vehicle["status"]>;

// Vehicle Type/Category
export type VehicleType = NonNullable<Vehicle["type"]>;

// Vehicle Fuel Type
export type VehicleFuelType = NonNullable<Vehicle["fuelType"]>;

// Vehicle Transmission Type
export type VehicleTransmissionType = NonNullable<Vehicle["transmissionType"]>;

// Vehicle Availability
export type VehicleAvailability = definitions["VehicleAvailability"];
export type VehicleAvailabilityDto = definitions["VehicleAvailabilityDto"];
export type VehicleAvailabilityUpdateDto =
  definitions["VehicleAvailabilityUpdateDto"];

// Vehicle Booking
export type VehicleBooking = definitions["VehicleBooking"];
export type VehicleBookingDto = definitions["VehicleBookingDto"];
export type VehicleBookingDepositDto = definitions["VehicleBookingDepositDto"];
export type VehicleBookingPhoto = definitions["VehicleBookingPhoto"];

// Vehicle Booking Status
export type VehicleBookingStatus = NonNullable<VehicleBooking["status"]>;

// Vehicle Document
export type VehicleDocument = definitions["VehicleDocument"];

// Vehicle Document Status
export type VehicleDocumentStatus = NonNullable<VehicleDocument["status"]>;

// Vehicle Photo
export type VehiclePhoto = definitions["VehiclePhoto"];
export type VehiclePhotoDto = definitions["VehiclePhotoDto"];

// Vehicle Inventory/Addons
export type VehicleInventory = definitions["VehicleInventory"];

// Vehicle Rate
export type VehicleRate = definitions["VehicleRate"];

// Vehicle Rate Week Day
export type VehicleRateWeekDay = NonNullable<VehicleRate["weekDay"]>;

// Vehicle Log
export type VehicleLogDto = definitions["VehicleLogDto"];

// Vehicle Log Status
export type VehicleLogStatus = NonNullable<VehicleLogDto["status"]>;

// Vehicle Log Type
export type VehicleLogType = NonNullable<VehicleLogDto["type"]>;

// Vehicle Filter
export type VehicleFilterDto = definitions["VehicleFilterDto"];

// Vehicle Filter Operators
export type VehicleFilterOperator = NonNullable<
  VehicleFilterDto["colorOperator"]
>;

// Paginated Vehicle Types
export type PageVehicleDto = definitions["Page«VehicleDto»"];
export type PageVehicleBookingDto = definitions["Page«VehicleBookingDto»"];
export type PageVehicleLogDto = definitions["Page«VehicleLogDto»"];
