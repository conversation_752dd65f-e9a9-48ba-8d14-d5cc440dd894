"use client";

import Link from "next/link";
import { CheckCircle, Search } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/common/components/ui/card";
import { Button } from "@/common/components/ui/button";
import { useParams } from "next/navigation";
import useBooking from "@/common/hooks/use-booking";

export default function RatingSuccess() {
  const params = useParams();
  const { data: booking } = useBooking(Number(params.id));
  const vehicle = booking?.vehicle;

  return (
    <div className="flex min-h-[74vh] items-center justify-center p-4">
      <div className="pt-28 pb-16">
        <Card className="w-full max-w-md text-center">
          <CardHeader className="space-y-2">
            <div className="flex justify-center">
              <CheckCircle className="h-16 w-16 text-green-500" />
            </div>
            <CardTitle className="text-2xl font-bold">Thank You!</CardTitle>
            <CardDescription className="text-lg">
              Your rating for the {vehicle?.name} {vehicle?.model} has been
              successfully submitted.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              We appreciate your feedback. It helps us improve our services and
              assist other customers in making informed decisions.
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Button asChild className="flex-1">
                <Link href="/">
                  <Search className="mr-2 h-4 w-4" />
                  Search More Vehicles
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
