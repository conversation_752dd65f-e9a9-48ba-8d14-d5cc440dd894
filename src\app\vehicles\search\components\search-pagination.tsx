"use client";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";

import { useSearchPagination } from "@/app/vehicles/search/components/use-search-pagination";
import { Button } from "@/common/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
} from "@/common/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import { PaginatedResponse, Vehicle } from "@/common/models";
import { useVehicleQuery } from "@/common/hooks/use-vehicle-query";
import React from "react";

interface SearchPaginationProps {
  searchData: PaginatedResponse<Vehicle> | undefined;
}

export default function SearchPagination({
  searchData,
}: SearchPaginationProps) {
  const { state, updatePage, updateSize } = useVehicleQuery();
  const { page, size } = state;

  // Calculate pagination UI elements
  const { pages, showEllipsis } = useSearchPagination({
    currentPage: page ? page + 1 : 1,
    totalPages: searchData?.totalPages || 1,
  });

  const handlePreviousPage = () => {
    if (page > 0) {
      updatePage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (searchData && page < searchData.totalPages - 1) {
      updatePage(page + 1);
    }
  };

  // If data is still loading
  if (!searchData) {
    return <div className="w-full py-4 text-center">Loading pagination...</div>;
  }

  if (searchData?.content.length === 0) return null;

  const displayPage = page + 1;

  return (
    <div className="w-full space-y-4">
      {/* Responsive pagination container */}
      <div className="flex w-full flex-col gap-4 sm:gap-3 md:flex-row md:items-center md:justify-between">
        {/* Page number information - Full on larger screens, simplified on mobile */}
        <div className="order-2 text-center md:order-1 md:text-left">
          <p className="text-sm text-muted-foreground" aria-live="polite">
            <span className="md:hidden">
              {/* Mobile view - simplified */}
              Page {displayPage}/{searchData.totalPages}
            </span>
            <span className="hidden md:inline">
              {/* Desktop view - detailed */}
              Page{" "}
              <span className="font-medium text-foreground">
                {displayPage}
              </span>{" "}
              of{" "}
              <span className="font-medium text-foreground">
                {searchData.totalPages}
              </span>{" "}
              - Showing{" "}
              <span className="font-medium text-foreground">
                {page * size + 1}-
                {Math.min((page + 1) * size, searchData.totalElements)}
              </span>{" "}
              of{" "}
              <span className="font-medium text-foreground">
                {searchData.totalElements}
              </span>{" "}
              vehicles
            </span>
          </p>
        </div>

        {/* Results per page - Move to bottom on mobile */}
        <div className="order-3 flex justify-center md:order-3 md:justify-end">
          <Select
            value={size.toString()}
            onValueChange={(value) => {
              updateSize(Number(value));
              // Reset to first page when changing page size
              updatePage(0);
            }}
            aria-label="Results per page"
          >
            <SelectTrigger
              id="results-per-page"
              className="w-fit whitespace-nowrap"
            >
              <SelectValue placeholder="Select number of results" />
            </SelectTrigger>
            <SelectContent>
              {[5, 10, 15, 20, 25].map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size} Per Page
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination buttons - Center on mobile */}
        <div className="order-1 flex justify-center md:order-2">
          <Pagination>
            <PaginationContent className="flex flex-wrap justify-center gap-1">
              {/* Previous page button */}
              <PaginationItem>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex h-8 items-center gap-1 px-2 disabled:pointer-events-none disabled:opacity-50 sm:h-9 sm:px-3"
                  onClick={handlePreviousPage}
                  disabled={searchData.first}
                  aria-label="Go to previous page"
                >
                  <ChevronLeftIcon size={16} aria-hidden="true" />
                  <span className="hidden sm:inline">Previous</span>
                </Button>
              </PaginationItem>

              {/* Page buttons with ellipsis - Responsive display */}
              {pages.map((page, index) => {
                const isActive = page === displayPage;

                // On mobile, only show current page and immediate neighbors
                const isMobileVisible =
                  page === 1 ||
                  page === searchData.totalPages ||
                  Math.abs(page - displayPage) <= 1;

                // Add ellipsis if needed (after first page and before last page)
                if (showEllipsis && index > 0 && page > pages[index - 1] + 1) {
                  return (
                    <React.Fragment key={`ellipsis-${index}`}>
                      <PaginationItem
                        className={!isMobileVisible ? "hidden sm:block" : ""}
                      >
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem
                        key={`page-${page}`}
                        className={!isMobileVisible ? "hidden sm:block" : ""}
                      >
                        <Button
                          size="icon"
                          variant={isActive ? "outline" : "ghost"}
                          onClick={() => updatePage(page - 1)}
                          aria-current={isActive ? "page" : undefined}
                          className="h-8 w-8 sm:h-9 sm:w-9"
                        >
                          {page}
                        </Button>
                      </PaginationItem>
                    </React.Fragment>
                  );
                }

                return (
                  <PaginationItem
                    key={`page-${page}`}
                    className={!isMobileVisible ? "hidden sm:block" : ""}
                  >
                    <Button
                      size="icon"
                      variant={isActive ? "outline" : "ghost"}
                      onClick={() => updatePage(page - 1)}
                      aria-current={isActive ? "page" : undefined}
                      className="h-8 w-8 sm:h-9 sm:w-9"
                    >
                      {page}
                    </Button>
                  </PaginationItem>
                );
              })}

              {/* Next page button */}
              <PaginationItem>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex h-8 items-center gap-1 px-2 disabled:pointer-events-none disabled:opacity-50 sm:h-9 sm:px-3"
                  onClick={handleNextPage}
                  disabled={searchData.last}
                  aria-label="Go to next page"
                >
                  <span className="hidden sm:inline">Next</span>
                  <ChevronRightIcon size={16} aria-hidden="true" />
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>

      {/* Mobile-only total items display */}
      <div className="block text-center md:hidden">
        <p className="text-xs text-muted-foreground">
          Showing {page * size + 1}-
          {Math.min((page + 1) * size, searchData.totalElements)} of{" "}
          {searchData.totalElements} vehicles
        </p>
      </div>
    </div>
  );
}
