import VehicleService from "@/common/services/vehicle.service";
import { useQuery } from "@tanstack/react-query";
import { VehicleQueryState } from "../types/vehicle-query";
export default function useVehicles(params: VehicleQueryState) {
    const service = new VehicleService();
    params = {
        ...params,
        promotionType: params.promotionType == "ALL" ? null : params.promotionType,
        start: `${params.start}T${params.startTime}`,
        end: `${params.end}T${params.endTime}`,
    }

    return useQuery({
        queryKey: ["/vehicles/search", params],
        queryFn: () => service.searchVehicles(params),
        staleTime: Infinity,
        gcTime: 1000 * 60 * 2,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
    });
}
